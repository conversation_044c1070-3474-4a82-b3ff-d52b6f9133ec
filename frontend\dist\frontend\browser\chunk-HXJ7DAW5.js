import{a as B}from"./chunk-U2XJBSKQ.js";import{a as R,b as p,c as N,d as T,e as k,f as q,g as G,h as j,i as K}from"./chunk-RKW5GK3Q.js";import{$ as L,B as m,C as v,D as r,E as i,F as x,G as S,H as g,I as l,K as y,S as E,U as J,_ as I,ba as V,ca as O,j as P,s as a,u as _,v as F,w as M,z as d}from"./chunk-3XVLO7MW.js";function $(t,o){t&1&&(r(0,"span"),l(1,"Email is required"),i())}function ee(t,o){t&1&&(r(0,"span"),l(1,"Please enter a valid email"),i())}function te(t,o){if(t&1&&(r(0,"div",14),d(1,$,2,0,"span",11)(2,ee,2,0,"span",11),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.loginForm.get("email"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.loginForm.get("email"))==null||e.errors==null?null:e.errors.email)}}function ne(t,o){t&1&&(r(0,"span"),l(1,"Password is required"),i())}function ie(t,o){t&1&&(r(0,"span"),l(1,"Password must be at least 6 characters"),i())}function re(t,o){if(t&1&&(r(0,"div",14),d(1,ne,2,0,"span",11)(2,ie,2,0,"span",11),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.loginForm.get("password"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.loginForm.get("password"))==null||e.errors==null?null:e.errors.minlength)}}function oe(t,o){if(t&1&&(r(0,"div",15),l(1),i()),t&2){let n=g();a(),y(" ",n.errorMessage," ")}}function ae(t,o){t&1&&(r(0,"span"),l(1,"Logging in..."),i())}function le(t,o){t&1&&(r(0,"span"),l(1,"Login"),i())}var A=class t{constructor(o,n,e){this.fb=o;this.authService=n;this.router=e;this.loginForm=this.fb.group({email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]]})}loginForm;isLoading=!1;errorMessage="";onSubmit(){if(this.loginForm.invalid)return;this.isLoading=!0,this.errorMessage="";let{email:o,password:n}=this.loginForm.value;this.authService.login(o,n).subscribe({next:()=>{this.isLoading=!1,this.router.navigate(["/dashboard"])},error:e=>{this.isLoading=!1,this.errorMessage=e.error?.message||"Login failed. Please try again."}})}static \u0275fac=function(n){return new(n||t)(_(j),_(O),_(I))};static \u0275cmp=F({type:t,selectors:[["app-login"]],standalone:!1,decls:24,vars:11,consts:[[1,"login-container"],[1,"login-card"],[3,"ngSubmit","formGroup"],[1,"form-group"],["for","email"],["type","email","id","email","formControlName","email","placeholder","Enter your email"],["class","error-message",4,"ngIf"],["for","password"],["type","password","id","password","formControlName","password","placeholder","Enter your password"],["class","alert alert-danger",4,"ngIf"],["type","submit",1,"login-button",3,"disabled"],[4,"ngIf"],[1,"register-link"],["routerLink","/auth/register"],[1,"error-message"],[1,"alert","alert-danger"]],template:function(n,e){if(n&1&&(r(0,"div",0)(1,"div",1)(2,"h2"),l(3,"HMS2025 Login"),i(),r(4,"form",2),S("ngSubmit",function(){return e.onSubmit()}),r(5,"div",3)(6,"label",4),l(7,"Email"),i(),x(8,"input",5),d(9,te,3,2,"div",6),i(),r(10,"div",3)(11,"label",7),l(12,"Password"),i(),x(13,"input",8),d(14,re,3,2,"div",6),i(),d(15,oe,2,1,"div",9),r(16,"button",10),d(17,ae,2,0,"span",11)(18,le,2,0,"span",11),i(),r(19,"div",12)(20,"p"),l(21,"Don't have an account? "),r(22,"a",13),l(23,"Register"),i()()()()()()),n&2){let s,u,c,f;a(4),m("formGroup",e.loginForm),a(4),v("invalid",((s=e.loginForm.get("email"))==null?null:s.invalid)&&((s=e.loginForm.get("email"))==null?null:s.touched)),a(),m("ngIf",((u=e.loginForm.get("email"))==null?null:u.invalid)&&((u=e.loginForm.get("email"))==null?null:u.touched)),a(4),v("invalid",((c=e.loginForm.get("password"))==null?null:c.invalid)&&((c=e.loginForm.get("password"))==null?null:c.touched)),a(),m("ngIf",((f=e.loginForm.get("password"))==null?null:f.invalid)&&((f=e.loginForm.get("password"))==null?null:f.touched)),a(),m("ngIf",e.errorMessage),a(),m("disabled",e.loginForm.invalid||e.isLoading),a(),m("ngIf",e.isLoading),a(),m("ngIf",!e.isLoading)}},dependencies:[E,L,k,R,N,T,q,G],styles:[".login-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background-color:#f5f5f5;padding:20px}.login-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 4px 12px #0000001a;padding:30px;width:100%;max-width:400px}h2[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px;color:#333}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}label[_ngcontent-%COMP%]{display:block;margin-bottom:6px;font-weight:500;color:#555}input[_ngcontent-%COMP%]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:4px;font-size:16px;transition:border-color .3s;box-sizing:border-box}input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff}input.invalid[_ngcontent-%COMP%]{border-color:#dc3545}.error-message[_ngcontent-%COMP%]{color:#dc3545;font-size:14px;margin-top:5px}.alert[_ngcontent-%COMP%]{padding:12px;border-radius:4px;margin-bottom:20px}.alert.alert-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.login-button[_ngcontent-%COMP%]{width:100%;padding:12px;background-color:#007bff;color:#fff;border:none;border-radius:4px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .3s}.login-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0069d9}.login-button[_ngcontent-%COMP%]:disabled{background-color:#6c757d;cursor:not-allowed}.register-link[_ngcontent-%COMP%]{text-align:center;margin-top:20px}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:none}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}"]})};function se(t,o){t&1&&(r(0,"span"),l(1,"Name is required"),i())}function me(t,o){t&1&&(r(0,"span"),l(1,"Name must be at least 3 characters"),i())}function de(t,o){if(t&1&&(r(0,"div",18),d(1,se,2,0,"span",15)(2,me,2,0,"span",15),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.registerForm.get("name"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.registerForm.get("name"))==null||e.errors==null?null:e.errors.minlength)}}function pe(t,o){t&1&&(r(0,"span"),l(1,"Email is required"),i())}function ge(t,o){t&1&&(r(0,"span"),l(1,"Please enter a valid email"),i())}function ue(t,o){if(t&1&&(r(0,"div",18),d(1,pe,2,0,"span",15)(2,ge,2,0,"span",15),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.registerForm.get("email"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.registerForm.get("email"))==null||e.errors==null?null:e.errors.email)}}function ce(t,o){t&1&&(r(0,"span"),l(1,"Password is required"),i())}function fe(t,o){t&1&&(r(0,"span"),l(1,"Password must be at least 6 characters"),i())}function _e(t,o){if(t&1&&(r(0,"div",18),d(1,ce,2,0,"span",15)(2,fe,2,0,"span",15),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.registerForm.get("password"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.registerForm.get("password"))==null||e.errors==null?null:e.errors.minlength)}}function ve(t,o){t&1&&(r(0,"span"),l(1,"Please confirm your password"),i())}function xe(t,o){t&1&&(r(0,"span"),l(1,"Passwords do not match"),i())}function be(t,o){if(t&1&&(r(0,"div",18),d(1,ve,2,0,"span",15)(2,xe,2,0,"span",15),i()),t&2){let n,e,s=g();a(),m("ngIf",(n=s.registerForm.get("confirmPassword"))==null||n.errors==null?null:n.errors.required),a(),m("ngIf",(e=s.registerForm.get("confirmPassword"))==null||e.errors==null?null:e.errors.passwordMismatch)}}function he(t,o){if(t&1&&(r(0,"div",19),l(1),i()),t&2){let n=g();a(),y(" ",n.errorMessage," ")}}function Ce(t,o){t&1&&(r(0,"span"),l(1,"Registering..."),i())}function we(t,o){t&1&&(r(0,"span"),l(1,"Register"),i())}var D=class t{constructor(o,n,e){this.fb=o;this.authService=n;this.router=e;this.registerForm=this.fb.group({name:["",[p.required,p.minLength(3)]],email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]],confirmPassword:["",[p.required]]},{validators:this.passwordMatchValidator})}registerForm;isLoading=!1;errorMessage="";passwordMatchValidator(o){let n=o.get("password")?.value,e=o.get("confirmPassword")?.value;return n!==e?(o.get("confirmPassword")?.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):null}onSubmit(){if(this.registerForm.invalid)return;this.isLoading=!0,this.errorMessage="";let{name:o,email:n,password:e}=this.registerForm.value;this.authService.register(o,n,e).subscribe({next:()=>{this.isLoading=!1,this.router.navigate(["/dashboard"])},error:s=>{this.isLoading=!1,this.errorMessage=s.error?.message||"Registration failed. Please try again."}})}static \u0275fac=function(n){return new(n||t)(_(j),_(O),_(I))};static \u0275cmp=F({type:t,selectors:[["app-register"]],standalone:!1,decls:34,vars:17,consts:[[1,"register-container"],[1,"register-card"],[3,"ngSubmit","formGroup"],[1,"form-group"],["for","name"],["type","text","id","name","formControlName","name","placeholder","Enter your full name"],["class","error-message",4,"ngIf"],["for","email"],["type","email","id","email","formControlName","email","placeholder","Enter your email"],["for","password"],["type","password","id","password","formControlName","password","placeholder","Enter your password"],["for","confirmPassword"],["type","password","id","confirmPassword","formControlName","confirmPassword","placeholder","Confirm your password"],["class","alert alert-danger",4,"ngIf"],["type","submit",1,"register-button",3,"disabled"],[4,"ngIf"],[1,"login-link"],["routerLink","/auth/login"],[1,"error-message"],[1,"alert","alert-danger"]],template:function(n,e){if(n&1&&(r(0,"div",0)(1,"div",1)(2,"h2"),l(3,"HMS2025 Registration"),i(),r(4,"form",2),S("ngSubmit",function(){return e.onSubmit()}),r(5,"div",3)(6,"label",4),l(7,"Full Name"),i(),x(8,"input",5),d(9,de,3,2,"div",6),i(),r(10,"div",3)(11,"label",7),l(12,"Email"),i(),x(13,"input",8),d(14,ue,3,2,"div",6),i(),r(15,"div",3)(16,"label",9),l(17,"Password"),i(),x(18,"input",10),d(19,_e,3,2,"div",6),i(),r(20,"div",3)(21,"label",11),l(22,"Confirm Password"),i(),x(23,"input",12),d(24,be,3,2,"div",6),i(),d(25,he,2,1,"div",13),r(26,"button",14),d(27,Ce,2,0,"span",15)(28,we,2,0,"span",15),i(),r(29,"div",16)(30,"p"),l(31,"Already have an account? "),r(32,"a",17),l(33,"Login"),i()()()()()()),n&2){let s,u,c,f,b,h,C,w;a(4),m("formGroup",e.registerForm),a(4),v("invalid",((s=e.registerForm.get("name"))==null?null:s.invalid)&&((s=e.registerForm.get("name"))==null?null:s.touched)),a(),m("ngIf",((u=e.registerForm.get("name"))==null?null:u.invalid)&&((u=e.registerForm.get("name"))==null?null:u.touched)),a(4),v("invalid",((c=e.registerForm.get("email"))==null?null:c.invalid)&&((c=e.registerForm.get("email"))==null?null:c.touched)),a(),m("ngIf",((f=e.registerForm.get("email"))==null?null:f.invalid)&&((f=e.registerForm.get("email"))==null?null:f.touched)),a(4),v("invalid",((b=e.registerForm.get("password"))==null?null:b.invalid)&&((b=e.registerForm.get("password"))==null?null:b.touched)),a(),m("ngIf",((h=e.registerForm.get("password"))==null?null:h.invalid)&&((h=e.registerForm.get("password"))==null?null:h.touched)),a(4),v("invalid",((C=e.registerForm.get("confirmPassword"))==null?null:C.invalid)&&((C=e.registerForm.get("confirmPassword"))==null?null:C.touched)),a(),m("ngIf",((w=e.registerForm.get("confirmPassword"))==null?null:w.invalid)&&((w=e.registerForm.get("confirmPassword"))==null?null:w.touched)),a(),m("ngIf",e.errorMessage),a(),m("disabled",e.registerForm.invalid||e.isLoading),a(),m("ngIf",e.isLoading),a(),m("ngIf",!e.isLoading)}},dependencies:[E,L,k,R,N,T,q,G],styles:[".register-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background-color:#f5f5f5;padding:20px}.register-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 4px 12px #0000001a;padding:30px;width:100%;max-width:500px}h2[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px;color:#333}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}label[_ngcontent-%COMP%]{display:block;margin-bottom:6px;font-weight:500;color:#555}input[_ngcontent-%COMP%]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:4px;font-size:16px;transition:border-color .3s;box-sizing:border-box}input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff}input.invalid[_ngcontent-%COMP%]{border-color:#dc3545}.error-message[_ngcontent-%COMP%]{color:#dc3545;font-size:14px;margin-top:5px}.alert[_ngcontent-%COMP%]{padding:12px;border-radius:4px;margin-bottom:20px}.alert.alert-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.register-button[_ngcontent-%COMP%]{width:100%;padding:12px;background-color:#007bff;color:#fff;border:none;border-radius:4px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .3s}.register-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0069d9}.register-button[_ngcontent-%COMP%]:disabled{background-color:#6c757d;cursor:not-allowed}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:20px}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:none}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}"]})};var Pe=[{path:"login",component:A,canActivate:[B]},{path:"register",component:D,canActivate:[B]},{path:"",redirectTo:"login",pathMatch:"full"}],z=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=M({type:t});static \u0275inj=P({imports:[V.forChild(Pe),V]})};var Q=class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=M({type:t});static \u0275inj=P({imports:[J,z,K]})};export{Q as AuthModule};
