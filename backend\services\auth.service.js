const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const userService = require('./user.service');

class AuthService {
  // Login user
  async login(email, password) {
    try {
      // Find user by email
      const user = await userService.findUserByEmail(email);
      
      // If user not found or password doesn't match
      if (!user || !(await bcrypt.compare(password, user.password))) {
        return null;
      }
      
      // Generate JWT token
      const token = this.generateToken(user);
      
      // Remove password from the returned user object
      const { password: _, ...userWithoutPassword } = user;
      
      return {
        user: userWithoutPassword,
        token
      };
    } catch (error) {
      console.error('Error during login:', error);
      throw error;
    }
  }

  // Register new user
  async register(userData) {
    try {
      // Check if user with this email already exists
      const existingUser = await userService.findUserByEmail(userData.email);
      if (existingUser) {
        return { error: 'User with this email already exists' };
      }
      
      // Create new user
      const newUser = await userService.createUser(userData);
      
      // Generate JWT token
      const token = this.generateToken(newUser);
      
      return {
        user: newUser,
        token
      };
    } catch (error) {
      console.error('Error during registration:', error);
      throw error;
    }
  }

  // Generate JWT token
  generateToken(user) {
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role
    };
    
    return jwt.sign(
      payload,
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );
  }

  // Verify JWT token
  verifyToken(token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (error) {
      console.error('Error verifying token:', error);
      return null;
    }
  }
}

module.exports = new AuthService();
