const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Test server is working!' });
});

// Test login route
app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  const { email, password } = req.body;
  
  if (email === '<EMAIL>' && password === 'admin123') {
    res.json({ 
      token: 'test-token',
      user: { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'admin' }
    });
  } else {
    res.status(401).json({ message: 'Invalid credentials' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Test server is running on port ${PORT}`);
});
