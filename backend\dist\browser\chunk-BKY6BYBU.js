var my=Object.defineProperty,yy=Object.defineProperties;var vy=Object.getOwnPropertyDescriptors;var cd=Object.getOwnPropertySymbols;var Dy=Object.prototype.hasOwnProperty,wy=Object.prototype.propertyIsEnumerable;var ud=(e,t,n)=>t in e?my(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,y=(e,t)=>{for(var n in t||={})Dy.call(t,n)&&ud(e,n,t[n]);if(cd)for(var n of cd(t))wy.call(t,n)&&ud(e,n,t[n]);return e},j=(e,t)=>yy(e,vy(t));var br=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(u){o(u)}},s=c=>{try{a(n.throw(c))}catch(u){o(u)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function Ea(e,t){return Object.is(e,t)}var X=null,ko=!1,Ia=1,pe=Symbol("SIGNAL");function k(e){let t=X;return X=e,t}function Ca(){return X}var En={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Tr(e){if(ko)throw new Error("");if(X===null)return;X.consumerOnSignalRead(e);let t=X.nextProducerIndex++;if(Vo(X),t<X.producerNode.length&&X.producerNode[t]!==e&&Sr(X)){let n=X.producerNode[t];jo(n,X.producerIndexOfThis[t])}X.producerNode[t]!==e&&(X.producerNode[t]=e,X.producerIndexOfThis[t]=Sr(X)?dd(e,X,t):0),X.producerLastReadVersion[t]=e.version}function ld(){Ia++}function ba(e){if(!(Sr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ia)){if(!e.producerMustRecompute(e)&&!Lo(e)){wa(e);return}e.producerRecomputeValue(e),wa(e)}}function Sa(e){if(e.liveConsumerNode===void 0)return;let t=ko;ko=!0;try{for(let n of e.liveConsumerNode)n.dirty||Ey(n)}finally{ko=t}}function Ta(){return X?.consumerAllowSignalWrites!==!1}function Ey(e){e.dirty=!0,Sa(e),e.consumerMarkedDirty?.(e)}function wa(e){e.dirty=!1,e.lastCleanEpoch=Ia}function Mr(e){return e&&(e.nextProducerIndex=0),k(e)}function Fo(e,t){if(k(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Sr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)jo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Lo(e){Vo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ba(n),r!==n.version))return!0}return!1}function _r(e){if(Vo(e),Sr(e))for(let t=0;t<e.producerNode.length;t++)jo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function dd(e,t,n){if(fd(e),e.liveConsumerNode.length===0&&hd(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=dd(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function jo(e,t){if(fd(e),e.liveConsumerNode.length===1&&hd(e))for(let r=0;r<e.producerNode.length;r++)jo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Vo(o),o.producerIndexOfThis[r]=t}}function Sr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Vo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function fd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function hd(e){return e.producerNode!==void 0}function Bo(e,t){let n=Object.create(Iy);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(ba(n),Tr(n),n.value===Po)throw n.error;return n.value};return r[pe]=n,r}var va=Symbol("UNSET"),Da=Symbol("COMPUTING"),Po=Symbol("ERRORED"),Iy=j(y({},En),{value:va,dirty:!0,error:null,equal:Ea,kind:"computed",producerMustRecompute(e){return e.value===va||e.value===Da},producerRecomputeValue(e){if(e.value===Da)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Da;let n=Mr(e),r,o=!1;try{r=e.computation(),k(null),o=t!==va&&t!==Po&&r!==Po&&e.equal(t,r)}catch(i){r=Po,e.error=i}finally{Fo(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Cy(){throw new Error}var pd=Cy;function gd(e){pd(e)}function Ma(e){pd=e}var by=null;function _a(e,t){let n=Object.create(Uo);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Tr(n),n.value);return r[pe]=n,r}function Nr(e,t){Ta()||gd(e),e.equal(e.value,t)||(e.value=t,Sy(e))}function Na(e,t){Ta()||gd(e),Nr(e,t(e.value))}var Uo=j(y({},En),{equal:Ea,value:void 0,kind:"signal"});function Sy(e){e.version++,ld(),Sa(e),by?.()}function Ra(e){let t=k(null);try{return e()}finally{k(t)}}var xa;function Rr(){return xa}function rt(e){let t=xa;return xa=e,t}var $o=Symbol("NotFound");function N(e){return typeof e=="function"}function In(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ho=In(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Gt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Z=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(N(r))try{r()}catch(i){t=i instanceof Ho?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{md(i)}catch(s){t=t??[],s instanceof Ho?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Ho(t)}}add(t){var n;if(t&&t!==this)if(this.closed)md(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Gt(n,t)}remove(t){let{_finalizers:n}=this;n&&Gt(n,t),t instanceof e&&t._removeParent(this)}};Z.EMPTY=(()=>{let e=new Z;return e.closed=!0,e})();var Aa=Z.EMPTY;function zo(e){return e instanceof Z||e&&"closed"in e&&N(e.remove)&&N(e.add)&&N(e.unsubscribe)}function md(e){N(e)?e():e.unsubscribe()}var Ae={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Cn={setTimeout(e,t,...n){let{delegate:r}=Cn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Cn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function qo(e){Cn.setTimeout(()=>{let{onUnhandledError:t}=Ae;if(t)t(e);else throw e})}function xr(){}var yd=Oa("C",void 0,void 0);function vd(e){return Oa("E",void 0,e)}function Dd(e){return Oa("N",e,void 0)}function Oa(e,t,n){return{kind:e,value:t,error:n}}var Wt=null;function bn(e){if(Ae.useDeprecatedSynchronousErrorHandling){let t=!Wt;if(t&&(Wt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Wt;if(Wt=null,n)throw r}}else e()}function wd(e){Ae.useDeprecatedSynchronousErrorHandling&&Wt&&(Wt.errorThrown=!0,Wt.error=e)}var Zt=class extends Z{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,zo(t)&&t.add(this)):this.destination=xy}static create(t,n,r){return new ot(t,n,r)}next(t){this.isStopped?Pa(Dd(t),this):this._next(t)}error(t){this.isStopped?Pa(vd(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Pa(yd,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Ny=Function.prototype.bind;function ka(e,t){return Ny.call(e,t)}var Fa=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Go(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Go(r)}else Go(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Go(n)}}},ot=class extends Zt{constructor(t,n,r){super();let o;if(N(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ae.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&ka(t.next,i),error:t.error&&ka(t.error,i),complete:t.complete&&ka(t.complete,i)}):o=t}this.destination=new Fa(o)}};function Go(e){Ae.useDeprecatedSynchronousErrorHandling?wd(e):qo(e)}function Ry(e){throw e}function Pa(e,t){let{onStoppedNotification:n}=Ae;n&&Cn.setTimeout(()=>n(e,t))}var xy={closed:!0,next:xr,error:Ry,complete:xr};var Sn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function de(e){return e}function La(...e){return ja(e)}function ja(e){return e.length===0?de:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Oy(n)?n:new ot(n,r,o);return bn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ed(r),new r((o,i)=>{let s=new ot({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Sn](){return this}pipe(...n){return ja(n)(this)}toPromise(n){return n=Ed(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ed(e){var t;return(t=e??Ae.Promise)!==null&&t!==void 0?t:Promise}function Ay(e){return e&&N(e.next)&&N(e.error)&&N(e.complete)}function Oy(e){return e&&e instanceof Zt||Ay(e)&&zo(e)}function Va(e){return N(e?.lift)}function T(e){return t=>{if(Va(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function b(e,t,n,r,o){return new Ba(e,t,n,r,o)}var Ba=class extends Zt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Tn(){return T((e,t)=>{let n=null;e._refCount++;let r=b(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Mn=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Va(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Z;let n=this.getSubject();t.add(this.source.subscribe(b(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Z.EMPTY)}return t}refCount(){return Tn()(this)}};var Id=In(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var q=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Wo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Id}next(n){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){bn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Aa:(this.currentObservers=null,i.push(n),new Z(()=>{this.currentObservers=null,Gt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new Wo(t,n),e})(),Wo=class extends q{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Aa}};var ee=class extends q{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ar={now(){return(Ar.delegate||Date).now()},delegate:void 0};var Or=class extends q{constructor(t=1/0,n=1/0,r=Ar){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Zo=class extends Z{constructor(t,n){super()}schedule(t,n=0){return this}};var kr={setInterval(e,t,...n){let{delegate:r}=kr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=kr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var Yo=class extends Zo{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return kr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&kr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Gt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var _n=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};_n.now=Ar.now;var Qo=class extends _n{constructor(t,n=_n.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Pr=new Qo(Yo),Cd=Pr;var ie=new F(e=>e.complete());function Ko(e){return e&&N(e.schedule)}function Ua(e){return e[e.length-1]}function Jo(e){return N(Ua(e))?e.pop():void 0}function Be(e){return Ko(Ua(e))?e.pop():void 0}function bd(e,t){return typeof Ua(e)=="number"?e.pop():t}function Td(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(f){s(f)}}function c(l){try{u(r.throw(l))}catch(f){s(f)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Sd(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Yt(e){return this instanceof Yt?(this.v=e,this):new Yt(e)}function Md(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(p){return Promise.resolve(p).then(d,f)}}function a(d,p){r[d]&&(o[d]=function(m){return new Promise(function(E,S){i.push([d,m,E,S])>1||c(d,m)})},p&&(o[d]=p(o[d])))}function c(d,p){try{u(r[d](p))}catch(m){h(i[0][3],m)}}function u(d){d.value instanceof Yt?Promise.resolve(d.value.v).then(l,f):h(i[0][2],d)}function l(d){c("next",d)}function f(d){c("throw",d)}function h(d,p){d(p),i.shift(),i.length&&c(i[0][0],i[0][1])}}function _d(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Sd=="function"?Sd(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var Xo=e=>e&&typeof e.length=="number"&&typeof e!="function";function ei(e){return N(e?.then)}function ti(e){return N(e[Sn])}function ni(e){return Symbol.asyncIterator&&N(e?.[Symbol.asyncIterator])}function ri(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function ky(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var oi=ky();function ii(e){return N(e?.[oi])}function si(e){return Md(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Yt(n.read());if(o)return yield Yt(void 0);yield yield Yt(r)}}finally{n.releaseLock()}})}function ai(e){return N(e?.getReader)}function B(e){if(e instanceof F)return e;if(e!=null){if(ti(e))return Py(e);if(Xo(e))return Fy(e);if(ei(e))return Ly(e);if(ni(e))return Nd(e);if(ii(e))return jy(e);if(ai(e))return Vy(e)}throw ri(e)}function Py(e){return new F(t=>{let n=e[Sn]();if(N(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Fy(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Ly(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,qo)})}function jy(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Nd(e){return new F(t=>{By(e,t).catch(n=>t.error(n))})}function Vy(e){return Nd(si(e))}function By(e,t){var n,r,o,i;return Td(this,void 0,void 0,function*(){try{for(n=_d(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ge(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function ci(e,t=0){return T((n,r)=>{n.subscribe(b(r,o=>ge(r,e,()=>r.next(o),t),()=>ge(r,e,()=>r.complete(),t),o=>ge(r,e,()=>r.error(o),t)))})}function ui(e,t=0){return T((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Rd(e,t){return B(e).pipe(ui(t),ci(t))}function xd(e,t){return B(e).pipe(ui(t),ci(t))}function Ad(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Od(e,t){return new F(n=>{let r;return ge(n,t,()=>{r=e[oi](),ge(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>N(r?.return)&&r.return()})}function li(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{ge(n,t,()=>{let r=e[Symbol.asyncIterator]();ge(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function kd(e,t){return li(si(e),t)}function Pd(e,t){if(e!=null){if(ti(e))return Rd(e,t);if(Xo(e))return Ad(e,t);if(ei(e))return xd(e,t);if(ni(e))return li(e,t);if(ii(e))return Od(e,t);if(ai(e))return kd(e,t)}throw ri(e)}function $(e,t){return t?Pd(e,t):B(e)}function C(...e){let t=Be(e);return $(e,t)}function Nn(e,t){let n=N(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function $a(e){return!!e&&(e instanceof F||N(e.lift)&&N(e.subscribe))}var it=In(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Fd(e){return e instanceof Date&&!isNaN(e)}function P(e,t){return T((n,r)=>{let o=0;n.subscribe(b(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Uy}=Array;function $y(e,t){return Uy(t)?e(...t):e(t)}function di(e){return P(t=>$y(e,t))}var{isArray:Hy}=Array,{getPrototypeOf:zy,prototype:qy,keys:Gy}=Object;function fi(e){if(e.length===1){let t=e[0];if(Hy(t))return{args:t,keys:null};if(Wy(t)){let n=Gy(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Wy(e){return e&&typeof e=="object"&&zy(e)===qy}function hi(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Fr(...e){let t=Be(e),n=Jo(e),{args:r,keys:o}=fi(e);if(r.length===0)return $([],t);let i=new F(Zy(r,t,o?s=>hi(o,s):de));return n?i.pipe(di(n)):i}function Zy(e,t,n=de){return r=>{Ld(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Ld(t,()=>{let u=$(e[c],t),l=!1;u.subscribe(b(r,f=>{i[c]=f,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Ld(e,t,n){e?ge(n,e,t):t()}function jd(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,f=!1,h=()=>{f&&!c.length&&!u&&t.complete()},d=m=>u<r?p(m):c.push(m),p=m=>{i&&t.next(m),u++;let E=!1;B(n(m,l++)).subscribe(b(t,S=>{o?.(S),i?d(S):t.next(S)},()=>{E=!0},void 0,()=>{if(E)try{for(u--;c.length&&u<r;){let S=c.shift();s?ge(t,s,()=>p(S)):p(S)}h()}catch(S){t.error(S)}}))};return e.subscribe(b(t,d,()=>{f=!0,h()})),()=>{a?.()}}function Q(e,t,n=1/0){return N(t)?Q((r,o)=>P((i,s)=>t(r,i,o,s))(B(e(r,o))),n):(typeof t=="number"&&(n=t),T((r,o)=>jd(r,o,e,n)))}function bt(e=1/0){return Q(de,e)}function Vd(){return bt(1)}function Rn(...e){return Vd()($(e,Be(e)))}function pi(e){return new F(t=>{B(e()).subscribe(t)})}function Yy(...e){let t=Jo(e),{args:n,keys:r}=fi(e),o=new F(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let f=!1;B(n[l]).subscribe(b(i,h=>{f||(f=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!f)&&(u||i.next(r?hi(r,a):a),i.complete())}))}});return t?o.pipe(di(t)):o}function Bd(e=0,t,n=Cd){let r=-1;return t!=null&&(Ko(t)?n=t:r=t),new F(o=>{let i=Fd(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Qy(...e){let t=Be(e),n=bd(e,1/0),r=e;return r.length?r.length===1?B(r[0]):bt(n)($(r,t)):ie}function oe(e,t){return T((n,r)=>{let o=0;n.subscribe(b(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Ud(e){return T((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(b(n,u=>{r=!0,o=u,i||B(e(u)).subscribe(i=b(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Ky(e,t=Pr){return Ud(()=>Bd(e,t))}function st(e){return T((t,n)=>{let r=null,o=!1,i;r=t.subscribe(b(n,void 0,void 0,s=>{i=B(e(s,st(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function $d(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(b(s,l=>{let f=u++;c=a?e(c,l,f):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ue(e,t){return N(t)?Q(e,t,1):Q(e,1)}function Jy(e,t=Pr){return T((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function c(){let u=s+e,l=t.now();if(l<u){o=this.schedule(void 0,u-l),r.add(o);return}a()}n.subscribe(b(r,u=>{i=u,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function St(e){return T((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function at(e){return e<=0?()=>ie:T((t,n)=>{let r=0;t.subscribe(b(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Xy(e,t=de){return e=e??ev,T((n,r)=>{let o,i=!0;n.subscribe(b(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function ev(e,t){return e===t}function gi(e=tv){return T((t,n)=>{let r=!1;t.subscribe(b(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function tv(){return new it}function Tt(e){return T((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function ct(e,t){let n=arguments.length>=2;return r=>r.pipe(e?oe((o,i)=>e(o,i,r)):de,at(1),n?St(t):gi(()=>new it))}function xn(e){return e<=0?()=>ie:T((t,n)=>{let r=[];t.subscribe(b(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ha(e,t){let n=arguments.length>=2;return r=>r.pipe(e?oe((o,i)=>e(o,i,r)):de,xn(1),n?St(t):gi(()=>new it))}function nv(){return T((e,t)=>{let n,r=!1;e.subscribe(b(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function za(e,t){return T($d(e,t,arguments.length>=2,!0))}function Ga(e={}){let{connector:t=()=>new q,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,u=0,l=!1,f=!1,h=()=>{a?.unsubscribe(),a=void 0},d=()=>{h(),s=c=void 0,l=f=!1},p=()=>{let m=s;d(),m?.unsubscribe()};return T((m,E)=>{u++,!f&&!l&&h();let S=c=c??t();E.add(()=>{u--,u===0&&!f&&!l&&(a=qa(p,o))}),S.subscribe(E),!s&&u>0&&(s=new ot({next:J=>S.next(J),error:J=>{f=!0,h(),a=qa(d,n,J),S.error(J)},complete:()=>{l=!0,h(),a=qa(d,r),S.complete()}}),B(m).subscribe(s))})(i)}}function qa(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new ot({next:()=>{r.unsubscribe(),e()}});return B(t(...n)).subscribe(r)}function rv(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,Ga({connector:()=>new Or(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function ov(e){return oe((t,n)=>e<=n)}function Wa(...e){let t=Be(e);return T((n,r)=>{(t?Rn(e,n,t):Rn(e,n)).subscribe(r)})}function me(e,t){return T((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(b(r,c=>{o?.unsubscribe();let u=0,l=i++;B(e(c,l)).subscribe(o=b(r,f=>r.next(t?t(c,f,l,u++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Za(e){return T((t,n)=>{B(e).subscribe(b(n,()=>n.complete(),xr)),!n.closed&&t.subscribe(n)})}function iv(e,t=!1){return T((n,r)=>{let o=0;n.subscribe(b(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Y(e,t,n){let r=N(e)||t||n?{next:e,error:t,complete:n}:e;return r?T((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(b(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):de}var xf="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(pu(t,n)),this.code=t}};function sv(e){return`NG0${Math.abs(e)}`}function pu(e,t){return`${sv(e)}${t?": "+t:""}`}var Af=Symbol("InputSignalNode#UNSET"),av=j(y({},Uo),{transformFn:void 0,applyValueToInputSignal(e,t){Nr(e,t)}});function Of(e,t){let n=Object.create(av);n.value=e,n.transformFn=t?.transform;function r(){if(Tr(n),n.value===Af){let o=null;throw new v(-950,o)}return n.value}return r[pe]=n,r}function Yr(e){return{toString:e}.toString()}var mi="__parameters__";function cv(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function kf(e,t,n){return Yr(()=>{let r=cv(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let f=c.hasOwnProperty(mi)?c[mi]:Object.defineProperty(c,mi,{value:[]})[mi];for(;f.length<=l;)f.push(null);return(f[l]=f[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var jn=globalThis;function U(e){for(let t in e)if(e[t]===U)return t;throw Error("Could not find renamed property on target object.")}function uv(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function De(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(De).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function uc(e,t){return e?t?`${e} ${t}`:e:t||""}var lv=U({__forward_ref__:U});function Pf(e){return e.__forward_ref__=Pf,e.toString=function(){return De(this())},e}function fe(e){return Ff(e)?e():e}function Ff(e){return typeof e=="function"&&e.hasOwnProperty(lv)&&e.__forward_ref__===Pf}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function At(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ji(e){return Hd(e,jf)||Hd(e,Vf)}function Lf(e){return Ji(e)!==null}function Hd(e,t){return e.hasOwnProperty(t)?e[t]:null}function dv(e){let t=e&&(e[jf]||e[Vf]);return t||null}function zd(e){return e&&(e.hasOwnProperty(qd)||e.hasOwnProperty(fv))?e[qd]:null}var jf=U({\u0275prov:U}),qd=U({\u0275inj:U}),Vf=U({ngInjectableDef:U}),fv=U({ngInjectorDef:U}),w=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=D({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Bf(e){return e&&!!e.\u0275providers}var hv=U({\u0275cmp:U}),pv=U({\u0275dir:U}),gv=U({\u0275pipe:U}),mv=U({\u0275mod:U}),Mi=U({\u0275fac:U}),Br=U({__NG_ELEMENT_ID__:U}),Gd=U({__NG_ENV_ID__:U});function Vn(e){return typeof e=="string"?e:e==null?"":String(e)}function yv(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Vn(e)}function Uf(e,t){throw new v(-200,e)}function gu(e,t){throw new v(-201,!1)}var O=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(O||{}),lc;function $f(){return lc}function ye(e){let t=lc;return lc=e,t}function Hf(e,t,n){let r=Ji(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&O.Optional)return null;if(t!==void 0)return t;gu(e,"Injector")}var vv={},Qt=vv,dc="__NG_DI_FLAG__",_i=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?$o:Qt,r)}},Ni="ngTempTokenPath",Dv="ngTokenPath",wv=/\n/gm,Ev="\u0275",Wd="__source";function Iv(e,t=O.Default){if(Rr()===void 0)throw new v(-203,!1);if(Rr()===null)return Hf(e,void 0,t);{let n=Rr(),r;return n instanceof _i?r=n.injector:r=n,r.get(e,t&O.Optional?null:void 0,t)}}function I(e,t=O.Default){return($f()||Iv)(fe(e),t)}function g(e,t=O.Default){return I(e,Xi(t))}function Xi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function fc(e){let t=[];for(let n=0;n<e.length;n++){let r=fe(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=O.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Cv(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function zf(e,t){return e[dc]=t,e.prototype[dc]=t,e}function Cv(e){return e[dc]}function bv(e,t,n,r){let o=e[Ni];throw t[Wd]&&o.unshift(t[Wd]),e.message=Sv(`
`+e.message,o,n,r),e[Dv]=o,e[Ni]=null,e}function Sv(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Ev?e.slice(2):e;let o=De(t);if(Array.isArray(t))o=t.map(De).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):De(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(wv,`
  `)}`}var Tv=zf(kf("Optional"),8);var NO=zf(kf("SkipSelf"),4);function Jt(e,t){let n=e.hasOwnProperty(Mi);return n?e[Mi]:null}function Mv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function _v(e){return e.flat(Number.POSITIVE_INFINITY)}function mu(e,t){e.forEach(n=>Array.isArray(n)?mu(n,t):t(n))}function qf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ri(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Nv(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Rv(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function es(e,t,n){let r=Qr(e,t);return r>=0?e[r|1]=n:(r=~r,Rv(e,r,t,n)),r}function Ya(e,t){let n=Qr(e,t);if(n>=0)return e[n|1]}function Qr(e,t){return xv(e,t,1)}function xv(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Xt={},he=[],Bn=new w(""),Gf=new w("",-1),Wf=new w(""),xi=class{get(t,n=Qt){if(n===Qt){let r=new Error(`NullInjectorError: No provider for ${De(t)}!`);throw r.name="NullInjectorError",r}return n}};function Zf(e,t){let n=e[mv]||null;if(!n&&t===!0)throw new Error(`Type ${De(e)} does not have '\u0275mod' property.`);return n}function Nt(e){return e[hv]||null}function Av(e){return e[pv]||null}function Ov(e){return e[gv]||null}function ln(e){return{\u0275providers:e}}function kv(...e){return{\u0275providers:Yf(!0,e),\u0275fromNgModule:!0}}function Yf(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return mu(t,s=>{let a=s;hc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Qf(o,i),n}function Qf(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];yu(o,i=>{t(i,r)})}}function hc(e,t,n,r){if(e=fe(e),!e)return!1;let o=null,i=zd(e),s=!i&&Nt(e);if(!i&&!s){let c=e.ngModule;if(i=zd(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)hc(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{mu(i.imports,l=>{hc(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Qf(u,t)}if(!a){let u=Jt(o)||(()=>new o);t({provide:o,useFactory:u,deps:he},o),t({provide:Wf,useValue:o,multi:!0},o),t({provide:Bn,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;yu(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function yu(e,t){for(let n of e)Bf(n)&&(n=n.\u0275providers),Array.isArray(n)?yu(n,t):t(n)}var Pv=U({provide:String,useValue:U});function Kf(e){return e!==null&&typeof e=="object"&&Pv in e}function Fv(e){return!!(e&&e.useExisting)}function Lv(e){return!!(e&&e.useFactory)}function Un(e){return typeof e=="function"}function jv(e){return!!e.useClass}var ts=new w(""),Ei={},Zd={},Qa;function ns(){return Qa===void 0&&(Qa=new xi),Qa}var ae=class{},Ur=class extends ae{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,gc(t,s=>this.processProvider(s)),this.records.set(Gf,An(void 0,this)),o.has("environment")&&this.records.set(ae,An(void 0,this));let i=this.records.get(ts);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Wf,he,O.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?$o:Qt,r)}destroy(){jr(this),this._destroyed=!0;let t=k(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),k(t)}}onDestroy(t){return jr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){jr(this);let n=rt(this),r=ye(void 0),o;try{return t()}finally{rt(n),ye(r)}}get(t,n=Qt,r=O.Default){if(jr(this),t.hasOwnProperty(Gd))return t[Gd](this);r=Xi(r);let o,i=rt(this),s=ye(void 0);try{if(!(r&O.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=Hv(t)&&Ji(t);u&&this.injectableDefInScope(u)?c=An(pc(t),Ei):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&O.Self?ns():this.parent;return n=r&O.Optional&&n===Qt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Ni]=a[Ni]||[]).unshift(De(t)),i)throw a;return bv(a,t,"R3InjectorError",this.source)}else throw a}finally{ye(s),rt(i)}}resolveInjectorInitializers(){let t=k(null),n=rt(this),r=ye(void 0),o;try{let i=this.get(Bn,he,O.Self);for(let s of i)s()}finally{rt(n),ye(r),k(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(De(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=fe(t);let n=Un(t)?t:fe(t&&t.provide),r=Bv(t);if(!Un(t)&&t.multi===!0){let o=this.records.get(n);o||(o=An(void 0,Ei,!0),o.factory=()=>fc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=k(null);try{return n.value===Zd?Uf(De(t)):n.value===Ei&&(n.value=Zd,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&$v(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{k(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=fe(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function pc(e){let t=Ji(e),n=t!==null?t.factory:Jt(e);if(n!==null)return n;if(e instanceof w)throw new v(204,!1);if(e instanceof Function)return Vv(e);throw new v(204,!1)}function Vv(e){if(e.length>0)throw new v(204,!1);let n=dv(e);return n!==null?()=>n.factory(e):()=>new e}function Bv(e){if(Kf(e))return An(void 0,e.useValue);{let t=Jf(e);return An(t,Ei)}}function Jf(e,t,n){let r;if(Un(e)){let o=fe(e);return Jt(o)||pc(o)}else if(Kf(e))r=()=>fe(e.useValue);else if(Lv(e))r=()=>e.useFactory(...fc(e.deps||[]));else if(Fv(e))r=(o,i)=>I(fe(e.useExisting),i!==void 0&&i&O.Optional?O.Optional:void 0);else{let o=fe(e&&(e.useClass||e.provide));if(Uv(e))r=()=>new o(...fc(e.deps));else return Jt(o)||pc(o)}return r}function jr(e){if(e.destroyed)throw new v(205,!1)}function An(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Uv(e){return!!e.deps}function $v(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Hv(e){return typeof e=="function"||typeof e=="object"&&e instanceof w}function gc(e,t){for(let n of e)Array.isArray(n)?gc(n,t):n&&Bf(n)?gc(n.\u0275providers,t):t(n)}function Ee(e,t){let n;e instanceof Ur?(jr(e),n=e):n=new _i(e);let r,o=rt(n),i=ye(void 0);try{return t()}finally{rt(o),ye(i)}}function Xf(){return $f()!==void 0||Rr()!=null}function vu(e){if(!Xf())throw new v(-203,!1)}function zv(e){return typeof e=="function"}var ht=0,R=1,M=2,ce=3,Pe=4,Ie=5,$n=6,Ai=7,te=8,Hn=9,ut=10,G=11,$r=12,Yd=13,Jn=14,be=15,en=16,On=17,lt=18,rs=19,eh=20,Mt=21,Ka=22,tn=23,Me=24,Fn=25,ne=26,th=1;var nn=7,Oi=8,zn=9,se=10;function _t(e){return Array.isArray(e)&&typeof e[th]=="object"}function pt(e){return Array.isArray(e)&&e[th]===!0}function Du(e){return(e.flags&4)!==0}function Xn(e){return e.componentOffset>-1}function os(e){return(e.flags&1)===1}function He(e){return!!e.template}function ki(e){return(e[M]&512)!==0}function er(e){return(e[M]&256)===256}var mc=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function nh(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var tr=(()=>{let e=()=>rh;return e.ngInherit=!0,e})();function rh(e){return e.type.prototype.ngOnChanges&&(e.setInput=Gv),qv}function qv(){let e=ih(this),t=e?.current;if(t){let n=e.previous;if(n===Xt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Gv(e,t,n,r,o){let i=this.declaredInputs[r],s=ih(e)||Wv(e,{previous:Xt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new mc(u&&u.currentValue,n,c===Xt),nh(e,t,o,n)}var oh="__ngSimpleChanges__";function ih(e){return e[oh]||null}function Wv(e,t){return e[oh]=t}var Qd=null;var V=function(e,t=null,n){Qd?.(e,t,n)},sh="svg",Zv="math";function ze(e){for(;Array.isArray(e);)e=e[ht];return e}function ah(e,t){return ze(t[e])}function Ze(e,t){return ze(t[e.index])}function wu(e,t){return e.data[t]}function ch(e,t){return e[t]}function uh(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function qe(e,t){let n=t[e];return _t(n)?n:n[ht]}function Yv(e){return(e[M]&4)===4}function Eu(e){return(e[M]&128)===128}function Qv(e){return pt(e[ce])}function Rt(e,t){return t==null?null:e[t]}function lh(e){e[On]=0}function dh(e){e[M]&1024||(e[M]|=1024,Eu(e)&&nr(e))}function Kv(e,t){for(;e>0;)t=t[Jn],e--;return t}function is(e){return!!(e[M]&9216||e[Me]?.dirty)}function yc(e){e[ut].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),is(e)&&nr(e)}function nr(e){e[ut].changeDetectionScheduler?.notify(0);let t=rn(e);for(;t!==null&&!(t[M]&8192||(t[M]|=8192,!Eu(t)));)t=rn(t)}function fh(e,t){if(er(e))throw new v(911,!1);e[Mt]===null&&(e[Mt]=[]),e[Mt].push(t)}function Jv(e,t){if(e[Mt]===null)return;let n=e[Mt].indexOf(t);n!==-1&&e[Mt].splice(n,1)}function rn(e){let t=e[ce];return pt(t)?t[ce]:t}function Iu(e){return e[Ai]??=[]}function Cu(e){return e.cleanup??=[]}function Xv(e,t,n,r){let o=Iu(t);o.push(n),e.firstCreatePass&&Cu(e).push(r,o.length-1)}var x={lFrame:vh(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var vc=!1;function eD(){return x.lFrame.elementDepthCount}function tD(){x.lFrame.elementDepthCount++}function nD(){x.lFrame.elementDepthCount--}function bu(){return x.bindingsEnabled}function hh(){return x.skipHydrationRootTNode!==null}function rD(e){return x.skipHydrationRootTNode===e}function oD(){x.skipHydrationRootTNode=null}function _(){return x.lFrame.lView}function z(){return x.lFrame.tView}function RO(e){return x.lFrame.contextLView=e,e[te]}function xO(e){return x.lFrame.contextLView=null,e}function ue(){let e=ph();for(;e!==null&&e.type===64;)e=e.parent;return e}function ph(){return x.lFrame.currentTNode}function iD(){let e=x.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ot(e,t){let n=x.lFrame;n.currentTNode=e,n.isParent=t}function Su(){return x.lFrame.isParent}function Tu(){x.lFrame.isParent=!1}function sD(){return x.lFrame.contextLView}function gh(){return vc}function Pi(e){let t=vc;return vc=e,t}function aD(){let e=x.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function cD(){return x.lFrame.bindingIndex}function uD(e){return x.lFrame.bindingIndex=e}function dn(){return x.lFrame.bindingIndex++}function Mu(e){let t=x.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function lD(){return x.lFrame.inI18n}function dD(e,t){let n=x.lFrame;n.bindingIndex=n.bindingRootIndex=e,Dc(t)}function fD(){return x.lFrame.currentDirectiveIndex}function Dc(e){x.lFrame.currentDirectiveIndex=e}function hD(e){let t=x.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function _u(){return x.lFrame.currentQueryIndex}function ss(e){x.lFrame.currentQueryIndex=e}function pD(e){let t=e[R];return t.type===2?t.declTNode:t.type===1?e[Ie]:null}function mh(e,t,n){if(n&O.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&O.Host);)if(o=pD(i),o===null||(i=i[Jn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=x.lFrame=yh();return r.currentTNode=t,r.lView=e,!0}function Nu(e){let t=yh(),n=e[R];x.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function yh(){let e=x.lFrame,t=e===null?null:e.child;return t===null?vh(e):t}function vh(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Dh(){let e=x.lFrame;return x.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var wh=Dh;function Ru(){let e=Dh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function gD(e){return(x.lFrame.contextLView=Kv(e,x.lFrame.contextLView))[te]}function gt(){return x.lFrame.selectedIndex}function on(e){x.lFrame.selectedIndex=e}function as(){let e=x.lFrame;return wu(e.tView,e.selectedIndex)}function AO(){x.lFrame.currentNamespace=sh}function OO(){mD()}function mD(){x.lFrame.currentNamespace=null}function yD(){return x.lFrame.currentNamespace}var Eh=!0;function cs(){return Eh}function us(e){Eh=e}function vD(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=rh(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function xu(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Ii(e,t,n){Ih(e,t,3,n)}function Ci(e,t,n,r){(e[M]&3)===n&&Ih(e,t,n,r)}function Ja(e,t){let n=e[M];(n&3)===t&&(n&=16383,n+=1,e[M]=n)}function Ih(e,t,n,r){let o=r!==void 0?e[On]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[On]+=65536),(a<i||i==-1)&&(DD(e,n,t,c),e[On]=(e[On]&**********)+c+2),c++}function Kd(e,t){V(4,e,t);let n=k(null);try{t.call(e)}finally{k(n),V(5,e,t)}}function DD(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[M]>>14<e[On]>>16&&(e[M]&3)===t&&(e[M]+=16384,Kd(a,i)):Kd(a,i)}var Ln=-1,sn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function wD(e){return(e.flags&8)!==0}function ED(e){return(e.flags&16)!==0}function ID(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];CD(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Ch(e){return e===3||e===4||e===6}function CD(e){return e.charCodeAt(0)===64}function qn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Jd(e,n,o,null,t[++r]):Jd(e,n,o,null,null))}}return e}function Jd(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function bh(e){return e!==Ln}function Fi(e){return e&32767}function bD(e){return e>>16}function Li(e,t){let n=bD(e),r=t;for(;n>0;)r=r[Jn],n--;return r}var wc=!0;function ji(e){let t=wc;return wc=e,t}var SD=256,Sh=SD-1,Th=5,TD=0,$e={};function MD(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Br)&&(r=n[Br]),r==null&&(r=n[Br]=TD++);let o=r&Sh,i=1<<o;t.data[e+(o>>Th)]|=i}function Vi(e,t){let n=Mh(e,t);if(n!==-1)return n;let r=t[R];r.firstCreatePass&&(e.injectorIndex=t.length,Xa(r.data,e),Xa(t,null),Xa(r.blueprint,null));let o=Au(e,t),i=e.injectorIndex;if(bh(o)){let s=Fi(o),a=Li(o,t),c=a[R].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Xa(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Mh(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Au(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Ah(o),r===null)return Ln;if(n++,o=o[Jn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Ln}function Ec(e,t,n){MD(e,t,n)}function _D(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Ch(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function _h(e,t,n){if(n&O.Optional||e!==void 0)return e;gu(t,"NodeInjector")}function Nh(e,t,n,r){if(n&O.Optional&&r===void 0&&(r=null),(n&(O.Self|O.Host))===0){let o=e[Hn],i=ye(void 0);try{return o?o.get(t,r,n&O.Optional):Hf(t,r,n&O.Optional)}finally{ye(i)}}return _h(r,t,n)}function Rh(e,t,n,r=O.Default,o){if(e!==null){if(t[M]&2048&&!(r&O.Self)){let s=AD(e,t,n,r,$e);if(s!==$e)return s}let i=xh(e,t,n,r,$e);if(i!==$e)return i}return Nh(t,n,r,o)}function xh(e,t,n,r,o){let i=RD(n);if(typeof i=="function"){if(!mh(t,e,r))return r&O.Host?_h(o,n,r):Nh(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&O.Optional))gu(n);else return s}finally{wh()}}else if(typeof i=="number"){let s=null,a=Mh(e,t),c=Ln,u=r&O.Host?t[be][Ie]:null;for((a===-1||r&O.SkipSelf)&&(c=a===-1?Au(e,t):t[a+8],c===Ln||!ef(r,!1)?a=-1:(s=t[R],a=Fi(c),t=Li(c,t)));a!==-1;){let l=t[R];if(Xd(i,a,l.data)){let f=ND(a,t,n,s,r,u);if(f!==$e)return f}c=t[a+8],c!==Ln&&ef(r,t[R].data[a+8]===u)&&Xd(i,a,t)?(s=l,a=Fi(c),t=Li(c,t)):a=-1}}return o}function ND(e,t,n,r,o,i){let s=t[R],a=s.data[e+8],c=r==null?Xn(a)&&wc:r!=s&&(a.type&3)!==0,u=o&O.Host&&i===a,l=bi(a,s,n,c,u);return l!==null?Hr(t,s,l,a,o):$e}function bi(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,f=r?a:a+l,h=o?a+l:u;for(let d=f;d<h;d++){let p=s[d];if(d<c&&n===p||d>=c&&p.type===n)return d}if(o){let d=s[c];if(d&&He(d)&&d.type===n)return c}return null}function Hr(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof sn){let a=i;a.resolving&&Uf(yv(s[n]));let c=ji(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?ye(a.injectImpl):null,f=mh(e,r,O.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&vD(n,s[n],t)}finally{l!==null&&ye(l),ji(c),a.resolving=!1,wh()}}return i}function RD(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Br)?e[Br]:void 0;return typeof t=="number"?t>=0?t&Sh:xD:t}function Xd(e,t,n){let r=1<<e;return!!(n[t+(e>>Th)]&r)}function ef(e,t){return!(e&O.Self)&&!(e&O.Host&&t)}var Kt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Rh(this._tNode,this._lView,t,Xi(r),n)}};function xD(){return new Kt(ue(),_())}function Ou(e){return Yr(()=>{let t=e.prototype.constructor,n=t[Mi]||Ic(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Mi]||Ic(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ic(e){return Ff(e)?()=>{let t=Ic(fe(e));return t&&t()}:Jt(e)}function AD(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[M]&2048&&!ki(s);){let a=xh(i,s,n,r|O.Self,$e);if(a!==$e)return a;let c=i.parent;if(!c){let u=s[eh];if(u){let l=u.get(n,$e,r);if(l!==$e)return l}c=Ah(s),s=s[Jn]}i=c}return o}function Ah(e){let t=e[R],n=t.type;return n===2?t.declTNode:n===1?e[Ie]:null}function ls(e){return _D(ue(),e)}function tf(e,t=null,n=null,r){let o=Oh(e,t,n,r);return o.resolveInjectorInitializers(),o}function Oh(e,t=null,n=null,r,o=new Set){let i=[n||he,kv(e)];return r=r||(typeof e=="object"?void 0:De(e)),new Ur(i,t||ns(),r||null,o)}var we=class e{static THROW_IF_NOT_FOUND=Qt;static NULL=new xi;static create(t,n){if(Array.isArray(t))return tf({name:""},n,t,"");{let r=t.name??"";return tf({name:r},t.parent,t.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>I(Gf)});static __NG_ELEMENT_ID__=-1};var nf=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>ls(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},OD=new w("");OD.__NG_ELEMENT_ID__=e=>{let t=ue();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&O.Optional)return null;throw new v(204,!1)};var kh=!1,kt=(()=>{class e{static __NG_ELEMENT_ID__=kD;static __NG_ENV_ID__=n=>n}return e})(),Bi=class extends kt{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return er(n)?(t(),()=>{}):(fh(n,t),()=>Jv(n,t))}};function kD(){return new Bi(_())}var an=class{},ds=new w("",{providedIn:"root",factory:()=>!1});var Ph=new w(""),Fh=new w(""),mt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new ee(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();var Cc=class extends q{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Xf()&&(this.destroyRef=g(kt,{optional:!0})??void 0,this.pendingTasks=g(mt,{optional:!0})??void 0)}emit(t){let n=k(null);try{super.next(t)}finally{k(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Z&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ve=Cc;function zr(...e){}function Lh(e){let t,n;function r(){e=zr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function rf(e){return queueMicrotask(()=>e()),()=>{e=zr}}var ku="isAngularZone",Ui=ku+"_ID",PD=0,H=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ve(!1);onMicrotaskEmpty=new ve(!1);onStable=new ve(!1);onError=new ve(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=kh}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,jD(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ku)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,FD,zr,zr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},FD={};function Pu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function LD(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Lh(()=>{e.callbackScheduled=!1,bc(e),e.isCheckStableRunning=!0,Pu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),bc(e)}function jD(e){let t=()=>{LD(e)},n=PD++;e._inner=e._inner.fork({name:"angular",properties:{[ku]:!0,[Ui]:n,[Ui+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(VD(c))return r.invokeTask(i,s,a,c);try{return of(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),sf(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return of(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!BD(c)&&t(),sf(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,bc(e),Pu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function bc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function of(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function sf(e){e._nesting--,Pu(e)}var Sc=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ve;onMicrotaskEmpty=new ve;onStable=new ve;onError=new ve;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function VD(e){return jh(e,"__ignore_ng_zone__")}function BD(e){return jh(e,"__scheduler_tick__")}function jh(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Ge=class{_console=console;handleError(t){this._console.error("ERROR",t)}},UD=new w("",{providedIn:"root",factory:()=>{let e=g(H),t=g(Ge);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function af(e,t){return Of(e,t)}function $D(e){return Of(Af,e)}var Vh=(af.required=$D,af);function HD(){return rr(ue(),_())}function rr(e,t){return new yt(Ze(e,t))}var yt=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=HD}return e})();function Bh(e){return e instanceof yt?e.nativeElement:e}function zD(e){return typeof e=="function"&&e[pe]!==void 0}function qD(e,t){let n=_a(e,t?.equal),r=n[pe];return n.set=o=>Nr(r,o),n.update=o=>Na(r,o),n.asReadonly=GD.bind(n),n}function GD(){let e=this[pe];if(e.readonlyFn===void 0){let t=()=>this();t[pe]=e,e.readonlyFn=t}return e.readonlyFn}function Uh(e){return zD(e)&&typeof e.set=="function"}function WD(){return this._results[Symbol.iterator]()}var Tc=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new q}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=_v(t);(this._changesDetected=!Mv(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=WD};function $h(e){return(e.flags&128)===128}var Hh=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Hh||{}),zh=new Map,ZD=0;function YD(){return ZD++}function QD(e){zh.set(e[rs],e)}function Mc(e){zh.delete(e[rs])}var cf="__ngContext__";function or(e,t){_t(t)?(e[cf]=t[rs],QD(t)):e[cf]=t}function qh(e){return Wh(e[$r])}function Gh(e){return Wh(e[Pe])}function Wh(e){for(;e!==null&&!pt(e);)e=e[Pe];return e}var _c;function Zh(e){_c=e}function KD(){if(_c!==void 0)return _c;if(typeof document<"u")return document;throw new v(210,!1)}var Fu=new w("",{providedIn:"root",factory:()=>JD}),JD="ng",Lu=new w(""),ir=new w("",{providedIn:"platform",factory:()=>"unknown"});var kO=new w(""),ju=new w("",{providedIn:"root",factory:()=>KD().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var XD="h",ew="b";var Yh=!1,tw=new w("",{providedIn:"root",factory:()=>Yh});var Vu=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Vu||{}),sr=new w(""),uf=new Set;function vt(e){uf.has(e)||(uf.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Bu=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=nw}return e})();function nw(){return new Bu(_(),ue())}var kn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(kn||{}),Qh=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),rw=[kn.EarlyRead,kn.Write,kn.MixedReadWrite,kn.Read],ow=(()=>{class e{ngZone=g(H);scheduler=g(an);errorHandler=g(Ge,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){g(sr,{optional:!0})}execute(){let n=this.sequences.size>0;n&&V(16),this.executing=!0;for(let r of rw)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&V(17)}register(n){let{view:r}=n;r!==void 0?((r[Fn]??=[]).push(n),nr(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Vu.AFTER_NEXT_RENDER,n):n()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),Nc=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Fn];t&&(this.view[Fn]=t.filter(n=>n!==this))}};function iw(e,t){!t?.injector&&vu(iw);let n=t?.injector??g(we);return vt("NgAfterRender"),Kh(e,n,t,!1)}function Uu(e,t){!t?.injector&&vu(Uu);let n=t?.injector??g(we);return vt("NgAfterNextRender"),Kh(e,n,t,!0)}function sw(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Kh(e,t,n,r){let o=t.get(Qh);o.impl??=t.get(ow);let i=t.get(sr,null,{optional:!0}),s=n?.phase??kn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(kt):null,c=t.get(Bu,null,{optional:!0}),u=new Nc(o.impl,sw(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var aw=(e,t,n,r)=>{};function cw(e,t,n,r){aw(e,t,n,r)}var uw=()=>null;function Jh(e,t,n=!1){return uw(e,t,n)}function Xh(e,t){let n=e.contentQueries;if(n!==null){let r=k(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];ss(i),a.contentQueries(2,t[s],s)}}}finally{k(r)}}}function Rc(e,t,n){ss(0);let r=k(null);try{t(e,n)}finally{k(r)}}function $u(e,t,n){if(Du(t)){let r=k(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{k(r)}}}var We=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(We||{}),yi;function lw(){if(yi===void 0&&(yi=null,jn.trustedTypes))try{yi=jn.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return yi}function fs(e){return lw()?.createHTML(e)||e}var vi;function dw(){if(vi===void 0&&(vi=null,jn.trustedTypes))try{vi=jn.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return vi}function lf(e){return dw()?.createScriptURL(e)||e}var dt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${xf})`}},xc=class extends dt{getTypeName(){return"HTML"}},Ac=class extends dt{getTypeName(){return"Style"}},Oc=class extends dt{getTypeName(){return"Script"}},kc=class extends dt{getTypeName(){return"URL"}},Pc=class extends dt{getTypeName(){return"ResourceURL"}};function Fe(e){return e instanceof dt?e.changingThisBreaksApplicationSecurity:e}function Pt(e,t){let n=fw(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${xf})`)}return n===t}function fw(e){return e instanceof dt&&e.getTypeName()||null}function ep(e){return new xc(e)}function tp(e){return new Ac(e)}function np(e){return new Oc(e)}function rp(e){return new kc(e)}function op(e){return new Pc(e)}function hw(e){let t=new Lc(e);return pw()?new Fc(t):t}var Fc=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(fs(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},Lc=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=fs(t),n}};function pw(){try{return!!new window.DOMParser().parseFromString(fs(""),"text/html")}catch{return!1}}var gw=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function hs(e){return e=String(e),e.match(gw)?e:"unsafe:"+e}function Dt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Kr(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var ip=Dt("area,br,col,hr,img,wbr"),sp=Dt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),ap=Dt("rp,rt"),mw=Kr(ap,sp),yw=Kr(sp,Dt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),vw=Kr(ap,Dt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),df=Kr(ip,yw,vw,mw),cp=Dt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Dw=Dt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),ww=Dt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Ew=Kr(cp,Dw,ww),Iw=Dt("script,style,template"),jc=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Sw(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=bw(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=ff(t).toLowerCase();if(!df.hasOwnProperty(n))return this.sanitizedSomething=!0,!Iw.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Ew.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;cp[a]&&(c=hs(c)),this.buf.push(" ",s,'="',hf(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=ff(t).toLowerCase();df.hasOwnProperty(n)&&!ip.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(hf(t))}};function Cw(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function bw(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw up(t);return t}function Sw(e){let t=e.firstChild;if(t&&Cw(e,t))throw up(t);return t}function ff(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function up(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Tw=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Mw=/([^\#-~ |!])/g;function hf(e){return e.replace(/&/g,"&amp;").replace(Tw,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Mw,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Di;function lp(e,t){let n=null;try{Di=Di||hw(e);let r=t?String(t):"";n=Di.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Di.getInertBodyElement(r)}while(r!==i);let a=new jc().sanitizeChildren(pf(n)||n);return fs(a)}finally{if(n){let r=pf(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function pf(e){return"content"in e&&_w(e)?e.content:null}function _w(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Ye=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ye||{});function Nw(e){let t=fp();return t?t.sanitize(Ye.URL,e)||"":Pt(e,"URL")?Fe(e):hs(Vn(e))}function Rw(e){let t=fp();if(t)return lf(t.sanitize(Ye.RESOURCE_URL,e)||"");if(Pt(e,"ResourceURL"))return lf(Fe(e));throw new v(904,!1)}function xw(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Rw:Nw}function dp(e,t,n){return xw(t,n)(e)}function fp(){let e=_();return e&&e[ut].sanitizer}var Aw=/^>|^->|<!--|-->|--!>|<!-$/g,Ow=/(<|>)/g,kw="\u200B$1\u200B";function Pw(e){return e.replace(Aw,t=>t.replace(Ow,kw))}function hp(e){return e instanceof Function?e():e}function Fw(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var pp="ng-template";function Lw(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Fw(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Hu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Hu(e){return e.type===4&&e.value!==pp}function jw(e,t,n){let r=e.type===4&&!n?pp:e.value;return t===r}function Vw(e,t,n){let r=4,o=e.attrs,i=o!==null?$w(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Oe(r)&&!Oe(c))return!1;if(s&&Oe(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!jw(e,c,n)||c===""&&t.length===1){if(Oe(r))return!1;s=!0}}else if(r&8){if(o===null||!Lw(e,o,c,n)){if(Oe(r))return!1;s=!0}}else{let u=t[++a],l=Bw(c,o,Hu(e),n);if(l===-1){if(Oe(r))return!1;s=!0;continue}if(u!==""){let f;if(l>i?f="":f=o[l+1].toLowerCase(),r&2&&u!==f){if(Oe(r))return!1;s=!0}}}}return Oe(r)||s}function Oe(e){return(e&1)===0}function Bw(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Hw(t,e)}function gp(e,t,n=!1){for(let r=0;r<t.length;r++)if(Vw(e,t[r],n))return!0;return!1}function Uw(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function $w(e){for(let t=0;t<e.length;t++){let n=e[t];if(Ch(n))return t}return e.length}function Hw(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function zw(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function gf(e,t){return e?":not("+t.trim()+")":t}function qw(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Oe(s)&&(t+=gf(i,o),o=""),r=s,i=i||!Oe(r);n++}return o!==""&&(t+=gf(i,o)),t}function Gw(e){return e.map(qw).join(",")}function Ww(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Oe(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ne={};function Zw(e,t){return e.createText(t)}function Yw(e,t,n){e.setValue(t,n)}function Qw(e,t){return e.createComment(Pw(t))}function mp(e,t,n){return e.createElement(t,n)}function $i(e,t,n,r,o){e.insertBefore(t,n,r,o)}function yp(e,t,n){e.appendChild(t,n)}function mf(e,t,n,r,o){r!==null?$i(e,t,n,r,o):yp(e,t,n)}function Kw(e,t,n){e.removeChild(null,t,n)}function Jw(e,t,n){e.setAttribute(t,"style",n)}function Xw(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function vp(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&ID(e,t,r),o!==null&&Xw(e,t,o),i!==null&&Jw(e,t,i)}function zu(e,t,n,r,o,i,s,a,c,u,l){let f=ne+r,h=f+o,d=eE(f,h),p=typeof u=="function"?u():u;return d[R]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:p,incompleteFirstPass:!1,ssrId:l}}function eE(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ne);return n}function tE(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=zu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function qu(e,t,n,r,o,i,s,a,c,u,l){let f=t.blueprint.slice();return f[ht]=o,f[M]=r|4|128|8|64|1024,(u!==null||e&&e[M]&2048)&&(f[M]|=2048),lh(f),f[ce]=f[Jn]=e,f[te]=n,f[ut]=s||e&&e[ut],f[G]=a||e&&e[G],f[Hn]=c||e&&e[Hn]||null,f[Ie]=i,f[rs]=YD(),f[$n]=l,f[eh]=u,f[be]=t.type==2?e[be]:f,f}function nE(e,t,n){let r=Ze(t,e),o=tE(n),i=e[ut].rendererFactory,s=Gu(e,qu(e,o,null,Dp(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Dp(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function wp(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Gu(e,t){return e[$r]?e[Yd][Pe]=t:e[$r]=t,e[Yd]=t,t}function PO(e=1){Ep(z(),_(),gt()+e,!1)}function Ep(e,t,n,r){if(!r)if((t[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ii(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Ci(t,i,0,n)}on(n)}var ps=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ps||{});function Vc(e,t,n,r){let o=k(null);try{let[i,s,a]=e.inputs[n],c=null;(s&ps.SignalBased)!==0&&(c=t[i][pe]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):nh(t,c,i,r)}finally{k(o)}}function Ip(e,t,n,r,o){let i=gt(),s=r&2;try{on(-1),s&&t.length>ne&&Ep(e,t,ne,!1),V(s?2:0,o),n(r,o)}finally{on(i),V(s?3:1,o)}}function gs(e,t,n){cE(e,t,n),(n.flags&64)===64&&uE(e,t,n)}function Wu(e,t,n=Ze){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function rE(e,t,n,r){let i=r.get(tw,Yh)||n===We.ShadowDom,s=e.selectRootElement(t,i);return oE(s),s}function oE(e){iE(e)}var iE=()=>null;function sE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Zu(e,t,n,r,o,i,s,a){if(!a&&Qu(t,e,n,r,o)){Xn(t)&&aE(n,t.index);return}if(t.type&3){let c=Ze(t,n);r=sE(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function aE(e,t){let n=qe(t,e);n[M]&16||(n[M]|=64)}function cE(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Xn(n)&&nE(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Vi(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Hr(t,e,s,n);if(or(c,t),i!==null&&hE(t,s-r,c,a,n,i),He(a)){let u=qe(n.index,t);u[te]=Hr(t,e,s,n)}}}function uE(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=fD();try{on(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];Dc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&lE(c,u)}}finally{on(-1),Dc(s)}}function lE(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Yu(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];gp(t,i.selectors,!1)&&(r??=[],He(i)?r.unshift(i):r.push(i))}return r}function dE(e,t,n,r,o,i){let s=Ze(e,t);fE(t[G],s,i,e.value,n,r,o)}function fE(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Vn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function hE(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Vc(r,n,c,u)}}function pE(e,t){let n=e[Hn],r=n?n.get(Ge,null):null;r&&r.handleError(t)}function Qu(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],f=t.data[u];Vc(f,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Vc(l,u,r,o),a=!0}return a}function gE(e,t){let n=qe(t,e),r=n[R];mE(r,n);let o=n[ht];o!==null&&n[$n]===null&&(n[$n]=Jh(o,n[Hn])),V(18),Ku(r,n,n[te]),V(19,n[te])}function mE(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Ku(e,t,n){Nu(t);try{let r=e.viewQuery;r!==null&&Rc(1,r,n);let o=e.template;o!==null&&Ip(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[lt]?.finishViewCreation(e),e.staticContentQueries&&Xh(e,t),e.staticViewQueries&&Rc(2,e.viewQuery,n);let i=e.components;i!==null&&yE(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[M]&=-5,Ru()}}function yE(e,t){for(let n=0;n<t.length;n++)gE(e,t[n])}function Jr(e,t,n,r){let o=k(null);try{let i=t.tView,a=e[M]&4096?4096:16,c=qu(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[en]=u;let l=e[lt];return l!==null&&(c[lt]=l.createEmbeddedView(i)),Ku(i,c,n),c}finally{k(o)}}function Gn(e,t){return!t||t.firstChild===null||$h(e)}var vE;function Ju(e,t){return vE(e,t)}var ft=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ft||{});function Xu(e){return(e.flags&32)===32}function Pn(e,t,n,r,o){if(r!=null){let i,s=!1;pt(r)?i=r:_t(r)&&(s=!0,r=r[ht]);let a=ze(r);e===0&&n!==null?o==null?yp(t,n,a):$i(t,n,a,o||null,!0):e===1&&n!==null?$i(t,n,a,o||null,!0):e===2?Kw(t,a,s):e===3&&t.destroyNode(a),i!=null&&_E(t,e,i,n,o)}}function DE(e,t){Cp(e,t),t[ht]=null,t[Ie]=null}function wE(e,t,n,r,o,i){r[ht]=o,r[Ie]=t,vs(e,r,n,1,o,i)}function Cp(e,t){t[ut].changeDetectionScheduler?.notify(9),vs(e,t,t[G],2,null,null)}function EE(e){let t=e[$r];if(!t)return ec(e[R],e);for(;t;){let n=null;if(_t(t))n=t[$r];else{let r=t[se];r&&(n=r)}if(!n){for(;t&&!t[Pe]&&t!==e;)_t(t)&&ec(t[R],t),t=t[ce];t===null&&(t=e),_t(t)&&ec(t[R],t),n=t&&t[Pe]}t=n}}function el(e,t){let n=e[zn],r=n.indexOf(t);n.splice(r,1)}function ms(e,t){if(er(t))return;let n=t[G];n.destroyNode&&vs(e,t,n,3,null,null),EE(t)}function ec(e,t){if(er(t))return;let n=k(null);try{t[M]&=-129,t[M]|=256,t[Me]&&_r(t[Me]),CE(e,t),IE(e,t),t[R].type===1&&t[G].destroy();let r=t[en];if(r!==null&&pt(t[ce])){r!==t[ce]&&el(r,t);let o=t[lt];o!==null&&o.detachView(e)}Mc(t)}finally{k(n)}}function IE(e,t){let n=e.cleanup,r=t[Ai];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Ai]=null);let o=t[Mt];if(o!==null){t[Mt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[tn];if(i!==null){t[tn]=null;for(let s of i)s.destroy()}}function CE(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof sn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];V(4,a,c);try{c.call(a)}finally{V(5,a,c)}}else{V(4,o,i);try{i.call(o)}finally{V(5,o,i)}}}}}function bp(e,t,n){return bE(e,t.parent,n)}function bE(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[ht];if(Xn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===We.None||o===We.Emulated)return null}return Ze(r,n)}function Sp(e,t,n){return TE(e,t,n)}function SE(e,t,n){return e.type&40?Ze(e,n):null}var TE=SE,yf;function ys(e,t,n,r){let o=bp(e,r,t),i=t[G],s=r.parent||t[Ie],a=Sp(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)mf(i,o,n[c],a,!1);else mf(i,o,n,a,!1);yf!==void 0&&yf(i,r,t,n,o)}function Vr(e,t){if(t!==null){let n=t.type;if(n&3)return Ze(t,e);if(n&4)return Bc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Vr(e,r);{let o=e[t.index];return pt(o)?Bc(-1,o):ze(o)}}else{if(n&128)return Vr(e,t.next);if(n&32)return Ju(t,e)()||ze(e[t.index]);{let r=Tp(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=rn(e[be]);return Vr(o,r)}else return Vr(e,t.next)}}}return null}function Tp(e,t){if(t!==null){let r=e[be][Ie],o=t.projection;return r.projection[o]}return null}function Bc(e,t){let n=se+e+1;if(n<t.length){let r=t[n],o=r[R].firstChild;if(o!==null)return Vr(r,o)}return t[nn]}function tl(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&or(ze(a),r),n.flags|=2),!Xu(n))if(c&8)tl(e,t,n.child,r,o,i,!1),Pn(t,e,o,a,i);else if(c&32){let u=Ju(n,r),l;for(;l=u();)Pn(t,e,o,l,i);Pn(t,e,o,a,i)}else c&16?Mp(e,t,r,n,o,i):Pn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function vs(e,t,n,r,o,i){tl(n,r,e.firstChild,t,o,i,!1)}function ME(e,t,n){let r=t[G],o=bp(e,n,t),i=n.parent||t[Ie],s=Sp(i,n,t);Mp(r,0,t,n,o,s)}function Mp(e,t,n,r,o,i){let s=n[be],c=s[Ie].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];Pn(t,e,o,l,i)}else{let u=c,l=s[ce];$h(r)&&(u.flags|=128),tl(e,t,u,l,o,i,!0)}}function _E(e,t,n,r,o){let i=n[nn],s=ze(n);i!==s&&Pn(t,e,r,i,o);for(let a=se;a<n.length;a++){let c=n[a];vs(c[R],c,e,t,r,i)}}function NE(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:ft.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=ft.Important),e.setStyle(n,r,o,i))}}function Hi(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ze(i)),pt(i)&&RE(i,r);let s=n.type;if(s&8)Hi(e,t,n.child,r);else if(s&32){let a=Ju(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Tp(t,n);if(Array.isArray(a))r.push(...a);else{let c=rn(t[be]);Hi(c[R],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function RE(e,t){for(let n=se;n<e.length;n++){let r=e[n],o=r[R].firstChild;o!==null&&Hi(r[R],r,o,t)}e[nn]!==e[ht]&&t.push(e[nn])}function _p(e){if(e[Fn]!==null){for(let t of e[Fn])t.impl.addSequence(t);e[Fn].length=0}}var Np=[];function xE(e){return e[Me]??AE(e)}function AE(e){let t=Np.pop()??Object.create(kE);return t.lView=e,t}function OE(e){e.lView[Me]!==e&&(e.lView=null,Np.push(e))}var kE=j(y({},En),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{nr(e.lView)},consumerOnSignalRead(){this.lView[Me]=this}});function PE(e){let t=e[Me]??Object.create(FE);return t.lView=e,t}var FE=j(y({},En),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=rn(e.lView);for(;t&&!Rp(t[R]);)t=rn(t);t&&dh(t)},consumerOnSignalRead(){this.lView[Me]=this}});function Rp(e){return e.type!==2}function xp(e){if(e[tn]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[tn])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[M]&8192)}}var LE=100;function Ap(e,t=!0,n=0){let o=e[ut].rendererFactory,i=!1;i||o.begin?.();try{jE(e,n)}catch(s){throw t&&pE(e,s),s}finally{i||o.end?.()}}function jE(e,t){let n=gh();try{Pi(!0),Uc(e,t);let r=0;for(;is(e);){if(r===LE)throw new v(103,!1);r++,Uc(e,1)}}finally{Pi(n)}}function VE(e,t,n,r){if(er(t))return;let o=t[M],i=!1,s=!1;Nu(t);let a=!0,c=null,u=null;i||(Rp(e)?(u=xE(t),c=Mr(u)):Ca()===null?(a=!1,u=PE(t),c=Mr(u)):t[Me]&&(_r(t[Me]),t[Me]=null));try{lh(t),uD(e.bindingStartIndex),n!==null&&Ip(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let d=e.preOrderCheckHooks;d!==null&&Ii(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Ci(t,d,0,null),Ja(t,0)}if(s||BE(t),xp(t),Op(t,0),e.contentQueries!==null&&Xh(e,t),!i)if(l){let d=e.contentCheckHooks;d!==null&&Ii(t,d)}else{let d=e.contentHooks;d!==null&&Ci(t,d,1),Ja(t,1)}$E(e,t);let f=e.components;f!==null&&Pp(t,f,0);let h=e.viewQuery;if(h!==null&&Rc(2,h,r),!i)if(l){let d=e.viewCheckHooks;d!==null&&Ii(t,d)}else{let d=e.viewHooks;d!==null&&Ci(t,d,2),Ja(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Ka]){for(let d of t[Ka])d();t[Ka]=null}i||(_p(t),t[M]&=-73)}catch(l){throw i||nr(t),l}finally{u!==null&&(Fo(u,c),a&&OE(u)),Ru()}}function Op(e,t){for(let n=qh(e);n!==null;n=Gh(n))for(let r=se;r<n.length;r++){let o=n[r];kp(o,t)}}function BE(e){for(let t=qh(e);t!==null;t=Gh(t)){if(!(t[M]&2))continue;let n=t[zn];for(let r=0;r<n.length;r++){let o=n[r];dh(o)}}}function UE(e,t,n){V(18);let r=qe(t,e);kp(r,n),V(19,r[te])}function kp(e,t){Eu(e)&&Uc(e,t)}function Uc(e,t){let r=e[R],o=e[M],i=e[Me],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Lo(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)VE(r,e,r.template,e[te]);else if(o&8192){xp(e),Op(e,1);let a=r.components;a!==null&&Pp(e,a,1),_p(e)}}function Pp(e,t,n){for(let r=0;r<t.length;r++)UE(e,t[r],n)}function $E(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)on(~o);else{let i=o,s=n[++r],a=n[++r];dD(s,i);let c=t[i];V(24,c),a(2,c),V(25,c)}}}finally{on(-1)}}function nl(e,t){let n=gh()?64:1088;for(e[ut].changeDetectionScheduler?.notify(t);e;){e[M]|=n;let r=rn(e);if(ki(e)&&!r)return e;e=r}return null}function Fp(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Lp(e,t){let n=se+t;if(n<e.length)return e[n]}function Xr(e,t,n,r=!0){let o=t[R];if(HE(o,t,e,n),r){let s=Bc(n,e),a=t[G],c=a.parentNode(e[nn]);c!==null&&wE(o,e[Ie],a,t,c,s)}let i=t[$n];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function jp(e,t){let n=qr(e,t);return n!==void 0&&ms(n[R],n),n}function qr(e,t){if(e.length<=se)return;let n=se+t,r=e[n];if(r){let o=r[en];o!==null&&o!==e&&el(o,r),t>0&&(e[n-1][Pe]=r[Pe]);let i=Ri(e,se+t);DE(r[R],r);let s=i[lt];s!==null&&s.detachView(i[R]),r[ce]=null,r[Pe]=null,r[M]&=-129}return r}function HE(e,t,n,r){let o=se+r,i=n.length;r>0&&(n[o-1][Pe]=t),r<i-se?(t[Pe]=n[o],qf(n,se+r,t)):(n.push(t),t[Pe]=null),t[ce]=n;let s=t[en];s!==null&&n!==s&&Vp(s,t);let a=t[lt];a!==null&&a.insertView(e),yc(t),t[M]|=128}function Vp(e,t){let n=e[zn],r=t[ce];if(_t(r))e[M]|=2;else{let o=r[ce][be];t[be]!==o&&(e[M]|=2)}n===null?e[zn]=[t]:n.push(t)}var Gr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[R];return Hi(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[te]}set context(t){this._lView[te]=t}get destroyed(){return er(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ce];if(pt(t)){let n=t[Oi],r=n?n.indexOf(this):-1;r>-1&&(qr(t,r),Ri(n,r))}this._attachedToViewContainer=!1}ms(this._lView[R],this._lView)}onDestroy(t){fh(this._lView,t)}markForCheck(){nl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){yc(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,Ap(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=ki(this._lView),n=this._lView[en];n!==null&&!t&&el(n,this._lView),Cp(this._lView[R],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=ki(this._lView),r=this._lView[en];r!==null&&!n&&Vp(r,this._lView),yc(this._lView)}};var Wn=(()=>{class e{static __NG_ELEMENT_ID__=GE}return e})(),zE=Wn,qE=class extends zE{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Jr(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Gr(o)}};function GE(){return Ds(ue(),_())}function Ds(e,t){return e.type&4?new qE(t,e,rr(e,t)):null}function ar(e,t,n,r,o){let i=e.data[t];if(i===null)i=WE(e,t,n,r,o),lD()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=iD();i.injectorIndex=s===null?-1:s.injectorIndex}return Ot(i,!0),i}function WE(e,t,n,r,o){let i=ph(),s=Su(),a=s?i:i&&i.parent,c=e.data[t]=YE(e,a,n,t,r,o);return ZE(e,c,i,s),c}function ZE(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function YE(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return hh()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var jO=new RegExp(`^(\\d+)*(${ew}|${XD})*(.*)`);var QE=()=>null;function Zn(e,t){return QE(e,t)}var KE=class{},Bp=class{},$c=class{resolveComponentFactory(t){throw Error(`No component factory found for ${De(t)}.`)}},ws=class{static NULL=new $c},Yn=class{},eo=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>JE()}return e})();function JE(){let e=_(),t=ue(),n=qe(t.index,e);return(_t(n)?n:e)[G]}var XE=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var tc={},Hc=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Xi(r);let o=this.injector.get(t,tc,r);return o!==tc||n===tc?o:this.parentInjector.get(t,n,r)}};function zc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=uc(o,a);else if(i==2){let c=a,u=t[++s];r=uc(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function le(e,t=O.Default){let n=_();if(n===null)return I(e,t);let r=ue();return Rh(r,n,fe(e),t)}function Up(){let e="invalid";throw new Error(e)}function rl(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=tI(s);l===null?a=s:[a,c,u]=l,oI(e,t,n,a,i,c,u)}i!==null&&r!==null&&eI(n,r,i)}function eI(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function tI(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&He(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,nI(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function nI(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function rI(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function oI(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let d=r[h];!c&&He(d)&&(c=!0,rI(e,n,h)),Ec(Vi(n,t),e,d.type)}lI(n,e.data.length,a);for(let h=0;h<a;h++){let d=r[h];d.providersResolver&&d.providersResolver(d)}let u=!1,l=!1,f=wp(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let d=r[h];if(n.mergedAttrs=qn(n.mergedAttrs,d.hostAttrs),sI(e,n,t,f,d),uI(f,d,o),s!==null&&s.has(d)){let[m,E]=s.get(d);n.directiveToIndex.set(d.type,[f,m+n.directiveStart,E+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let p=d.type.prototype;!u&&(p.ngOnChanges||p.ngOnInit||p.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(p.ngOnChanges||p.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),f++}iI(e,n,i)}function iI(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))vf(0,t,o,r),vf(1,t,o,r),wf(t,r,!1);else{let i=n.get(o);Df(0,t,i,r),Df(1,t,i,r),wf(t,r,!0)}}}function vf(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),$p(t,i)}}function Df(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),$p(t,s)}}function $p(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function wf(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Hu(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function sI(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Jt(o.type,!0)),s=new sn(i,He(o),le);e.blueprint[r]=s,n[r]=s,aI(e,t,r,wp(e,n,o.hostVars,Ne),o)}function aI(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;cI(s)!=a&&s.push(a),s.push(n,r,i)}}function cI(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function uI(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;He(t)&&(n[""]=e)}}function lI(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Hp(e,t,n,r,o,i,s,a){let c=t.consts,u=Rt(c,s),l=ar(t,e,2,r,u);return i&&rl(t,n,l,Rt(c,a),o),l.mergedAttrs=qn(l.mergedAttrs,l.attrs),l.attrs!==null&&zc(l,l.attrs,!1),l.mergedAttrs!==null&&zc(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function zp(e,t){xu(e,t),Du(t)&&e.queries.elementEnd(t)}var zi=class extends ws{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Nt(t);return new cn(n,this.ngModule)}};function dI(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&ps.SignalBased)!==0};return o&&(i.transform=o),i})}function fI(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function hI(e,t,n){let r=t instanceof ae?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Hc(n,r):n}function pI(e){let t=e.get(Yn,null);if(t===null)throw new v(407,!1);let n=e.get(XE,null),r=e.get(an,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function gI(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return mp(t,n,n==="svg"?sh:n==="math"?Zv:null)}var cn=class extends Bp{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=dI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=fI(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Gw(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){V(22);let i=k(null);try{let s=this.componentDef,a=r?["ng-version","19.2.13"]:Ww(this.componentDef.selectors[0]),c=zu(0,null,null,1,0,null,null,null,null,[a],null),u=hI(s,o||this.ngModule,t),l=pI(u),f=l.rendererFactory.createRenderer(null,s),h=r?rE(f,r,s.encapsulation,u):gI(s,f),d=qu(null,c,null,512|Dp(s),null,null,l,f,u,null,Jh(h,u,!0));d[ne]=h,Nu(d);let p=null;try{let m=Hp(ne,c,d,"#host",()=>[this.componentDef],!0,0);h&&(vp(f,h,m),or(h,d)),gs(c,d,m),$u(c,m,d),zp(c,m),n!==void 0&&mI(m,this.ngContentSelectors,n),p=qe(m.index,d),d[te]=p[te],Ku(c,d,null)}catch(m){throw p!==null&&Mc(p),Mc(d),m}finally{V(23),Ru()}return new qc(this.componentType,d)}finally{k(i)}}},qc=class extends KE{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=wu(n[R],ne),this.location=rr(this._tNode,n),this.instance=qe(this._tNode.index,n)[te],this.hostView=this.changeDetectorRef=new Gr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Qu(r,o[R],o,t,n);this.previousInputValues.set(t,n);let s=qe(r.index,o);nl(s,1)}get injector(){return new Kt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function mI(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ft=(()=>{class e{static __NG_ELEMENT_ID__=yI}return e})();function yI(){let e=ue();return Gp(e,_())}var vI=Ft,qp=class extends vI{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return rr(this._hostTNode,this._hostLView)}get injector(){return new Kt(this._hostTNode,this._hostLView)}get parentInjector(){let t=Au(this._hostTNode,this._hostLView);if(bh(t)){let n=Li(t,this._hostLView),r=Fi(t),o=n[R].data[r+8];return new Kt(o,n)}else return new Kt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Ef(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-se}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Zn(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Gn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!zv(t),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let c=s?t:new cn(Nt(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let m=(s?u:this.parentInjector).get(ae,null);m&&(i=m)}let l=Nt(c.componentType??{}),f=Zn(this._lContainer,l?.id??null),h=f?.firstChild??null,d=c.create(u,o,h,i);return this.insertImpl(d.hostView,a,Gn(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Qv(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[ce],u=new qp(c,c[Ie],c[ce]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Xr(s,o,i,r),t.attachToViewContainerRef(),qf(nc(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Ef(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=qr(this._lContainer,n);r&&(Ri(nc(this._lContainer),n),ms(r[R],r))}detach(t){let n=this._adjustIndex(t,-1),r=qr(this._lContainer,n);return r&&Ri(nc(this._lContainer),n)!=null?new Gr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Ef(e){return e[Oi]}function nc(e){return e[Oi]||(e[Oi]=[])}function Gp(e,t){let n,r=t[e.index];return pt(r)?n=r:(n=Fp(r,t,null,e),t[e.index]=n,Gu(t,n)),wI(n,t,e,r),new qp(n,e,t)}function DI(e,t){let n=e[G],r=n.createComment(""),o=Ze(t,e),i=n.parentNode(o);return $i(n,i,r,n.nextSibling(o),!1),r}var wI=CI,EI=()=>!1;function II(e,t,n){return EI(e,t,n)}function CI(e,t,n,r){if(e[nn])return;let o;n.type&8?o=ze(r):o=DI(t,n),e[nn]=o}var Gc=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Wc=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)il(t,n).matches!==null&&this.queries[n].setDirty()}},qi=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=NI(t):this.predicate=t}},Zc=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Yc=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,bI(n,i)),this.matchTNodeWithReadOption(t,n,bi(n,t,i,!1,!1))}else r===Wn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,bi(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===yt||o===Ft||o===Wn&&n.type&4)this.addMatch(n.index,-2);else{let i=bi(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function bI(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function SI(e,t){return e.type&11?rr(e,t):e.type&4?Ds(e,t):null}function TI(e,t,n,r){return n===-1?SI(t,e):n===-2?MI(e,t,r):Hr(e,e[R],n,t)}function MI(e,t,n){if(n===yt)return rr(t,e);if(n===Wn)return Ds(t,e);if(n===Ft)return Gp(t,e)}function Wp(e,t,n,r){let o=t[lt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(TI(t,l,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Qc(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Wp(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=t[-c];for(let f=se;f<l.length;f++){let h=l[f];h[en]===h[ce]&&Qc(h[R],h,u,r)}if(l[zn]!==null){let f=l[zn];for(let h=0;h<f.length;h++){let d=f[h];Qc(d[R],d,u,r)}}}}}return r}function ol(e,t){return e[lt].queries[t].queryList}function Zp(e,t,n){let r=new Tc((n&4)===4);return Xv(e,t,r,r.destroy),(t[lt]??=new Wc).queries.push(new Gc(r))-1}function _I(e,t,n){let r=z();return r.firstCreatePass&&(Qp(r,new qi(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Zp(r,_(),t)}function Yp(e,t,n,r){let o=z();if(o.firstCreatePass){let i=ue();Qp(o,new qi(t,n,r),i.index),RI(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Zp(o,_(),n)}function NI(e){return e.split(",").map(t=>t.trim())}function Qp(e,t,n){e.queries===null&&(e.queries=new Zc),e.queries.track(new Yc(t,n))}function RI(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function il(e,t){return e.queries.getByIndex(t)}function Kp(e,t){let n=e[R],r=il(n,t);return r.crossesNgTemplate?Qc(n,e,t,[]):Wp(n,e,r,t)}function Jp(e,t,n){let r,o=Bo(()=>{r._dirtyCounter();let i=kI(r,e);if(t&&i===void 0)throw new v(-951,!1);return i});return r=o[pe],r._dirtyCounter=qD(0),r._flatValue=void 0,o}function xI(e){return Jp(!0,!1,e)}function AI(e){return Jp(!0,!0,e)}function OI(e,t){let n=e[pe];n._lView=_(),n._queryIndex=t,n._queryList=ol(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function kI(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[M]&4)return t?void 0:he;let o=ol(n,r),i=Kp(n,r);return o.reset(i,Bh),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function If(e,t){return xI(t)}function PI(e,t){return AI(t)}var HO=(If.required=PI,If);var Qn=class{},sl=class{};var Kc=class extends Qn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new zi(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Zf(t);this._bootstrapComponents=hp(i.bootstrap),this._r3Injector=Oh(t,n,[{provide:Qn,useValue:this},{provide:ws,useValue:this.componentFactoryResolver},...r],De(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Jc=class extends sl{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Kc(this.moduleType,t,[])}};var Gi=class extends Qn{injector;componentFactoryResolver=new zi(this);instance=null;constructor(t){super();let n=new Ur([...t.providers,{provide:Qn,useValue:this},{provide:ws,useValue:this.componentFactoryResolver}],t.parent||ns(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function to(e,t,n=null){return new Gi({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var FI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Yf(!1,n.type),o=r.length>0?to([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(I(ae))})}return e})();function Xp(e){return Yr(()=>{let t=tg(e),n=j(y({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Hh.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(FI).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||We.Emulated,styles:e.styles||he,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&vt("NgStandalone"),ng(n);let r=e.dependencies;return n.directiveDefs=Cf(r,!1),n.pipeDefs=Cf(r,!0),n.id=UI(n),n})}function LI(e){return Nt(e)||Av(e)}function jI(e){return e!==null}function Lt(e){return Yr(()=>({type:e.type,bootstrap:e.bootstrap||he,declarations:e.declarations||he,imports:e.imports||he,exports:e.exports||he,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function VI(e,t){if(e==null)return Xt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ps.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function BI(e){if(e==null)return Xt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function jt(e){return Yr(()=>{let t=tg(e);return ng(t),t})}function eg(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function tg(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Xt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||he,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:VI(e.inputs,t),outputs:BI(e.outputs),debugInfo:null}}function ng(e){e.features?.forEach(t=>t(e))}function Cf(e,t){if(!e)return null;let n=t?Ov:LI;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(jI)}function UI(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function $I(e){return Object.getPrototypeOf(e.prototype).constructor}function HI(e){let t=$I(e.type),n=!0,r=[e];for(;t;){let o;if(He(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new v(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=rc(e.inputs),s.declaredInputs=rc(e.declaredInputs),s.outputs=rc(e.outputs);let a=o.hostBindings;a&&ZI(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&GI(e,c),u&&WI(e,u),zI(e,o),uv(e.outputs,o.outputs),He(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===HI&&(n=!1)}}t=Object.getPrototypeOf(t)}qI(r)}function zI(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function qI(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=qn(o.hostAttrs,n=qn(n,o.hostAttrs))}}function rc(e){return e===Xt?{}:e===he?[]:e}function GI(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function WI(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function ZI(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function YI(e,t,n){return e[t]=n}function _e(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function QI(e,t,n,r){let o=_e(e,t,n);return _e(e,t+1,r)||o}function KI(e,t,n,r,o,i,s,a,c){let u=t.consts,l=ar(t,e,4,s||null,a||null);bu()&&rl(t,n,l,Rt(u,c),Yu),l.mergedAttrs=qn(l.mergedAttrs,l.attrs),xu(t,l);let f=l.tView=zu(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),f.queries=t.queries.embeddedTView(l)),l}function Wi(e,t,n,r,o,i,s,a,c,u){let l=n+ne,f=t.firstCreatePass?KI(l,t,e,r,o,i,s,a,c):t.data[l];Ot(f,!1);let h=XI(t,e,f,n);cs()&&ys(t,e,h,f),or(h,e);let d=Fp(h,e,h,f);return e[l]=d,Gu(e,d),II(d,f,e),os(f)&&gs(t,e,f),c!=null&&Wu(e,f,u),f}function JI(e,t,n,r,o,i,s,a){let c=_(),u=z(),l=Rt(u.consts,i);return Wi(c,u,e,t,n,r,o,l,s,a),JI}var XI=eC;function eC(e,t,n,r){return us(!0),t[G].createComment("")}var al=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var rg=new w("");var og=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new Xc})}return e})(),Xc=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function no(e){return!!e&&typeof e.then=="function"}function ig(e){return!!e&&typeof e.subscribe=="function"}var sg=new w("");function cl(e){return ln([{provide:sg,multi:!0,useValue:e}])}var ag=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=g(sg,{optional:!0})??[];injector=g(we);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Ee(this.injector,o);if(no(i))n.push(i);else if(ig(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Es=new w("");function tC(){Ma(()=>{throw new v(600,!1)})}function nC(e){return e.isBoundToModule}var rC=10;var xt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=g(UD);afterRenderManager=g(Qh);zonelessEnabled=g(ds);rootEffectScheduler=g(og);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new q;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=g(mt).hasPendingTasks.pipe(P(n=>!n));constructor(){g(sr,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=g(ae);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=we.NULL){V(10);let i=n instanceof Bp;if(!this._injector.get(ag).done){let d="";throw new v(405,d)}let a;i?a=n:a=this._injector.get(ws).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=nC(a)?void 0:this._injector.get(Qn),u=r||a.selector,l=a.create(o,[],u,c),f=l.location.nativeElement,h=l.injector.get(rg,null);return h?.registerApplication(f),l.onDestroy(()=>{this.detachView(l.hostView),Si(this.components,l),h?.unregisterApplication(f)}),this._loadComponent(l),V(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){V(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Vu.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=k(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,k(n),this.afterTick.next(),V(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Yn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<rC;)V(14),this.synchronizeOnce(),V(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)oC(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>is(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Si(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Es,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Si(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Si(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function oC(e,t,n,r){if(!n&&!is(e))return;Ap(e,t,n&&!r?0:1)}function ul(e,t,n,r){let o=_(),i=dn();if(_e(o,i,t)){let s=z(),a=as();dE(a,o,e,t,n,r)}return ul}function cg(e,t,n,r){return _e(e,dn(),n)?t+Vn(n)+r:Ne}function iC(e,t,n,r,o,i){let s=cD(),a=QI(e,s,n,o);return Mu(2),a?t+Vn(n)+r+Vn(o)+i:Ne}function wi(e,t){return e<<17|t<<2}function un(e){return e>>17&32767}function sC(e){return(e&2)==2}function aC(e,t){return e&131071|t<<17}function eu(e){return e|2}function Kn(e){return(e&131068)>>2}function oc(e,t){return e&-131069|t<<2}function cC(e){return(e&1)===1}function tu(e){return e|1}function uC(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=un(s),c=Kn(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let f=n;l=f[1],(l===null||Qr(f,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=un(e[a+1]);e[r+1]=wi(h,a),h!==0&&(e[h+1]=oc(e[h+1],r)),e[a+1]=aC(e[a+1],r)}else e[r+1]=wi(a,0),a!==0&&(e[a+1]=oc(e[a+1],r)),a=r;else e[r+1]=wi(c,0),a===0?a=r:e[c+1]=oc(e[c+1],r),c=r;u&&(e[r+1]=eu(e[r+1])),bf(e,l,r,!0),bf(e,l,r,!1),lC(t,l,e,r,i),s=wi(a,c),i?t.classBindings=s:t.styleBindings=s}function lC(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Qr(i,t)>=0&&(n[r+1]=tu(n[r+1]))}function bf(e,t,n,r){let o=e[n+1],i=t===null,s=r?un(o):Kn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];dC(c,t)&&(a=!0,e[s+1]=r?tu(u):eu(u)),s=r?un(u):Kn(u)}a&&(e[n+1]=r?eu(o):tu(o))}function dC(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Qr(e,t)>=0:!1}var ke={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function fC(e){return e.substring(ke.key,ke.keyEnd)}function hC(e){return pC(e),ug(e,lg(e,0,ke.textEnd))}function ug(e,t){let n=ke.textEnd;return n===t?-1:(t=ke.keyEnd=gC(e,ke.key=t,n),lg(e,t,n))}function pC(e){ke.key=0,ke.keyEnd=0,ke.value=0,ke.valueEnd=0,ke.textEnd=e.length}function lg(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function gC(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function mC(e,t,n){let r=_(),o=dn();if(_e(r,o,t)){let i=z(),s=as();Zu(i,s,r,e,t,r[G],n,!1)}return mC}function nu(e,t,n,r,o){Qu(t,e,n,o?"class":"style",r)}function yC(e,t,n){return fg(e,t,n,!1),yC}function vC(e,t){return fg(e,t,null,!0),vC}function GO(e){hg(bC,dg,e,!0)}function dg(e,t){for(let n=hC(t);n>=0;n=ug(t,n))es(e,fC(t),!0)}function fg(e,t,n,r){let o=_(),i=z(),s=Mu(2);if(i.firstUpdatePass&&gg(i,e,s,r),t!==Ne&&_e(o,s,t)){let a=i.data[gt()];mg(i,a,o,o[G],e,o[s+1]=TC(t,n),r,s)}}function hg(e,t,n,r){let o=z(),i=Mu(2);o.firstUpdatePass&&gg(o,null,i,r);let s=_();if(n!==Ne&&_e(s,i,n)){let a=o.data[gt()];if(yg(a,r)&&!pg(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=uc(c,n||"")),nu(o,a,s,n,r)}else SC(o,a,s,s[G],s[i+1],s[i+1]=CC(e,t,n),r,i)}}function pg(e,t){return t>=e.expandoStartIndex}function gg(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[gt()],s=pg(e,n);yg(i,r)&&t===null&&!s&&(t=!1),t=DC(o,i,t,r),uC(o,i,t,n,s,r)}}function DC(e,t,n,r){let o=hD(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=ic(null,e,t,n,r),n=Wr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=ic(o,e,t,n,r),i===null){let c=wC(e,t,r);c!==void 0&&Array.isArray(c)&&(c=ic(null,e,t,c[1],r),c=Wr(c,t.attrs,r),EC(e,t,r,c))}else i=IC(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function wC(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Kn(r)!==0)return e[un(r)]}function EC(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[un(o)]=r}function IC(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Wr(r,s,n)}return Wr(r,t.attrs,n)}function ic(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Wr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Wr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),es(e,s,n?!0:t[++i]))}return e===void 0?null:e}function CC(e,t,n){if(n==null||n==="")return he;let r=[],o=Fe(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function bC(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&es(e,r,n)}function SC(e,t,n,r,o,i,s,a){o===Ne&&(o=he);let c=0,u=0,l=0<o.length?o[0]:null,f=0<i.length?i[0]:null;for(;l!==null||f!==null;){let h=c<o.length?o[c+1]:void 0,d=u<i.length?i[u+1]:void 0,p=null,m;l===f?(c+=2,u+=2,h!==d&&(p=f,m=d)):f===null||l!==null&&l<f?(c+=2,p=l):(u+=2,p=f,m=d),p!==null&&mg(e,t,n,r,p,m,s,a),l=c<o.length?o[c]:null,f=u<i.length?i[u]:null}}function mg(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=cC(u)?Sf(c,t,n,o,Kn(u),s):void 0;if(!Zi(l)){Zi(i)||sC(u)&&(i=Sf(c,null,n,o,a,s));let f=ah(gt(),n);NE(r,s,f,o,i)}}function Sf(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,f=l===null,h=n[o+1];h===Ne&&(h=f?he:void 0);let d=f?Ya(h,r):l===r?h:void 0;if(u&&!Zi(d)&&(d=Ya(c,r)),Zi(d)&&(a=d,s))return a;let p=e[o+1];o=s?un(p):Kn(p)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Ya(c,r))}return a}function Zi(e){return e!==void 0}function TC(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=De(Fe(e)))),e}function yg(e,t){return(e.flags&(t?8:16))!==0}function WO(e,t,n){let r=_(),o=cg(r,e,t,n);hg(es,dg,o,!0)}var ru=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function sc(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function MC(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],f=sc(i,u,i,l,n);if(f!==0){f<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),d=t[c],p=sc(s,h,c,d,n);if(p!==0){p<0&&e.updateValue(s,d),s--,c--;continue}let m=n(i,u),E=n(s,h),S=n(i,l);if(Object.is(S,E)){let J=n(c,d);Object.is(J,m)?(e.swap(i,s),e.updateValue(s,d),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new Yi,o??=Mf(e,i,s,n),ou(e,r,i,S))e.updateValue(i,l),i++,s++;else if(o.has(S))r.set(m,e.detach(i)),s--;else{let J=e.create(i,t[i]);e.attach(i,J),i++,s++}}for(;i<=c;)Tf(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),f=u.value,h=sc(i,l,i,f,n);if(h!==0)h<0&&e.updateValue(i,f),i++,u=c.next();else{r??=new Yi,o??=Mf(e,i,s,n);let d=n(i,f);if(ou(e,r,i,d))e.updateValue(i,f),i++,s++,u=c.next();else if(!o.has(d))e.attach(i,e.create(i,f)),i++,s++,u=c.next();else{let p=n(i,l);r.set(p,e.detach(i)),s--}}}for(;!u.done;)Tf(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function ou(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Tf(e,t,n,r,o){if(ou(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Mf(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Yi=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function ZO(e,t){vt("NgControlFlow");let n=_(),r=dn(),o=n[r]!==Ne?n[r]:-1,i=o!==-1?Qi(n,ne+o):void 0,s=0;if(_e(n,r,e)){let a=k(null);try{if(i!==void 0&&jp(i,s),e!==-1){let c=ne+e,u=Qi(n,c),l=cu(n[R],c),f=Zn(u,l.tView.ssrId),h=Jr(n,l,t,{dehydratedView:f});Xr(u,h,s,Gn(l,f))}}finally{k(a)}}else if(i!==void 0){let a=Lp(i,s);a!==void 0&&(a[te]=t)}}var iu=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-se}};function YO(e,t){return t}var su=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function QO(e,t,n,r,o,i,s,a,c,u,l,f,h){vt("NgControlFlow");let d=_(),p=z(),m=c!==void 0,E=_(),S=a?s.bind(E[be][te]):s,J=new su(m,S);E[ne+e]=J,Wi(d,p,e+1,t,n,r,o,Rt(p.consts,i)),m&&Wi(d,p,e+2,c,u,l,f,Rt(p.consts,h))}var au=class extends ru{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-se}at(t){return this.getLView(t)[te].$implicit}attach(t,n){let r=n[$n];this.needsIndexUpdate||=t!==this.length,Xr(this.lContainer,n,t,Gn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,_C(this.lContainer,t)}create(t,n){let r=Zn(this.lContainer,this.templateTNode.tView.ssrId),o=Jr(this.hostLView,this.templateTNode,new iu(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){ms(t[R],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[te].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[te].$index=t}getLView(t){return NC(this.lContainer,t)}};function KO(e){let t=k(null),n=gt();try{let r=_(),o=r[R],i=r[n],s=n+1,a=Qi(r,s);if(i.liveCollection===void 0){let u=cu(o,s);i.liveCollection=new au(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if(MC(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=dn(),l=c.length===0;if(_e(r,u,l)){let f=n+2,h=Qi(r,f);if(l){let d=cu(o,f),p=Zn(h,d.tView.ssrId),m=Jr(r,d,void 0,{dehydratedView:p});Xr(h,m,0,Gn(d,p))}else jp(h,0)}}}finally{k(t)}}function Qi(e,t){return e[t]}function _C(e,t){return qr(e,t)}function NC(e,t){return Lp(e,t)}function cu(e,t){return wu(e,t)}function vg(e,t,n,r){let o=_(),i=z(),s=ne+e,a=o[G],c=i.firstCreatePass?Hp(s,i,o,t,Yu,bu(),n,r):i.data[s],u=RC(i,o,c,a,t,e);o[s]=u;let l=os(c);return Ot(c,!0),vp(a,u,c),!Xu(c)&&cs()&&ys(i,o,u,c),(eD()===0||l)&&or(u,o),tD(),l&&(gs(i,o,c),$u(i,c,o)),r!==null&&Wu(o,c),vg}function Dg(){let e=ue();Su()?Tu():(e=e.parent,Ot(e,!1));let t=e;rD(t)&&oD(),nD();let n=z();return n.firstCreatePass&&zp(n,t),t.classesWithoutHost!=null&&wD(t)&&nu(n,t,_(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&ED(t)&&nu(n,t,_(),t.stylesWithoutHost,!1),Dg}function ll(e,t,n,r){return vg(e,t,n,r),Dg(),ll}var RC=(e,t,n,r,o,i)=>(us(!0),mp(r,o,yD()));function xC(e,t,n,r,o){let i=t.consts,s=Rt(i,r),a=ar(t,e,8,"ng-container",s);s!==null&&zc(a,s,!0);let c=Rt(i,o);return bu()&&rl(t,n,a,c,Yu),a.mergedAttrs=qn(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function wg(e,t,n){let r=_(),o=z(),i=e+ne,s=o.firstCreatePass?xC(i,o,r,t,n):o.data[i];Ot(s,!0);let a=OC(o,r,s,e);return r[i]=a,cs()&&ys(o,r,a,s),or(a,r),os(s)&&(gs(o,r,s),$u(o,s,r)),n!=null&&Wu(r,s),wg}function Eg(){let e=ue(),t=z();return Su()?Tu():(e=e.parent,Ot(e,!1)),t.firstCreatePass&&(xu(t,e),Du(e)&&t.queries.elementEnd(e)),Eg}function AC(e,t,n){return wg(e,t,n),Eg(),AC}var OC=(e,t,n,r)=>(us(!0),Qw(t[G],""));function JO(){return _()}function kC(e,t,n){let r=_(),o=dn();if(_e(r,o,t)){let i=z(),s=as();Zu(i,s,r,e,t,r[G],n,!0)}return kC}var Ki="en-US";var PC=Ki;function FC(e){typeof e=="string"&&(PC=e.toLowerCase().replace(/_/g,"-"))}function _f(e,t,n){return function r(o){if(o===Function)return n;let i=Xn(e)?qe(e.index,t):t;nl(i,5);let s=t[te],a=Nf(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=Nf(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Nf(e,t,n,r){let o=k(null);try{return V(6,t,n),n(r)!==!1}catch(i){return LC(e,i),!1}finally{V(7,t,n),k(o)}}function LC(e,t){let n=e[Hn],r=n?n.get(Ge,null):null;r&&r.handleError(t)}function Rf(e,t,n,r,o,i){let s=t[n],a=t[R],u=a.data[n].outputs[r],l=s[u],f=a.firstCreatePass?Cu(a):null,h=Iu(t),d=l.subscribe(i),p=h.length;h.push(i,d),f&&f.push(o,e.index,p,-(p+1))}function dl(e,t,n,r){let o=_(),i=z(),s=ue();return Ig(i,o,o[G],s,e,t,r),dl}function jC(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Ai],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Ig(e,t,n,r,o,i,s){let a=os(r),u=e.firstCreatePass?Cu(e):null,l=Iu(t),f=!0;if(r.type&3||s){let h=Ze(r,t),d=s?s(h):h,p=l.length,m=s?S=>s(ze(S[r.index])):r.index,E=null;if(!s&&a&&(E=jC(e,t,o,r.index)),E!==null){let S=E.__ngLastListenerFn__||E;S.__ngNextListenerFn__=i,E.__ngLastListenerFn__=i,f=!1}else{i=_f(r,t,i),cw(t,d,o,i);let S=n.listen(d,o,i);l.push(i,S),u&&u.push(o,m,p,p+1)}}else i=_f(r,t,i);if(f){let h=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let p=0;p<d.length;p+=2){let m=d[p],E=d[p+1];Rf(r,t,m,E,o,i)}if(h&&h.length)for(let p of h)Rf(r,t,p,o,o,i)}}function XO(e=1){return gD(e)}function VC(e,t){let n=null,r=Uw(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?gp(e,i,!0):zw(r,i))return o}return n}function ek(e){let t=_()[be][Ie];if(!t.projection){let n=e?e.length:1,r=t.projection=Nv(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?VC(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function tk(e,t=0,n,r,o,i){let s=_(),a=z(),c=r?e+1:null;c!==null&&Wi(s,a,c,r,o,i,null,n);let u=ar(a,ne+e,16,null,n||null);u.projection===null&&(u.projection=t),Tu();let f=!s[$n]||hh();s[be][Ie].projection[u.projection]===null&&c!==null?BC(s,a,c):f&&!Xu(u)&&ME(a,s,u)}function BC(e,t,n){let r=ne+n,o=t.data[r],i=e[r],s=Zn(i,o.tView.ssrId),a=Jr(e,o,void 0,{dehydratedView:s});Xr(i,a,0,Gn(o,s))}function UC(e,t,n,r){Yp(e,t,n,r)}function nk(e,t,n){_I(e,t,n)}function $C(e){let t=_(),n=z(),r=_u();ss(r+1);let o=il(n,r);if(e.dirty&&Yv(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Kp(t,r);e.reset(i,Bh),e.notifyOnChanges()}return!0}return!1}function HC(){return ol(_(),_u())}function rk(e,t,n,r,o){OI(t,Yp(e,n,r,o))}function ok(e=1){ss(_u()+e)}function ik(e){let t=sD();return ch(t,ne+e)}function sk(e,t=""){let n=_(),r=z(),o=e+ne,i=r.firstCreatePass?ar(r,o,1,t,null):r.data[o],s=zC(r,n,i,t,e);n[o]=s,cs()&&ys(r,n,s,i),Ot(i,!1)}var zC=(e,t,n,r,o)=>(us(!0),Zw(t[G],r));function qC(e){return Cg("",e,""),qC}function Cg(e,t,n){let r=_(),o=cg(r,e,t,n);return o!==Ne&&bg(r,gt(),o),Cg}function GC(e,t,n,r,o){let i=_(),s=iC(i,e,t,n,r,o);return s!==Ne&&bg(i,gt(),s),GC}function bg(e,t,n){let r=ah(t,e);Yw(e[G],r,n)}function WC(e,t,n){Uh(t)&&(t=t());let r=_(),o=dn();if(_e(r,o,t)){let i=z(),s=as();Zu(i,s,r,e,t,r[G],n,!1)}return WC}function ak(e,t){let n=Uh(e);return n&&e.set(t),n}function ZC(e,t){let n=_(),r=z(),o=ue();return Ig(r,n,n[G],o,e,t),ZC}var YC={};function QC(e){let t=z(),n=_(),r=e+ne,o=ar(t,r,128,null,null);return Ot(o,!1),uh(t,n,r,YC),QC}function KC(e,t,n){let r=z();if(r.firstCreatePass){let o=He(e);uu(n,r.data,r.blueprint,o,!0),uu(t,r.data,r.blueprint,o,!1)}}function uu(e,t,n,r,o){if(e=fe(e),Array.isArray(e))for(let i=0;i<e.length;i++)uu(e[i],t,n,r,o);else{let i=z(),s=_(),a=ue(),c=Un(e)?e:fe(e.provide),u=Jf(e),l=a.providerIndexes&1048575,f=a.directiveStart,h=a.providerIndexes>>20;if(Un(e)||!e.multi){let d=new sn(u,o,le),p=cc(c,t,o?l:l+h,f);p===-1?(Ec(Vi(a,s),i,c),ac(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[p]=d,s[p]=d)}else{let d=cc(c,t,l+h,f),p=cc(c,t,l,l+h),m=d>=0&&n[d],E=p>=0&&n[p];if(o&&!E||!o&&!m){Ec(Vi(a,s),i,c);let S=eb(o?XC:JC,n.length,o,r,u);!o&&E&&(n[p].providerFactory=S),ac(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(S),s.push(S)}else{let S=Sg(n[o?p:d],u,!o&&r);ac(i,e,d>-1?d:p,S)}!o&&r&&E&&n[p].componentProviders++}}}function ac(e,t,n,r){let o=Un(t),i=jv(t);if(o||i){let c=(i?fe(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function Sg(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function cc(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function JC(e,t,n,r,o){return lu(this.multi,[])}function XC(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Hr(r,r[R],this.providerFactory.index,o);s=c.slice(0,a),lu(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],lu(i,s);return s}function lu(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function eb(e,t,n,r,o){let i=new sn(e,n,le);return i.multi=[],i.index=t,i.componentProviders=0,Sg(i,o,r&&!n),i}function ck(e,t=[]){return n=>{n.providersResolver=(r,o)=>KC(r,o?o(e):e,t)}}function tb(e,t){let n=e[t];return n===Ne?void 0:n}function nb(e,t,n,r,o,i){let s=t+n;return _e(e,s,o)?YI(e,s+1,i?r.call(i,o):r(o)):tb(e,s+1)}function uk(e,t){let n=z(),r,o=e+ne;n.firstCreatePass?(r=rb(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Jt(r.type,!0)),s,a=ye(le);try{let c=ji(!1),u=i();return ji(c),uh(n,_(),o,u),u}finally{ye(a)}}function rb(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function lk(e,t,n){let r=e+ne,o=_(),i=ch(o,r);return ob(o,r)?nb(o,aD(),t,i.transform,n,i):i.transform(n)}function ob(e,t){return e[R].data[t].pure}function dk(e,t){return Ds(e,t)}var Zr=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},fk=new Zr("19.2.13"),du=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Tg=(()=>{class e{compileModuleSync(n){return new Jc(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Zf(n),i=hp(o.declarations).reduce((s,a)=>{let c=Nt(a);return c&&s.push(new cn(c)),s},[]);return new du(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ib=(()=>{class e{zone=g(H);changeDetectionScheduler=g(an);applicationRef=g(xt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),sb=new w("",{factory:()=>!1});function Mg({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new H(j(y({},_g()),{scheduleInRootZone:n})),[{provide:H,useFactory:e},{provide:Bn,multi:!0,useFactory:()=>{let r=g(ib,{optional:!0});return()=>r.initialize()}},{provide:Bn,multi:!0,useFactory:()=>{let r=g(ab);return()=>{r.initialize()}}},t===!0?{provide:Ph,useValue:!0}:[],{provide:Fh,useValue:n??kh}]}function hk(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Mg({ngZoneFactory:()=>{let o=_g(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&vt("NgZone_CoalesceEvent"),new H(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return ln([{provide:sb,useValue:!0},{provide:ds,useValue:!1},r])}function _g(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var ab=(()=>{class e{subscription=new Z;initialized=!1;zone=g(H);pendingTasks=g(mt);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{H.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var cb=(()=>{class e{appRef=g(xt);taskService=g(mt);ngZone=g(H);zonelessEnabled=g(ds);tracing=g(sr,{optional:!0});disableScheduling=g(Ph,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Z;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ui):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(g(Fh,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Sc||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?rf:Lh;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ui+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,rf(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ub(){return typeof $localize<"u"&&$localize.locale||Ki}var fl=new w("",{providedIn:"root",factory:()=>g(fl,O.Optional|O.SkipSelf)||ub()});var fu=new w(""),lb=new w("");function Lr(e){return!e.moduleRef}function db(e){let t=Lr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(H);return n.run(()=>{Lr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ge,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Lr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(fu);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(fu);s.add(i),e.moduleRef.onDestroy(()=>{Si(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return hb(r,n,()=>{let i=t.get(ag);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(fl,Ki);if(FC(s||Ki),!t.get(lb,!0))return Lr(e)?t.get(xt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Lr(e)){let c=t.get(xt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return fb(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function fb(e,t){let n=e.injector.get(xt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new v(-403,!1);t.push(e)}function hb(e,t,n){try{let r=n();return no(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Ti=null;function pb(e=[],t){return we.create({name:t,providers:[{provide:ts,useValue:"platform"},{provide:fu,useValue:new Set([()=>Ti=null])},...e]})}function gb(e=[]){if(Ti)return Ti;let t=pb(e);return Ti=t,tC(),mb(t),t}function mb(e){let t=e.get(Lu,null);Ee(e,()=>{t?.forEach(n=>n())})}var ro=(()=>{class e{static __NG_ELEMENT_ID__=yb}return e})();function yb(e){return vb(ue(),_(),(e&16)===16)}function vb(e,t,n){if(Xn(e)&&!n){let r=qe(e.index,t);return new Gr(r,r)}else if(e.type&175){let r=t[be];return new Gr(r,t)}return null}function Ng(e){V(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=gb(r),i=[Mg({}),{provide:an,useExisting:cb},...n||[]],s=new Gi({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return db({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{V(9)}}function oo(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Db(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function wb(e){return Ra(e)}function pk(e,t){return Bo(e,t?.equal)}var hu=class{[pe];constructor(t){this[pe]=t}destroy(){this[pe].destroy()}};function Eb(e,t){!t?.injector&&vu(Eb);let n=t?.injector??g(we),r=t?.manualCleanup!==!0?n.get(kt):null,o,i=n.get(Bu,null,{optional:!0}),s=n.get(an);return i!==null&&!t?.forceRoot?(o=bb(i.view,s,e),r instanceof Bi&&r._lView===i.view&&(r=null)):o=Sb(e,n.get(og),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new hu(o)}var Rg=j(y({},En),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:zr,run(){if(this.dirty=!1,this.hasRun&&!Lo(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Mr(this),n=Pi(!1);try{this.maybeCleanup(),this.fn(e)}finally{Pi(n),Fo(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),Ib=j(y({},Rg),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){_r(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),Cb=j(y({},Rg),{consumerMarkedDirty(){this.view[M]|=8192,nr(this.view),this.notifier.notify(13)},destroy(){_r(this),this.onDestroyFn(),this.maybeCleanup(),this.view[tn]?.delete(this)}});function bb(e,t,n){let r=Object.create(Cb);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[tn]??=new Set,e[tn].add(r),r.consumerMarkedDirty(r),r}function Sb(e,t,n){let r=Object.create(Ib);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function gk(e,t){let n=Nt(e),r=t.elementInjector||ns();return new cn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function xg(e){let t=Nt(e);if(!t)return null;let n=new cn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var K=new w("");var kg=null;function wt(){return kg}function hl(e){kg??=e}var io=class{},so=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(Pg),providedIn:"platform"})}return e})(),pl=new w(""),Pg=(()=>{class e extends so{_location;_history;_doc=g(K);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return wt().getBaseHref(this._doc)}onPopState(n){let r=wt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=wt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Is(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Ag(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Le(e){return e&&e[0]!=="?"?`?${e}`:e}var je=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(bs),providedIn:"root"})}return e})(),Cs=new w(""),bs=(()=>{class e extends je{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??g(K).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Is(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Le(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Le(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Le(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(so),I(Cs,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Vt=(()=>{class e{_subject=new q;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=_b(Ag(Og(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Le(r))}normalize(n){return e.stripTrailingSlash(Mb(this._basePath,Og(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Le(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Le(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Le;static joinWithSlash=Is;static stripTrailingSlash=Ag;static \u0275fac=function(r){return new(r||e)(I(je))};static \u0275prov=D({token:e,factory:()=>Tb(),providedIn:"root"})}return e})();function Tb(){return new Vt(I(je))}function Mb(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Og(e){return e.replace(/\/index.html$/,"")}function _b(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var ml=(()=>{class e extends je{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Is(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Le(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Le(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(so),I(Cs,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var gl=/\s+/,Fg=[],Nb=(()=>{class e{_ngEl;_renderer;initialClasses=Fg;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(gl):Fg}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(gl):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(gl).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(le(yt),le(eo))};static \u0275dir=jt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Rb=(()=>{class e{_viewContainer;_context=new Ss;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Lg(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Lg(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(le(Ft),le(Wn))};static \u0275dir=jt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ss=class{$implicit=null;ngIf=null};function Lg(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}var xb=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(le(Ft))};static \u0275dir=jt({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[tr]})}return e})();function Ab(e,t){return new v(2100,!1)}var Ob=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,kb=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw Ab(e,n);return n.replace(Ob,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=eg({name:"titlecase",type:e,pure:!0})}return e})();var jg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Lt({type:e});static \u0275inj=At({})}return e})();function ao(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Ts="browser",Vg="server";function Pb(e){return e===Ts}function Ms(e){return e===Vg}var fn=class{};var Bg=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new yl(g(K),window)})}return e})(),yl=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=Lb(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function Lb(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Rs=new w(""),El=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(I(Rs),I(H))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),co=class{_doc;constructor(t){this._doc=t}manager},_s="ng-app-id";function Ug(e){for(let t of e)t.remove()}function $g(e,t){let n=t.createElement("style");return n.textContent=e,n}function Vb(e,t,n,r){let o=e.head?.querySelectorAll(`style[${_s}="${t}"],link[${_s}="${t}"]`);if(o)for(let i of o)i.removeAttribute(_s),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Dl(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Il=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Ms(i),Vb(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,$g);r?.forEach(o=>this.addUsage(o,this.external,Dl))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Ug(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Ug(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,$g(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Dl(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(_s,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(K),I(Fu),I(ju,8),I(ir))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),vl={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Cl=/%COMP%/g;var zg="%COMP%",Bb=`_nghost-${zg}`,Ub=`_ngcontent-${zg}`,$b=!0,Hb=new w("",{providedIn:"root",factory:()=>$b});function zb(e){return Ub.replace(Cl,e)}function qb(e){return Bb.replace(Cl,e)}function qg(e,t){return t.map(n=>n.replace(Cl,e))}var bl=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Ms(a),this.defaultRenderer=new uo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===We.ShadowDom&&(r=j(y({},r),{encapsulation:We.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Ns?o.applyToHost(n):o instanceof lo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,f=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case We.Emulated:i=new Ns(c,u,r,this.appId,l,s,a,f,h);break;case We.ShadowDom:return new wl(c,u,n,r,s,a,this.nonce,f,h);default:i=new lo(c,u,r,l,s,a,f,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(I(El),I(Il),I(Fu),I(Hb),I(K),I(ir),I(H),I(ju),I(sr,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),uo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(vl[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Hg(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Hg(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=vl[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=vl[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(ft.DashCase|ft.Important)?t.style.setProperty(n,r,o&ft.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&ft.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=wt().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Hg(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var wl=class extends uo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=qg(o.id,l);for(let h of l){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=h,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let h of f){let d=Dl(h,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},lo=class extends uo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?qg(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ns=class extends lo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=zb(l),this.hostAttr=qb(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var xs=class e extends io{supportsDOMEvents=!0;static makeCurrent(){hl(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Gb();return n==null?null:Wb(n)}resetBaseElement(){fo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return ao(document.cookie,t)}},fo=null;function Gb(){return fo=fo||document.head.querySelector("base"),fo?fo.getAttribute("href"):null}function Wb(e){return new URL(e,document.baseURI).pathname}var Zb=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Wg=(()=>{class e extends co{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(K))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Gg=["alt","control","meta","shift"],Yb={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Qb={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Zg=(()=>{class e extends co{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>wt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Gg.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=Yb[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Gg.forEach(s=>{if(s!==o){let a=Qb[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(I(K))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function Kb(e,t){return Ng(y({rootComponent:e},Jb(t)))}function Jb(e){return{appProviders:[...rS,...e?.providers??[]],platformProviders:nS}}function Xb(){xs.makeCurrent()}function eS(){return new Ge}function tS(){return Zh(document),document}var nS=[{provide:ir,useValue:Ts},{provide:Lu,useValue:Xb,multi:!0},{provide:K,useFactory:tS}];var rS=[{provide:ts,useValue:"root"},{provide:Ge,useFactory:eS},{provide:Rs,useClass:Wg,multi:!0,deps:[K]},{provide:Rs,useClass:Zg,multi:!0,deps:[K]},bl,Il,El,{provide:Yn,useExisting:bl},{provide:fn,useClass:Zb},[]];var ur=class{},lr=class{},Re=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var ks=class{encodeKey(t){return Yg(t)}encodeValue(t){return Yg(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function oS(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var iS=/%(\d[a-f0-9])/gi,sS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Yg(e){return encodeURIComponent(e).replace(iS,(t,n)=>sS[n]??t)}function As(e){return`${e}`}var It=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new ks,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=oS(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(As):[As(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(As(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(As(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ps=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function aS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Qg(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Kg(e){return typeof Blob<"u"&&e instanceof Blob}function Jg(e){return typeof FormData<"u"&&e instanceof FormData}function cS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var ho="Content-Type",Fs="Accept",Nl="X-Request-URL",tm="text/plain",nm="application/json",rm=`${nm}, ${tm}, */*`,cr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(aS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Re,this.context??=new Ps,!this.params)this.params=new It,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Qg(this.body)||Kg(this.body)||Jg(this.body)||cS(this.body)?this.body:this.body instanceof It?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Jg(this.body)?null:Kg(this.body)?this.body.type||null:Qg(this.body)?null:typeof this.body=="string"?tm:this.body instanceof It?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?nm:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,f=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,d)=>h.set(d,t.setHeaders[d]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,d)=>h.set(d,t.setParams[d]),l)),new e(n,r,s,{params:l,headers:u,context:f,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Ct=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Ct||{}),dr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Re,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},po=class e extends dr{constructor(t={}){super(t)}type=Ct.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},fr=class e extends dr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Ct.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Et=class extends dr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},om=200,uS=204;function Sl(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var js=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof cr)i=n;else{let c;o.headers instanceof Re?c=o.headers:c=new Re(o.headers);let u;o.params&&(o.params instanceof It?u=o.params:u=new It({fromObject:o.params})),i=new cr(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=C(i).pipe(Ue(c=>this.handler.handle(c)));if(n instanceof cr||o.observe==="events")return s;let a=s.pipe(oe(c=>c instanceof fr));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(P(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(P(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(P(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(P(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new It().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Sl(o,r))}post(n,r,o={}){return this.request("POST",n,Sl(o,r))}put(n,r,o={}){return this.request("PUT",n,Sl(o,r))}static \u0275fac=function(r){return new(r||e)(I(ur))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),lS=/^\)\]\}',?\n/;function Xg(e){if(e.url)return e.url;let t=Nl.toLocaleLowerCase();return e.headers.get(t)}var im=new w(""),Os=(()=>{class e{fetchImpl=g(Tl,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=g(H);destroyRef=g(kt);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new F(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(Ml,i=>r.error(new Et({error:i}))),()=>o.abort()})}doRequest(n,r,o){return br(this,null,function*(){let i=this.createRequestInit(n),s;try{let d=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,y({signal:r},i)));dS(d),o.next({type:Ct.Sent}),s=yield d}catch(d){o.error(new Et({error:d,status:d.status??0,statusText:d.statusText,url:n.urlWithParams,headers:d.headers}));return}let a=new Re(s.headers),c=s.statusText,u=Xg(s)??n.urlWithParams,l=s.status,f=null;if(n.reportProgress&&o.next(new po({headers:a,status:l,statusText:c,url:u})),s.body){let d=s.headers.get("content-length"),p=[],m=s.body.getReader(),E=0,S,J,W=typeof Zone<"u"&&Zone.current,wn=!1;if(yield this.ngZone.runOutsideAngular(()=>br(this,null,function*(){for(;;){if(this.destroyed){yield m.cancel(),wn=!0;break}let{done:qt,value:ya}=yield m.read();if(qt)break;if(p.push(ya),E+=ya.length,n.reportProgress){J=n.responseType==="text"?(J??"")+(S??=new TextDecoder).decode(ya,{stream:!0}):void 0;let ad=()=>o.next({type:Ct.DownloadProgress,total:d?+d:void 0,loaded:E,partialText:J});W?W.run(ad):ad()}}})),wn){o.complete();return}let ma=this.concatChunks(p,E);try{let qt=s.headers.get(ho)??"";f=this.parseBody(n,ma,qt)}catch(qt){o.error(new Et({error:qt,headers:new Re(s.headers),status:s.status,statusText:s.statusText,url:Xg(s)??n.urlWithParams}));return}}l===0&&(l=f?om:0),l>=200&&l<300?(o.next(new fr({body:f,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new Et({error:f,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(lS,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(Fs)||(r[Fs]=rm),!n.headers.has(ho)){let i=n.detectContentTypeHeader();i!==null&&(r[ho]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Tl=class{};function Ml(){}function dS(e){e.then(Ml,Ml)}function sm(e,t){return t(e)}function fS(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function hS(e,t,n){return(r,o)=>Ee(n,()=>t(r,i=>e(i,o)))}var am=new w(""),Rl=new w(""),cm=new w(""),xl=new w("",{providedIn:"root",factory:()=>!0});function pS(){let e=null;return(t,n)=>{e===null&&(e=(g(am,{optional:!0})??[]).reduceRight(fS,sm));let r=g(mt);if(g(xl)){let i=r.add();return e(t,n).pipe(Tt(()=>r.remove(i)))}else return e(t,n)}}var Ls=(()=>{class e extends ur{backend;injector;chain=null;pendingTasks=g(mt);contributeToStability=g(xl);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Rl),...this.injector.get(cm,[])]));this.chain=r.reduceRight((o,i)=>hS(o,i,this.injector),sm)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Tt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(I(lr),I(ae))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var gS=/^\)\]\}',?\n/,mS=RegExp(`^${Nl}:`,"m");function yS(e){return"responseURL"in e&&e.responseURL?e.responseURL:mS.test(e.getAllResponseHeaders())?e.getResponseHeader(Nl):null}var _l=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?$(r.\u0275loadImpl()):C(null)).pipe(me(()=>new F(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,E)=>s.setRequestHeader(m,E.join(","))),n.headers.has(Fs)||s.setRequestHeader(Fs,rm),!n.headers.has(ho)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(ho,m)}if(n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",E=new Re(s.getAllResponseHeaders()),S=yS(s)||n.url;return c=new po({headers:E,status:s.status,statusText:m,url:S}),c},l=()=>{let{headers:m,status:E,statusText:S,url:J}=u(),W=null;E!==uS&&(W=typeof s.response>"u"?s.responseText:s.response),E===0&&(E=W?om:0);let wn=E>=200&&E<300;if(n.responseType==="json"&&typeof W=="string"){let ma=W;W=W.replace(gS,"");try{W=W!==""?JSON.parse(W):null}catch(qt){W=ma,wn&&(wn=!1,W={error:qt,text:W})}}wn?(i.next(new fr({body:W,headers:m,status:E,statusText:S,url:J||void 0})),i.complete()):i.error(new Et({error:W,headers:m,status:E,statusText:S,url:J||void 0}))},f=m=>{let{url:E}=u(),S=new Et({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:E||void 0});i.error(S)},h=!1,d=m=>{h||(i.next(u()),h=!0);let E={type:Ct.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(E.total=m.total),n.responseType==="text"&&s.responseText&&(E.partialText=s.responseText),i.next(E)},p=m=>{let E={type:Ct.UploadProgress,loaded:m.loaded};m.lengthComputable&&(E.total=m.total),i.next(E)};return s.addEventListener("load",l),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),n.reportProgress&&(s.addEventListener("progress",d),a!==null&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:Ct.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",l),s.removeEventListener("timeout",f),n.reportProgress&&(s.removeEventListener("progress",d),a!==null&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(I(fn))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),um=new w(""),vS="XSRF-TOKEN",DS=new w("",{providedIn:"root",factory:()=>vS}),wS="X-XSRF-TOKEN",ES=new w("",{providedIn:"root",factory:()=>wS}),go=class{},IS=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=ao(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(I(K),I(DS))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function CS(e,t){let n=e.url.toLowerCase();if(!g(um)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=g(go).getToken(),o=g(ES);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Vs=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Vs||{});function lm(e,t){return{\u0275kind:e,\u0275providers:t}}function dm(...e){let t=[js,_l,Ls,{provide:ur,useExisting:Ls},{provide:lr,useFactory:()=>g(im,{optional:!0})??g(_l)},{provide:Rl,useValue:CS,multi:!0},{provide:um,useValue:!0},{provide:go,useClass:IS}];for(let n of e)t.push(...n.\u0275providers);return ln(t)}var em=new w("");function fm(){return lm(Vs.LegacyInterceptors,[{provide:em,useFactory:pS},{provide:Rl,useExisting:em,multi:!0}])}function bS(){return lm(Vs.Fetch,[Os,{provide:im,useExisting:Os},{provide:lr,useExisting:Os}])}var SS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Lt({type:e});static \u0275inj=At({providers:[dm(fm())]})}return e})();var hm=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(I(K))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var MS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=I(_S),o},providedIn:"root"})}return e})(),_S=(()=>{class e extends MS{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case Ye.NONE:return r;case Ye.HTML:return Pt(r,"HTML")?Fe(r):lp(this._doc,String(r)).toString();case Ye.STYLE:return Pt(r,"Style")?Fe(r):r;case Ye.SCRIPT:if(Pt(r,"Script"))return Fe(r);throw new v(5200,!1);case Ye.URL:return Pt(r,"URL")?Fe(r):hs(String(r));case Ye.RESOURCE_URL:if(Pt(r,"ResourceURL"))return Fe(r);throw new v(5201,!1);default:throw new v(5202,!1)}}bypassSecurityTrustHtml(n){return ep(n)}bypassSecurityTrustStyle(n){return tp(n)}bypassSecurityTrustScript(n){return np(n)}bypassSecurityTrustUrl(n){return rp(n)}bypassSecurityTrustResourceUrl(n){return op(n)}static \u0275fac=function(r){return new(r||e)(I(K))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var A="primary",_o=Symbol("RouteTitle"),Fl=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function gn(e){return new Fl(e)}function Em(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function RS(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Ke(e[n],t[n]))return!1;return!0}function Ke(e,t){let n=e?Ll(e):void 0,r=t?Ll(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Im(e[o],t[o]))return!1;return!0}function Ll(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Im(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function Cm(e){return e.length>0?e[e.length-1]:null}function zt(e){return $a(e)?e:no(e)?$(Promise.resolve(e)):C(e)}var xS={exact:Sm,subset:Tm},bm={exact:AS,subset:OS,ignored:()=>!0};function pm(e,t,n){return xS[n.paths](e.root,t.root,n.matrixParams)&&bm[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function AS(e,t){return Ke(e,t)}function Sm(e,t,n){if(!hn(e.segments,t.segments)||!$s(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Sm(e.children[r],t.children[r],n))return!1;return!0}function OS(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Im(e[n],t[n]))}function Tm(e,t,n){return Mm(e,t,t.segments,n)}function Mm(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!hn(o,n)||t.hasChildren()||!$s(o,n,r))}else if(e.segments.length===n.length){if(!hn(e.segments,n)||!$s(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!Tm(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!hn(e.segments,o)||!$s(e.segments,o,r)||!e.children[A]?!1:Mm(e.children[A],t,i,r)}}function $s(e,t,n){return t.every((r,o)=>bm[n](e[o].parameters,r.parameters))}var Xe=class{root;queryParams;fragment;_queryParamMap;constructor(t=new L([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=gn(this.queryParams),this._queryParamMap}toString(){return FS.serialize(this)}},L=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Hs(this)}},Bt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=gn(this.parameters),this._parameterMap}toString(){return Nm(this)}};function kS(e,t){return hn(e,t)&&e.every((n,r)=>Ke(n.parameters,t[r].parameters))}function hn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function PS(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===A&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==A&&(n=n.concat(t(o,r)))}),n}var mn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new Ut,providedIn:"root"})}return e})(),Ut=class{parse(t){let n=new Vl(t);return new Xe(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${mo(t.root,!0)}`,r=VS(t.queryParams),o=typeof t.fragment=="string"?`#${LS(t.fragment)}`:"";return`${n}${r}${o}`}},FS=new Ut;function Hs(e){return e.segments.map(t=>Nm(t)).join("/")}function mo(e,t){if(!e.hasChildren())return Hs(e);if(t){let n=e.children[A]?mo(e.children[A],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==A&&r.push(`${o}:${mo(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=PS(e,(r,o)=>o===A?[mo(e.children[A],!1)]:[`${o}:${mo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[A]!=null?`${Hs(e)}/${n[0]}`:`${Hs(e)}/(${n.join("//")})`}}function _m(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Bs(e){return _m(e).replace(/%3B/gi,";")}function LS(e){return encodeURI(e)}function jl(e){return _m(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function zs(e){return decodeURIComponent(e)}function gm(e){return zs(e.replace(/\+/g,"%20"))}function Nm(e){return`${jl(e.path)}${jS(e.parameters)}`}function jS(e){return Object.entries(e).map(([t,n])=>`;${jl(t)}=${jl(n)}`).join("")}function VS(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Bs(n)}=${Bs(o)}`).join("&"):`${Bs(n)}=${Bs(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var BS=/^[^\/()?;#]+/;function Al(e){let t=e.match(BS);return t?t[0]:""}var US=/^[^\/()?;=#]+/;function $S(e){let t=e.match(US);return t?t[0]:""}var HS=/^[^=?&#]+/;function zS(e){let t=e.match(HS);return t?t[0]:""}var qS=/^[^&#]+/;function GS(e){let t=e.match(qS);return t?t[0]:""}var Vl=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new L([],{}):new L([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[A]=new L(t,n)),r}parseSegment(){let t=Al(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new Bt(zs(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=$S(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Al(this.remaining);o&&(r=o,this.capture(r))}t[zs(n)]=zs(r)}parseQueryParam(t){let n=zS(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=GS(this.remaining);s&&(r=s,this.capture(r))}let o=gm(n),i=gm(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Al(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=A);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[A]:new L([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function Rm(e){return e.segments.length>0?new L([],{[A]:e}):e}function xm(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=xm(o);if(r===A&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new L(e.segments,t);return WS(n)}function WS(e){if(e.numberOfChildren===1&&e.children[A]){let t=e.children[A];return new L(e.segments.concat(t.segments),t.children)}return e}function $t(e){return e instanceof Xe}function Am(e,t,n=null,r=null){let o=Om(e);return km(o,t,n,r)}function Om(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new L(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Rm(r);return t??o}function km(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Ol(o,o,o,n,r);let i=ZS(t);if(i.toRoot())return Ol(o,o,new L([],{}),n,r);let s=YS(i,o,e),a=s.processChildren?vo(s.segmentGroup,s.index,i.commands):Fm(s.segmentGroup,s.index,i.commands);return Ol(o,s.segmentGroup,a,n,r)}function Gs(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function wo(e){return typeof e=="object"&&e!=null&&e.outlets}function Ol(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=Pm(e,t,n);let a=Rm(xm(s));return new Xe(a,i,o)}function Pm(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Pm(i,t,n)}),new L(e.segments,r)}var Ws=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Gs(r[0]))throw new v(4003,!1);let o=r.find(wo);if(o&&o!==Cm(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ZS(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Ws(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Ws(n,t,r)}var gr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function YS(e,t,n){if(e.isAbsolute)return new gr(t,!0,0);if(!n)return new gr(t,!1,NaN);if(n.parent===null)return new gr(n,!0,0);let r=Gs(e.commands[0])?0:1,o=n.segments.length-1+r;return QS(n,o,e.numberOfDoubleDots)}function QS(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new gr(r,!1,o-i)}function KS(e){return wo(e[0])?e[0].outlets:{[A]:e}}function Fm(e,t,n){if(e??=new L([],{}),e.segments.length===0&&e.hasChildren())return vo(e,t,n);let r=JS(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new L(e.segments.slice(0,r.pathIndex),{});return i.children[A]=new L(e.segments.slice(r.pathIndex),e.children),vo(i,0,o)}else return r.match&&o.length===0?new L(e.segments,{}):r.match&&!e.hasChildren()?Bl(e,t,n):r.match?vo(e,0,o):Bl(e,t,n)}function vo(e,t,n){if(n.length===0)return new L(e.segments,{});{let r=KS(n),o={};if(Object.keys(r).some(i=>i!==A)&&e.children[A]&&e.numberOfChildren===1&&e.children[A].segments.length===0){let i=vo(e.children[A],t,n);return new L(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Fm(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new L(e.segments,o)}}function JS(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(wo(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!ym(c,u,s))return i;r+=2}else{if(!ym(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Bl(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(wo(i)){let c=XS(i.outlets);return new L(r,c)}if(o===0&&Gs(n[0])){let c=e.segments[t];r.push(new Bt(c.path,mm(n[0]))),o++;continue}let s=wo(i)?i.outlets[A]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Gs(a)?(r.push(new Bt(s,mm(a))),o+=2):(r.push(new Bt(s,{})),o++)}return new L(r,{})}function XS(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Bl(new L([],{}),0,r))}),t}function mm(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function ym(e,t,n){return e==n.path&&Ke(t,n.parameters)}var qs="imperative",re=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(re||{}),Te=class{id;url;constructor(t,n){this.id=t,this.url=n}},Ht=class extends Te{type=re.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},xe=class extends Te{urlAfterRedirects;type=re.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ce=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(Ce||{}),yr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(yr||{}),Je=class extends Te{reason;code;type=re.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},et=class extends Te{reason;code;type=re.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},vr=class extends Te{error;target;type=re.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Eo=class extends Te{urlAfterRedirects;state;type=re.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Zs=class extends Te{urlAfterRedirects;state;type=re.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ys=class extends Te{urlAfterRedirects;state;shouldActivate;type=re.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Qs=class extends Te{urlAfterRedirects;state;type=re.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ks=class extends Te{urlAfterRedirects;state;type=re.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Js=class{route;type=re.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Xs=class{route;type=re.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ea=class{snapshot;type=re.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ta=class{snapshot;type=re.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},na=class{snapshot;type=re.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ra=class{snapshot;type=re.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Dr=class{routerEvent;position;anchor;type=re.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},Io=class{},wr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function eT(e,t){return e.providers&&!e._injector&&(e._injector=to(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Ve(e){return e.outlet||A}function tT(e,t){let n=e.filter(r=>Ve(r)===t);return n.push(...e.filter(r=>Ve(r)!==t)),n}function No(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var oa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return No(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new yn(this.rootInjector)}},yn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new oa(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(I(ae))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ia=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Ul(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Ul(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=$l(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return $l(t,this._root).map(n=>n.value)}};function Ul(e,t){if(e===t.value)return t;for(let n of t.children){let r=Ul(e,n);if(r)return r}return null}function $l(e,t){if(e===t.value)return[t];for(let n of t.children){let r=$l(e,n);if(r.length)return r.unshift(t),r}return[]}var Se=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function pr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Co=class extends ia{snapshot;constructor(t,n){super(t),this.snapshot=n,Ql(this,t)}toString(){return this.snapshot.toString()}};function Lm(e){let t=nT(e),n=new ee([new Bt("",{})]),r=new ee({}),o=new ee({}),i=new ee({}),s=new ee(""),a=new tt(n,r,i,s,o,A,e,t.root);return a.snapshot=t.root,new Co(new Se(a,[]),t)}function nT(e){let t={},n={},r={},o="",i=new pn([],t,r,o,n,A,e,null,{});return new bo("",new Se(i,[]))}var tt=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(P(u=>u[_o]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(P(t=>gn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(P(t=>gn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function sa(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:y(y({},t.params),e.params),data:y(y({},t.data),e.data),resolve:y(y(y(y({},e.data),t.data),o?.data),e._resolvedData)}:r={params:y({},e.params),data:y({},e.data),resolve:y(y({},e.data),e._resolvedData??{})},o&&Vm(o)&&(r.resolve[_o]=o.title),r}var pn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[_o]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=gn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=gn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},bo=class extends ia{url;constructor(t,n){super(n),this.url=t,Ql(this,n)}toString(){return jm(this._root)}};function Ql(e,t){t.value._routerState=e,t.children.forEach(n=>Ql(e,n))}function jm(e){let t=e.children.length>0?` { ${e.children.map(jm).join(", ")} } `:"";return`${e.value}${t}`}function kl(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Ke(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Ke(t.params,n.params)||e.paramsSubject.next(n.params),RS(t.url,n.url)||e.urlSubject.next(n.url),Ke(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Hl(e,t){let n=Ke(e.params,t.params)&&kS(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Hl(e.parent,t.parent))}function Vm(e){return typeof e.title=="string"||e.title===null}var Bm=new w(""),Kl=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=A;activateEvents=new ve;deactivateEvents=new ve;attachEvents=new ve;detachEvents=new ve;routerOutletData=Vh(void 0);parentContexts=g(yn);location=g(Ft);changeDetector=g(ro);inputBinder=g(Ro,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new zl(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=jt({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[tr]})}return e})(),zl=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===tt?this.route:t===yn?this.childContexts:t===Bm?this.outletData:this.parent.get(t,n)}},Ro=new w(""),Jl=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=Fr([r.queryParams,r.params,r.data]).pipe(me(([i,s,a],c)=>(a=y(y(y({},i),s),a),c===0?C(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=xg(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Xl=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Xp({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&ll(0,"router-outlet")},dependencies:[Kl],encapsulation:2})}return e})();function ed(e){let t=e.children&&e.children.map(ed),n=t?j(y({},e),{children:t}):y({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==A&&(n.component=Xl),n}function rT(e,t,n){let r=So(e,t._root,n?n._root:void 0);return new Co(r,t)}function So(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=oT(e,t,n);return new Se(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>So(e,a)),s}}let r=iT(t.value),o=t.children.map(i=>So(e,i));return new Se(r,o)}}function oT(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return So(e,r,o);return So(e,r)})}function iT(e){return new tt(new ee(e.url),new ee(e.params),new ee(e.queryParams),new ee(e.fragment),new ee(e.data),e.outlet,e.component,e)}var Er=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Um="ngNavigationCancelingError";function aa(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=$t(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=$m(!1,Ce.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function $m(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Um]=!0,n.cancellationCode=t,n}function sT(e){return Hm(e)&&$t(e.url)}function Hm(e){return!!e&&e[Um]}var aT=(e,t,n,r)=>P(o=>(new ql(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),ql=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),kl(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=pr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=pr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=pr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=pr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new ra(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ta(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(kl(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),kl(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},ca=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},mr=class{component;route;constructor(t,n){this.component=t,this.route=n}};function cT(e,t,n){let r=e._root,o=t?t._root:null;return yo(r,o,n,[r.value])}function uT(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Cr(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Lf(e)?e:t.get(e):r}function yo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=pr(t);return e.children.forEach(s=>{lT(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Do(a,n.getContext(s),o)),o}function lT(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=dT(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new ca(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?yo(e,t,a?a.children:null,r,o):yo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new mr(a.outlet.component,s))}else s&&Do(t,a,o),o.canActivateChecks.push(new ca(r)),i.component?yo(e,null,a?a.children:null,r,o):yo(e,null,n,r,o);return o}function dT(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!hn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!hn(e.url,t.url)||!Ke(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Hl(e,t)||!Ke(e.queryParams,t.queryParams);case"paramsChange":default:return!Hl(e,t)}}function Do(e,t,n){let r=pr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Do(s,t.children.getContext(i),n):Do(s,null,n):Do(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new mr(t.outlet.component,o)):n.canDeactivateChecks.push(new mr(null,o)):n.canDeactivateChecks.push(new mr(null,o))}function xo(e){return typeof e=="function"}function fT(e){return typeof e=="boolean"}function hT(e){return e&&xo(e.canLoad)}function pT(e){return e&&xo(e.canActivate)}function gT(e){return e&&xo(e.canActivateChild)}function mT(e){return e&&xo(e.canDeactivate)}function yT(e){return e&&xo(e.canMatch)}function zm(e){return e instanceof it||e?.name==="EmptyError"}var Us=Symbol("INITIAL_VALUE");function Ir(){return me(e=>Fr(e.map(t=>t.pipe(at(1),Wa(Us)))).pipe(P(t=>{for(let n of t)if(n!==!0){if(n===Us)return Us;if(n===!1||vT(n))return n}return!0}),oe(t=>t!==Us),at(1)))}function vT(e){return $t(e)||e instanceof Er}function DT(e,t){return Q(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(j(y({},n),{guardsResult:!0})):wT(s,r,o,e).pipe(Q(a=>a&&fT(a)?ET(r,i,e,t):C(a)),P(a=>j(y({},n),{guardsResult:a})))})}function wT(e,t,n,r){return $(e).pipe(Q(o=>TT(o.component,o.route,n,t,r)),ct(o=>o!==!0,!0))}function ET(e,t,n,r){return $(t).pipe(Ue(o=>Rn(CT(o.route.parent,r),IT(o.route,r),ST(e,o.path,n),bT(e,o.route,n))),ct(o=>o!==!0,!0))}function IT(e,t){return e!==null&&t&&t(new na(e)),C(!0)}function CT(e,t){return e!==null&&t&&t(new ea(e)),C(!0)}function bT(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>pi(()=>{let s=No(t)??n,a=Cr(i,s),c=pT(a)?a.canActivate(t,e):Ee(s,()=>a(t,e));return zt(c).pipe(ct())}));return C(o).pipe(Ir())}function ST(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>uT(s)).filter(s=>s!==null).map(s=>pi(()=>{let a=s.guards.map(c=>{let u=No(s.node)??n,l=Cr(c,u),f=gT(l)?l.canActivateChild(r,e):Ee(u,()=>l(r,e));return zt(f).pipe(ct())});return C(a).pipe(Ir())}));return C(i).pipe(Ir())}function TT(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=No(t)??o,u=Cr(a,c),l=mT(u)?u.canDeactivate(e,t,n,r):Ee(c,()=>u(e,t,n,r));return zt(l).pipe(ct())});return C(s).pipe(Ir())}function MT(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=Cr(s,e),c=hT(a)?a.canLoad(t,n):Ee(e,()=>a(t,n));return zt(c)});return C(i).pipe(Ir(),qm(r))}function qm(e){return La(Y(t=>{if(typeof t!="boolean")throw aa(e,t)}),P(t=>t===!0))}function _T(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=Cr(s,e),c=yT(a)?a.canMatch(t,n):Ee(e,()=>a(t,n));return zt(c)});return C(i).pipe(Ir(),qm(r))}var To=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Mo=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function hr(e){return Nn(new To(e))}function NT(e){return Nn(new v(4e3,!1))}function RT(e){return Nn($m(!1,Ce.GuardRejected))}var Gl=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[A])return NT(`${t.redirectTo}`);o=o.children[A]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,params:d,data:p,title:m}=o,E=Ee(i,()=>a({params:d,data:p,queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,title:m}));if(E instanceof Xe)throw new Mo(E);n=E}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Mo(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Xe(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new L(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Wl={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function xT(e,t,n,r,o){let i=Gm(e,t,n);return i.matched?(r=eT(t,r),_T(r,t,n,o).pipe(P(s=>s===!0?i:y({},Wl)))):C(i)}function Gm(e,t,n){if(t.path==="**")return AT(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?y({},Wl):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||Em)(n,e,t);if(!o)return y({},Wl);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?y(y({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function AT(e){return{matched:!0,parameters:e.length>0?Cm(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function vm(e,t,n,r){return n.length>0&&PT(e,n,r)?{segmentGroup:new L(t,kT(r,new L(n,e.children))),slicedSegments:[]}:n.length===0&&FT(e,n,r)?{segmentGroup:new L(e.segments,OT(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new L(e.segments,e.children),slicedSegments:n}}function OT(e,t,n,r){let o={};for(let i of n)if(la(e,t,i)&&!r[Ve(i)]){let s=new L([],{});o[Ve(i)]=s}return y(y({},r),o)}function kT(e,t){let n={};n[A]=t;for(let r of e)if(r.path===""&&Ve(r)!==A){let o=new L([],{});n[Ve(r)]=o}return n}function PT(e,t,n){return n.some(r=>la(e,t,r)&&Ve(r)!==A)}function FT(e,t,n){return n.some(r=>la(e,t,r))}function la(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function LT(e,t,n){return t.length===0&&!e.children[n]}var Zl=class{};function jT(e,t,n,r,o,i,s="emptyOnly"){return new Yl(e,t,n,r,o,s,i).recognize()}var VT=31,Yl=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Gl(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=vm(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(P(({children:n,rootSnapshot:r})=>{let o=new Se(r,n),i=new bo("",o),s=Am(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new pn([],Object.freeze({}),Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),A,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,A,n).pipe(P(r=>({children:r,rootSnapshot:n})),st(r=>{if(r instanceof Mo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof To?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(P(s=>s instanceof Se?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return $(i).pipe(Ue(s=>{let a=r.children[s],c=tT(n,s);return this.processSegmentGroup(t,c,a,s,o)}),za((s,a)=>(s.push(...a),s)),St(null),Ha(),Q(s=>{if(s===null)return hr(r);let a=Wm(s);return BT(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return $(n).pipe(Ue(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(st(u=>{if(u instanceof To)return C(null);throw u}))),ct(c=>!!c),st(c=>{if(zm(c))return LT(r,o,i)?C(new Zl):hr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Ve(r)!==s&&(s===A||!la(o,i,r))?hr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):hr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:f,remainingSegments:h}=Gm(n,o,i);if(!c)return hr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>VT&&(this.allowRedirects=!1));let d=new pn(i,u,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Dm(o),Ve(o),o.component??o._loadedComponent??null,o,wm(o)),p=sa(d,a,this.paramsInheritanceStrategy);d.params=Object.freeze(p.params),d.data=Object.freeze(p.data);let m=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,f,d,t);return this.applyRedirects.lineralizeSegments(o,m).pipe(Q(E=>this.processSegment(t,r,n,E.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=xT(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(me(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(me(({routes:u})=>{let l=r._loadedInjector??t,{parameters:f,consumedSegments:h,remainingSegments:d}=c,p=new pn(h,f,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Dm(r),Ve(r),r.component??r._loadedComponent??null,r,wm(r)),m=sa(p,s,this.paramsInheritanceStrategy);p.params=Object.freeze(m.params),p.data=Object.freeze(m.data);let{segmentGroup:E,slicedSegments:S}=vm(n,h,d,u);if(S.length===0&&E.hasChildren())return this.processChildren(l,u,E,p).pipe(P(W=>new Se(p,W)));if(u.length===0&&S.length===0)return C(new Se(p,[]));let J=Ve(r)===i;return this.processSegment(l,u,E,S,J?A:i,!0,p).pipe(P(W=>new Se(p,W instanceof Se?[W]:[])))}))):hr(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):MT(t,n,r,this.urlSerializer).pipe(Q(o=>o?this.configLoader.loadChildren(t,n).pipe(Y(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):RT(n))):C({routes:[],injector:t})}};function BT(e){e.sort((t,n)=>t.value.outlet===A?-1:n.value.outlet===A?1:t.value.outlet.localeCompare(n.value.outlet))}function UT(e){let t=e.value.routeConfig;return t&&t.path===""}function Wm(e){let t=[],n=new Set;for(let r of e){if(!UT(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Wm(r.children);t.push(new Se(r.value,o))}return t.filter(r=>!n.has(r))}function Dm(e){return e.data||{}}function wm(e){return e.resolve||{}}function $T(e,t,n,r,o,i){return Q(s=>jT(e,t,n,r,s.extractedUrl,o,i).pipe(P(({state:a,tree:c})=>j(y({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function HT(e,t){return Q(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Zm(c))s.add(u);let a=0;return $(s).pipe(Ue(c=>i.has(c)?zT(c,r,e,t):(c.data=sa(c,c.parent,e).resolve,C(void 0))),Y(()=>a++),xn(1),Q(c=>a===s.size?C(n):ie))})}function Zm(e){let t=e.children.map(n=>Zm(n)).flat();return[e,...t]}function zT(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Vm(o)&&(i[_o]=o.title),qT(i,e,t,r).pipe(P(s=>(e._resolvedData=s,e.data=sa(e,e.parent,n).resolve,null)))}function qT(e,t,n,r){let o=Ll(e);if(o.length===0)return C({});let i={};return $(o).pipe(Q(s=>GT(e[s],t,n,r).pipe(ct(),Y(a=>{if(a instanceof Er)throw aa(new Ut,a);i[s]=a}))),xn(1),P(()=>i),st(s=>zm(s)?ie:Nn(s)))}function GT(e,t,n,r){let o=No(t)??r,i=Cr(e,o),s=i.resolve?i.resolve(t,n):Ee(o,()=>i(t,n));return zt(s)}function Pl(e){return me(t=>{let n=e(t);return n?$(n).pipe(P(()=>t)):C(t)})}var td=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===A);return r}getResolvedTitleForRoute(n){return n.data[_o]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(Ym),providedIn:"root"})}return e})(),Ym=(()=>{class e extends td{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(hm))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vn=new w("",{providedIn:"root",factory:()=>({})}),Dn=new w(""),da=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=g(Tg);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=zt(n.loadComponent()).pipe(P(Km),Y(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Tt(()=>{this.componentLoaders.delete(n)})),o=new Mn(r,()=>new q).pipe(Tn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Qm(r,this.compiler,n,this.onLoadEndListener).pipe(Tt(()=>{this.childrenLoaders.delete(r)})),s=new Mn(i,()=>new q).pipe(Tn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Qm(e,t,n,r){return zt(e.loadChildren()).pipe(P(Km),Q(o=>o instanceof sl||Array.isArray(o)?C(o):$(t.compileModuleAsync(o))),P(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Dn,[],{optional:!0,self:!0}).flat()),{routes:s.map(ed),injector:i}}))}function WT(e){return e&&typeof e=="object"&&"default"in e}function Km(e){return WT(e)?e.default:e}var fa=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(ZT),providedIn:"root"})}return e})(),ZT=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),nd=new w(""),rd=new w("");function Jm(e,t,n){let r=e.get(rd),o=e.get(K);return e.get(H).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),YT(e))),{onViewTransitionCreated:c}=r;return c&&Ee(e,()=>c({transition:a,from:t,to:n})),s})}function YT(e){return new Promise(t=>{Uu({read:()=>setTimeout(t)},{injector:e})})}var od=new w(""),ha=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new q;transitionAbortSubject=new q;configLoader=g(da);environmentInjector=g(ae);destroyRef=g(kt);urlSerializer=g(mn);rootContexts=g(yn);location=g(Vt);inputBindingEnabled=g(Ro,{optional:!0})!==null;titleStrategy=g(td);options=g(vn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=g(fa);createViewTransition=g(nd,{optional:!0});navigationErrorHandler=g(od,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Js(o)),r=o=>this.events.next(new Xs(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(y({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new ee(null),this.transitions.pipe(oe(r=>r!==null),me(r=>{let o=!1,i=!1;return C(r).pipe(me(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Ce.SupersededByNewNavigation),ie;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?j(y({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new et(s.id,this.urlSerializer.serialize(s.rawUrl),u,yr.IgnoredSameUrlNavigation)),s.resolve(!1),ie}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(me(u=>(this.events.next(new Ht(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?ie:Promise.resolve(u))),$T(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Y(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=j(y({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new Eo(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:f,restoredState:h,extras:d}=s,p=new Ht(u,this.urlSerializer.serialize(l),f,h);this.events.next(p);let m=Lm(this.rootComponentType).snapshot;return this.currentTransition=r=j(y({},s),{targetSnapshot:m,urlAfterRedirects:l,extras:j(y({},d),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let u="";return this.events.next(new et(s.id,this.urlSerializer.serialize(s.extractedUrl),u,yr.IgnoredByUrlHandlingStrategy)),s.resolve(!1),ie}}),Y(s=>{let a=new Zs(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),P(s=>(this.currentTransition=r=j(y({},s),{guards:cT(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),DT(this.environmentInjector,s=>this.events.next(s)),Y(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw aa(this.urlSerializer,s.guardsResult);let a=new Ys(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),oe(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",Ce.GuardRejected),!1)),Pl(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(Y(a=>{let c=new Qs(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),me(a=>{let c=!1;return C(a).pipe(HT(this.paramsInheritanceStrategy,this.environmentInjector),Y({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",Ce.NoDataFromResolver)}}))}),Y(a=>{let c=new Ks(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),Pl(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(Y(l=>{c.component=l}),P(()=>{})));for(let l of c.children)u.push(...a(l));return u};return Fr(a(s.targetSnapshot.root)).pipe(St(null),at(1))}),Pl(()=>this.afterPreactivation()),me(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?$(c).pipe(P(()=>r)):C(r)}),P(s=>{let a=rT(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=j(y({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),Y(()=>{this.events.next(new Io)}),aT(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),at(1),Y({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new xe(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Za(this.transitionAbortSubject.pipe(Y(s=>{throw s}))),Tt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",Ce.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),st(s=>{if(this.destroyed)return r.resolve(!1),ie;if(i=!0,Hm(s))this.events.next(new Je(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),sT(s)?this.events.next(new wr(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new vr(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Ee(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Er){let{message:u,cancellationCode:l}=aa(this.urlSerializer,c);this.events.next(new Je(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new wr(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return ie}))}))}cancelNavigationTransition(n,r,o){let i=new Je(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function QT(e){return e!==qs}var Xm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(KT),providedIn:"root"})}return e})(),ua=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},KT=(()=>{class e extends ua{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Ou(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ey=(()=>{class e{urlSerializer=g(mn);options=g(vn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=g(Vt);urlHandlingStrategy=g(fa);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Xe;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Xe?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Lm(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>g(JT),providedIn:"root"})}return e})(),JT=(()=>{class e extends ey{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Ht?this.updateStateMemento():n instanceof et?this.commitTransition(r):n instanceof Eo?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Io?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Je&&(n.code===Ce.GuardRejected||n.code===Ce.NoDataFromResolver)?this.restoreHistory(r):n instanceof vr?this.restoreHistory(r,!0):n instanceof xe&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=y(y({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=y(y({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=Ou(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function pa(e,t){e.events.pipe(oe(n=>n instanceof xe||n instanceof Je||n instanceof vr||n instanceof et),P(n=>n instanceof xe||n instanceof et?0:(n instanceof Je?n.code===Ce.Redirect||n.code===Ce.SupersededByNewNavigation:!1)?2:1),oe(n=>n!==2),at(1)).subscribe(()=>{t()})}var XT={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},eM={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},nt=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=g(al);stateManager=g(ey);options=g(vn,{optional:!0})||{};pendingTasks=g(mt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=g(ha);urlSerializer=g(mn);location=g(Vt);urlHandlingStrategy=g(fa);_events=new q;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=g(Xm);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=g(Dn,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!g(Ro,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new Z;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Je&&r.code!==Ce.Redirect&&r.code!==Ce.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof xe)this.navigated=!0;else if(r instanceof wr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=y({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||QT(o.source)},s);this.scheduleNavigation(a,qs,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}nM(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),qs,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=y({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(ed),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=y(y({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let f;try{let h=o?o.snapshot:this.routerState.snapshot.root;f=Om(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return km(f,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=$t(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,qs,null,r)}navigate(n,r={skipLocationChange:!1}){return tM(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=y({},XT):r===!1?o=y({},eM):o=r,$t(n))return pm(this.currentUrlTree,n,o);let i=this.parseUrl(n);return pm(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((f,h)=>{a=f,c=h});let l=this.pendingTasks.add();return pa(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function tM(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}function nM(e){return!(e instanceof Io)&&!(e instanceof wr)}var ty=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new q;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof xe&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):($t(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:dp(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:$t(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(le(nt),le(tt),ls("tabindex"),le(eo),le(yt),le(je))};static \u0275dir=jt({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&dl("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&ul("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",oo],skipLocationChange:[2,"skipLocationChange","skipLocationChange",oo],replaceUrl:[2,"replaceUrl","replaceUrl",oo],routerLink:"routerLink"},features:[tr]})}return e})();var Ao=class{};var ny=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(oe(n=>n instanceof xe),Ue(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=to(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return $(o).pipe(bt())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=C(null);let i=o.pipe(Q(s=>s===null?C(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return $([i,s]).pipe(bt())}else return i})}static \u0275fac=function(r){return new(r||e)(I(nt),I(ae),I(Ao),I(da))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ry=new w(""),rM=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Ht?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof xe?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof et&&n.code===yr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Dr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Dr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Up()};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function oM(e,...t){return ln([{provide:Dn,multi:!0,useValue:e},[],{provide:tt,useFactory:oy,deps:[nt]},{provide:Es,multi:!0,useFactory:iy},t.map(n=>n.\u0275providers)])}function oy(e){return e.routerState.root}function Oo(e,t){return{\u0275kind:e,\u0275providers:t}}function iy(){let e=g(we);return t=>{let n=e.get(xt);if(t!==n.components[0])return;let r=e.get(nt),o=e.get(sy);e.get(sd)===1&&r.initialNavigation(),e.get(uy,null,O.Optional)?.setUpPreloading(),e.get(ry,null,O.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var sy=new w("",{factory:()=>new q}),sd=new w("",{providedIn:"root",factory:()=>1});function ay(){let e=[{provide:sd,useValue:0},cl(()=>{let t=g(we);return t.get(pl,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(nt),i=t.get(sy);pa(o,()=>{r(!0)}),t.get(ha).afterPreactivation=()=>(r(!0),i.closed?C(void 0):i),o.initialNavigation()}))})];return Oo(2,e)}function cy(){let e=[cl(()=>{g(nt).setUpLocationChangeListener()}),{provide:sd,useValue:2}];return Oo(3,e)}var uy=new w("");function ly(e){return Oo(0,[{provide:uy,useExisting:ny},{provide:Ao,useExisting:e}])}function dy(){return Oo(8,[Jl,{provide:Ro,useExisting:Jl}])}function fy(e){vt("NgRouterViewTransitions");let t=[{provide:nd,useValue:Jm},{provide:rd,useValue:y({skipNextTransition:!!e?.skipInitialTransition},e)}];return Oo(9,t)}var hy=[Vt,{provide:mn,useClass:Ut},nt,yn,{provide:tt,useFactory:oy,deps:[nt]},da,[]],iM=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[hy,[],{provide:Dn,multi:!0,useValue:n},[],r?.errorHandler?{provide:od,useValue:r.errorHandler}:[],{provide:vn,useValue:r||{}},r?.useHash?aM():cM(),sM(),r?.preloadingStrategy?ly(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?uM(r):[],r?.bindToComponentInputs?dy().\u0275providers:[],r?.enableViewTransitions?fy().\u0275providers:[],lM()]}}static forChild(n){return{ngModule:e,providers:[{provide:Dn,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Lt({type:e});static \u0275inj=At({})}return e})();function sM(){return{provide:ry,useFactory:()=>{let e=g(Bg),t=g(H),n=g(vn),r=g(ha),o=g(mn);return n.scrollOffset&&e.setOffset(n.scrollOffset),new rM(o,r,e,t,n)}}}function aM(){return{provide:je,useClass:ml}}function cM(){return{provide:je,useClass:bs}}function uM(e){return[e.initialNavigation==="disabled"?cy().\u0275providers:[],e.initialNavigation==="enabledBlocking"?ay().\u0275providers:[]]}var id=new w("");function lM(){return[{provide:id,useFactory:iy},{provide:Es,multi:!0,useExisting:id}]}var py={production:!1,apiUrl:"http://localhost:3000/api"};var ga=class e{constructor(t){this.http=t}apiUrl=py.apiUrl;getHeaders(){let t=localStorage.getItem("auth_token"),n=new Re({"Content-Type":"application/json"});return t&&(n=n.set("Authorization",`Bearer ${t}`)),n}get(t){return this.http.get(`${this.apiUrl}/${t}`,{headers:this.getHeaders()})}post(t,n){return this.http.post(`${this.apiUrl}/${t}`,n,{headers:this.getHeaders()})}put(t,n){return this.http.put(`${this.apiUrl}/${t}`,n,{headers:this.getHeaders()})}delete(t){return this.http.delete(`${this.apiUrl}/${t}`,{headers:this.getHeaders()})}static \u0275fac=function(n){return new(n||e)(I(js))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})};var gy=class e{constructor(t){this.apiService=t;this.loadUserFromStorage()}currentUserSubject=new ee(null);currentUser$=this.currentUserSubject.asObservable();tokenKey="auth_token";loadUserFromStorage(){let t=localStorage.getItem(this.tokenKey);if(t)try{let n=JSON.parse(atob(t.split(".")[1]));n.exp*1e3>Date.now()?this.getUserProfile(n.id).subscribe({error:()=>{this.logout()}}):this.logout()}catch{this.logout()}}login(t,n){return this.apiService.post("auth/login",{email:t,password:n}).pipe(Y(r=>{localStorage.setItem(this.tokenKey,r.token),this.currentUserSubject.next(r.user)}))}register(t,n,r,o="patient"){return this.apiService.post("auth/register",{name:t,email:n,password:r,role:o}).pipe(Y(i=>{localStorage.setItem(this.tokenKey,i.token),this.currentUserSubject.next(i.user)}))}logout(){localStorage.removeItem(this.tokenKey),this.currentUserSubject.next(null)}getUserProfile(t){let n=t;if(!n){let r=this.getToken();if(r)try{n=JSON.parse(atob(r.split(".")[1])).id}catch{throw new Error("Invalid token")}else throw new Error("No token found")}return this.apiService.get(`users/${n}`).pipe(Y(r=>{this.currentUserSubject.next(r)}))}isLoggedIn(){return!!localStorage.getItem(this.tokenKey)}getToken(){return localStorage.getItem(this.tokenKey)}static \u0275fac=function(n){return new(n||e)(I(ga))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})};export{y as a,j as b,Z as c,F as d,q as e,ee as f,Or as g,ie as h,$ as i,C as j,Nn as k,P as l,Fr as m,Rn as n,pi as o,Yy as p,Qy as q,oe as r,Ky as s,st as t,Jy as u,at as v,Xy as w,Tt as x,nv as y,Ga as z,rv as A,ov as B,Wa as C,me as D,Za as E,iv as F,Y as G,v as H,Pf as I,D as J,At as K,w as L,I as M,g as N,Tv as O,NO as P,kv as Q,ae as R,tr as S,RO as T,xO as U,AO as V,OO as W,Ou as X,we as Y,nf as Z,ve as _,H as $,Ge as aa,yt as ba,zD as ca,qD as da,Tc as ea,Fu as fa,ir as ga,kO as ha,ju as ia,iw as ja,Uu as ka,Ye as la,PO as ma,Wn as na,Yn as oa,eo as pa,le as qa,Up as ra,Ft as sa,HO as ta,Qn as ua,Xp as va,Lt as wa,jt as xa,HI as ya,JI as za,no as Aa,xt as Ba,ul as Ca,mC as Da,yC as Ea,vC as Fa,GO as Ga,WO as Ha,ZO as Ia,YO as Ja,QO as Ka,KO as La,vg as Ma,Dg as Na,ll as Oa,AC as Pa,JO as Qa,kC as Ra,dl as Sa,XO as Ta,ek as Ua,tk as Va,UC as Wa,nk as Xa,$C as Ya,HC as Za,rk as _a,ok as $a,ik as ab,sk as bb,qC as cb,Cg as db,GC as eb,WC as fb,ak as gb,ZC as hb,QC as ib,ck as jb,uk as kb,lk as lb,dk as mb,fk as nb,hk as ob,fl as pb,ro as qb,oo as rb,Db as sb,wb as tb,pk as ub,Eb as vb,gk as wb,K as xb,wt as yb,Vt as zb,Nb as Ab,Rb as Bb,xb as Cb,kb as Db,jg as Eb,Pb as Fb,Kb as Gb,js as Hb,dm as Ib,bS as Jb,SS as Kb,MS as Lb,Kl as Mb,nt as Nb,ty as Ob,oM as Pb,iM as Qb,gy as Rb};
