const prismaService = require('./prisma.service');
const bcrypt = require('bcrypt');

class UserService {
  constructor() {
    this.prisma = prismaService.getClient();
  }

  // Get all users
  async getAllUsers() {
    try {
      const users = await this.prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true
        }
      });
      return users;
    } catch (error) {
      console.error('Error getting all users:', error);
      throw error;
    }
  }

  // Get user by ID
  async getUserById(id) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: parseInt(id) },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true,
          updatedAt: true
        }
      });
      return user;
    } catch (error) {
      console.error(`Error getting user with ID ${id}:`, error);
      throw error;
    }
  }

  // Create a new user
  async createUser(userData) {
    try {
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      const user = await this.prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword
        }
      });
      
      // Remove password from the returned user object
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Update a user
  async updateUser(id, userData) {
    try {
      // If password is being updated, hash it
      if (userData.password) {
        userData.password = await bcrypt.hash(userData.password, 10);
      }
      
      const user = await this.prisma.user.update({
        where: { id: parseInt(id) },
        data: userData
      });
      
      // Remove password from the returned user object
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }

  // Delete a user
  async deleteUser(id) {
    try {
      await this.prisma.user.delete({
        where: { id: parseInt(id) }
      });
      return { success: true };
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  }

  // Find user by email
  async findUserByEmail(email) {
    try {
      return await this.prisma.user.findUnique({
        where: { email }
      });
    } catch (error) {
      console.error(`Error finding user with email ${email}:`, error);
      throw error;
    }
  }
}

module.exports = new UserService();
