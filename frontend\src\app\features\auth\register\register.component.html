<div class="register-container">
  <div class="register-card">
    <h2>HMS2025 Registration</h2>
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="name">Full Name</label>
        <input
          type="text"
          id="name"
          formControlName="name"
          placeholder="Enter your full name"
          [ngClass]="{'invalid': registerForm.get('name')?.invalid && registerForm.get('name')?.touched}"
        >
        @if (registerForm.get('name')?.invalid && registerForm.get('name')?.touched) {
          <div class="error-message">
            @if (registerForm.get('name')?.errors?.['required']) {
              <span>Name is required</span>
            } @else if (registerForm.get('name')?.errors?.['minlength']) {
              <span>Name must be at least 3 characters</span>
            }
          </div>
        }
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Enter your email"
          [ngClass]="{'invalid': registerForm.get('email')?.invalid && registerForm.get('email')?.touched}"
        >
        @if (registerForm.get('email')?.invalid && registerForm.get('email')?.touched) {
          <div class="error-message">
            @if (registerForm.get('email')?.errors?.['required']) {
              <span>Email is required</span>
            } @else if (registerForm.get('email')?.errors?.['email']) {
              <span>Please enter a valid email</span>
            }
          </div>
        }
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [ngClass]="{'invalid': registerForm.get('password')?.invalid && registerForm.get('password')?.touched}"
        >
        @if (registerForm.get('password')?.invalid && registerForm.get('password')?.touched) {
          <div class="error-message">
            @if (registerForm.get('password')?.errors?.['required']) {
              <span>Password is required</span>
            } @else if (registerForm.get('password')?.errors?.['minlength']) {
              <span>Password must be at least 6 characters</span>
            }
          </div>
        }
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input
          type="password"
          id="confirmPassword"
          formControlName="confirmPassword"
          placeholder="Confirm your password"
          [ngClass]="{'invalid': registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched}"
        >
        @if (registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) {
          <div class="error-message">
            @if (registerForm.get('confirmPassword')?.errors?.['required']) {
              <span>Please confirm your password</span>
            } @else if (registerForm.get('confirmPassword')?.errors?.['passwordMismatch']) {
              <span>Passwords do not match</span>
            }
          </div>
        }
      </div>

      @if (errorMessage) {
        <div class="alert alert-danger">
          {{ errorMessage }}
        </div>
      }

      <button
        type="submit"
        [disabled]="registerForm.invalid || isLoading"
        class="register-button"
      >
        @if (isLoading) {
          <span>Registering...</span>
        } @else {
          <span>Register</span>
        }
      </button>

      <div class="login-link">
        <p>Already have an account? <a routerLink="/auth/login">Login</a></p>
      </div>
    </form>
  </div>
</div>
