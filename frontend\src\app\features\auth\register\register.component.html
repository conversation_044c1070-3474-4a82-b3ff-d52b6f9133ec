<div class="register-container">
  <div class="register-card">
    <h2>HMS2025 Registration</h2>
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="name">Full Name</label>
        <input
          type="text"
          id="name"
          formControlName="name"
          placeholder="Enter your full name"
          [class.invalid]="registerForm.get('name')?.invalid && registerForm.get('name')?.touched"
        >
        <div *ngIf="registerForm.get('name')?.invalid && registerForm.get('name')?.touched" class="error-message">
          <span *ngIf="registerForm.get('name')?.errors?.['required']">Name is required</span>
          <span *ngIf="registerForm.get('name')?.errors?.['minlength']">Name must be at least 3 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Enter your email"
          [class.invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
        >
        <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched" class="error-message">
          <span *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [class.invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
        >
        <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" class="error-message">
          <span *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <input
          type="password"
          id="confirmPassword"
          formControlName="confirmPassword"
          placeholder="Confirm your password"
          [class.invalid]="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched"
        >
        <div *ngIf="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched" class="error-message">
          <span *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Please confirm your password</span>
          <span *ngIf="registerForm.get('confirmPassword')?.errors?.['passwordMismatch']">Passwords do not match</span>
        </div>
      </div>

      <div *ngIf="errorMessage" class="alert alert-danger">
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        [disabled]="registerForm.invalid || isLoading"
        class="register-button"
      >
        <span *ngIf="isLoading">Registering...</span>
        <span *ngIf="!isLoading">Register</span>
      </button>

      <div class="login-link">
        <p>Already have an account? <a routerLink="/auth/login">Login</a></p>
      </div>
    </form>
  </div>
</div>
