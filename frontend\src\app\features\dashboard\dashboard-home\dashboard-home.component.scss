.dashboard-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f8f9fa;
}

.welcome-section {
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  h1 {
    color: #333;
    margin-bottom: 20px;
  }

  .user-info {
    p {
      margin: 8px 0;
      color: #666;
    }

    .role-badge {
      background-color: #007bff;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }
  }
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .card {
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    h3 {
      color: #333;
      margin-bottom: 10px;
    }

    p {
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }
  }
}

.actions {
  text-align: center;
  padding: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;

  &.btn-primary {
    background-color: #007bff;
    color: white;

    &:hover {
      background-color: #0069d9;
    }
  }

  &.btn-danger {
    background-color: #dc3545;
    color: white;

    &:hover {
      background-color: #c82333;
    }
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 15px;
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
  }

  .welcome-section {
    padding: 20px;
  }
}
