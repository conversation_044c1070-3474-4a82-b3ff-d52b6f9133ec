.footer {
  background-color: #343a40;
  color: white;
  padding: 40px 0 20px;
  margin-top: auto;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;

    .footer-section {
      h4 {
        color: #007bff;
        margin-bottom: 15px;
        font-size: 18px;
      }

      p {
        color: #adb5bd;
        line-height: 1.6;
        margin-bottom: 10px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          margin-bottom: 8px;

          a {
            color: #adb5bd;
            text-decoration: none;
            transition: color 0.3s;

            &:hover {
              color: #007bff;
            }
          }
        }
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid #495057;
    padding-top: 20px;
    text-align: center;

    p {
      color: #adb5bd;
      margin: 0;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .footer {
    .footer-content {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
}
