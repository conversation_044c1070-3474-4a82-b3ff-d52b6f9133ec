<div class="dashboard-container">
  <app-header></app-header>

  <main class="main-content">
    <div class="welcome-section">
      <h1>Welcome to HMS2025 Dashboard</h1>
      <div *ngIf="currentUser" class="user-info">
        <p>Hello, <strong>{{ currentUser.name }}</strong>!</p>
        <p>Role: <span class="role-badge">{{ currentUser.role | titlecase }}</span></p>
        <p>Email: {{ currentUser.email }}</p>
      </div>
    </div>

    <div class="dashboard-cards">
      <div class="card">
        <h3>Patients</h3>
        <p>Manage patient records and information</p>
        <button class="btn btn-primary">View Patients</button>
      </div>

      <div class="card">
        <h3>Appointments</h3>
        <p>Schedule and manage appointments</p>
        <button class="btn btn-primary">View Appointments</button>
      </div>

      <div class="card">
        <h3>Doctors</h3>
        <p>Manage doctor profiles and schedules</p>
        <button class="btn btn-primary">View Doctors</button>
      </div>

      <div class="card">
        <h3>Reports</h3>
        <p>Generate and view medical reports</p>
        <button class="btn btn-primary">View Reports</button>
      </div>
    </div>

    <div class="actions">
      <button class="btn btn-danger" (click)="logout()">Logout</button>
    </div>
  </main>

  <app-footer></app-footer>
</div>
