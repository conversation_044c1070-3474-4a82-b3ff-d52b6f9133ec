const express = require('express');
const cors = require('cors');
require('dotenv').config();
const { users } = require('./data/mockData');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('Using mock data for development');

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to HMS2025 API' });
});

// API routes will be added here
app.use('/api', require('./routes/index'));

// Start server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
