const express = require('express');
const cors = require('cors');
require('dotenv').config();
const prismaService = require('./services/prisma.service');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Connect to the database
prismaService.connect()
  .then(() => {
    console.log('Connected to the database');
  })
  .catch((error) => {
    console.error('Failed to connect to the database:', error);
    process.exit(1);
  });

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await prismaService.disconnect();
  console.log('Disconnected from the database');
  process.exit(0);
});

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to HMS2025 API' });
});

// API routes will be added here
app.use('/api', require('./routes/index'));

// Start server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
