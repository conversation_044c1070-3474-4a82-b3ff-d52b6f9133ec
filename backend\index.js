const express = require('express');
const cors = require('cors');
require('dotenv').config();
const prismaService = require('./services/prisma.service');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
// Serve static files
app.use(express.static(path.join(__dirname, 'dist', 'browser')));

// Connect to the database
prismaService.connect()
  .then(() => {
    console.log('Connected to the database');
  })
  .catch((error) => {
    console.error('Failed to connect to the database:', error);
    process.exit(1);
  });

// Handle graceful shutdown
process.on('SIGINT', async () => {
  await prismaService.disconnect();
  console.log('Disconnected from the database');
  process.exit(0);
});


// Route for your app
app.get('/my', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'browser', 'index.html'));
});

// API routes will be added here
app.use('/api', require('./routes/index'));

// Start server
const server = app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

// Keep the process alive
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

module.exports = app;
