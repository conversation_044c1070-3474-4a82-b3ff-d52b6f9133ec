import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, User } from '../../../core/services/auth.service';

@Component({
  selector: 'app-dashboard-home',
  standalone: false,
  templateUrl: './dashboard-home.component.html',
  styleUrl: './dashboard-home.component.scss'
})
export class DashboardHomeComponent implements OnInit {
  currentUser: User | null = null;

  stats = [
    {
      title: 'Total Patients',
      value: '1,234',
      change: '+12%',
      changeType: 'increase',
      icon: 'users'
    },
    {
      title: 'Appointments Today',
      value: '56',
      change: '+8%',
      changeType: 'increase',
      icon: 'calendar'
    },
    {
      title: 'Available Doctors',
      value: '24',
      change: '-2%',
      changeType: 'decrease',
      icon: 'user-md'
    },
    {
      title: 'Revenue This Month',
      value: '$45,678',
      change: '+15%',
      changeType: 'increase',
      icon: 'dollar'
    }
  ];

  recentAppointments = [
    {
      id: 1,
      patient: '<PERSON>',
      doctor: 'Dr<PERSON> <PERSON>',
      time: '10:00 AM',
      status: 'confirmed'
    },
    {
      id: 2,
      patient: '<PERSON>',
      doctor: 'Dr. <PERSON>',
      time: '11:30 AM',
      status: 'pending'
    },
    {
      id: 3,
      patient: 'Mike <PERSON>',
      doctor: 'Dr. Davis',
      time: '2:00 PM',
      status: 'completed'
    }
  ];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }
}
