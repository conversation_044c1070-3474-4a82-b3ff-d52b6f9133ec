const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  try {
    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10);
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Admin User',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'admin'
      }
    });
    console.log('Admin user created:', admin.id);

    // Create doctor user
    const doctorPassword = await bcrypt.hash('doctor123', 10);
    const doctor = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Doctor User',
        email: '<EMAIL>',
        password: doctor<PERSON>assword,
        role: 'doctor',
        doctor: {
          create: {
            specialization: 'Cardiology'
          }
        }
      }
    });
    console.log('Doctor user created:', doctor.id);

    // Create patient user
    const patientPassword = await bcrypt.hash('patient123', 10);
    const patient = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Patient User',
        email: '<EMAIL>',
        password: patientPassword,
        role: 'patient',
        patient: {
          create: {
            dateOfBirth: new Date('1990-01-01'),
            bloodGroup: 'O+',
            doctorId: doctor.id
          }
        }
      }
    });
    console.log('Patient user created:', patient.id);

    console.log('Seed completed successfully');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
