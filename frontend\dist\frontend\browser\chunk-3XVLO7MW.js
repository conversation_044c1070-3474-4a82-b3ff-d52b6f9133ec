var rg=Object.defineProperty,og=Object.defineProperties;var ig=Object.getOwnPropertyDescriptors;var Ru=Object.getOwnPropertySymbols;var sg=Object.prototype.hasOwnProperty,ag=Object.prototype.propertyIsEnumerable;var xu=(e,t,n)=>t in e?rg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||={})sg.call(t,n)&&xu(e,n,t[n]);if(Ru)for(var n of Ru(t))ag.call(t,n)&&xu(e,n,t[n]);return e},V=(e,t)=>og(e,ig(t));var Zn=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(u){o(u)}},s=c=>{try{a(n.throw(c))}catch(u){o(u)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function ws(e,t){return Object.is(e,t)}var Y=null,Xr=!1,Is=1,be=Symbol("SIGNAL");function N(e){let t=Y;return Y=e,t}function Cs(){return Y}var Qn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Kn(e){if(Xr)throw new Error("");if(Y===null)return;Y.consumerOnSignalRead(e);let t=Y.nextProducerIndex++;if(ro(Y),t<Y.producerNode.length&&Y.producerNode[t]!==e&&Yn(Y)){let n=Y.producerNode[t];no(n,Y.producerIndexOfThis[t])}Y.producerNode[t]!==e&&(Y.producerNode[t]=e,Y.producerIndexOfThis[t]=Yn(Y)?Ou(e,Y,t):0),Y.producerLastReadVersion[t]=e.version}function Au(){Is++}function bs(e){if(!(Yn(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Is)){if(!e.producerMustRecompute(e)&&!_s(e)){Es(e);return}e.producerRecomputeValue(e),Es(e)}}function Ss(e){if(e.liveConsumerNode===void 0)return;let t=Xr;Xr=!0;try{for(let n of e.liveConsumerNode)n.dirty||cg(n)}finally{Xr=t}}function Ts(){return Y?.consumerAllowSignalWrites!==!1}function cg(e){e.dirty=!0,Ss(e),e.consumerMarkedDirty?.(e)}function Es(e){e.dirty=!1,e.lastCleanEpoch=Is}function to(e){return e&&(e.nextProducerIndex=0),N(e)}function Ms(e,t){if(N(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Yn(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)no(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function _s(e){ro(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(bs(n),r!==n.version))return!0}return!1}function Ns(e){if(ro(e),Yn(e))for(let t=0;t<e.producerNode.length;t++)no(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Ou(e,t,n){if(ku(e),e.liveConsumerNode.length===0&&Pu(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Ou(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function no(e,t){if(ku(e),e.liveConsumerNode.length===1&&Pu(e))for(let r=0;r<e.producerNode.length;r++)no(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];ro(o),o.producerIndexOfThis[r]=t}}function Yn(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ro(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ku(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Pu(e){return e.producerNode!==void 0}function Rs(e,t){let n=Object.create(ug);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(bs(n),Kn(n),n.value===eo)throw n.error;return n.value};return r[be]=n,r}var vs=Symbol("UNSET"),Ds=Symbol("COMPUTING"),eo=Symbol("ERRORED"),ug=V(m({},Qn),{value:vs,dirty:!0,error:null,equal:ws,kind:"computed",producerMustRecompute(e){return e.value===vs||e.value===Ds},producerRecomputeValue(e){if(e.value===Ds)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Ds;let n=to(e),r,o=!1;try{r=e.computation(),N(null),o=t!==vs&&t!==eo&&r!==eo&&e.equal(t,r)}catch(i){r=eo,e.error=i}finally{Ms(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function lg(){throw new Error}var Fu=lg;function Lu(e){Fu(e)}function xs(e){Fu=e}var dg=null;function As(e,t){let n=Object.create(oo);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Kn(n),n.value);return r[be]=n,r}function Jn(e,t){Ts()||Lu(e),e.equal(e.value,t)||(e.value=t,fg(e))}function Os(e,t){Ts()||Lu(e),Jn(e,t(e.value))}var oo=V(m({},Qn),{equal:ws,value:void 0,kind:"signal"});function fg(e){e.version++,Au(),Ss(e),dg?.()}function ks(e){let t=N(null);try{return e()}finally{N(t)}}var Ps;function Xn(){return Ps}function Qe(e){let t=Ps;return Ps=e,t}var io=Symbol("NotFound");function b(e){return typeof e=="function"}function Qt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var so=Qt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function er(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var G=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(b(r))try{r()}catch(i){t=i instanceof so?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{ju(i)}catch(s){t=t??[],s instanceof so?t=[...t,...s.errors]:t.push(s)}}if(t)throw new so(t)}}add(t){var n;if(t&&t!==this)if(this.closed)ju(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&er(n,t)}remove(t){let{_finalizers:n}=this;n&&er(n,t),t instanceof e&&t._removeParent(this)}};G.EMPTY=(()=>{let e=new G;return e.closed=!0,e})();var Fs=G.EMPTY;function ao(e){return e instanceof G||e&&"closed"in e&&b(e.remove)&&b(e.add)&&b(e.unsubscribe)}function ju(e){b(e)?e():e.unsubscribe()}var Se={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Kt={setTimeout(e,t,...n){let{delegate:r}=Kt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Kt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function co(e){Kt.setTimeout(()=>{let{onUnhandledError:t}=Se;if(t)t(e);else throw e})}function tr(){}var Vu=Ls("C",void 0,void 0);function Bu(e){return Ls("E",void 0,e)}function Uu(e){return Ls("N",e,void 0)}function Ls(e,t,n){return{kind:e,value:t,error:n}}var St=null;function Jt(e){if(Se.useDeprecatedSynchronousErrorHandling){let t=!St;if(t&&(St={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=St;if(St=null,n)throw r}}else e()}function $u(e){Se.useDeprecatedSynchronousErrorHandling&&St&&(St.errorThrown=!0,St.error=e)}var Tt=class extends G{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,ao(t)&&t.add(this)):this.destination=vg}static create(t,n,r){return new Xt(t,n,r)}next(t){this.isStopped?Vs(Uu(t),this):this._next(t)}error(t){this.isStopped?Vs(Bu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Vs(Vu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},mg=Function.prototype.bind;function js(e,t){return mg.call(e,t)}var Bs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){uo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){uo(r)}else uo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){uo(n)}}},Xt=class extends Tt{constructor(t,n,r){super();let o;if(b(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Se.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&js(t.next,i),error:t.error&&js(t.error,i),complete:t.complete&&js(t.complete,i)}):o=t}this.destination=new Bs(o)}};function uo(e){Se.useDeprecatedSynchronousErrorHandling?$u(e):co(e)}function yg(e){throw e}function Vs(e,t){let{onStoppedNotification:n}=Se;n&&Kt.setTimeout(()=>n(e,t))}var vg={closed:!0,next:tr,error:yg,complete:tr};var en=typeof Symbol=="function"&&Symbol.observable||"@@observable";function fe(e){return e}function Us(...e){return $s(e)}function $s(e){return e.length===0?fe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var k=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Eg(n)?n:new Xt(n,r,o);return Jt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Hu(r),new r((o,i)=>{let s=new Xt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[en](){return this}pipe(...n){return $s(n)(this)}toPromise(n){return n=Hu(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Hu(e){var t;return(t=e??Se.Promise)!==null&&t!==void 0?t:Promise}function Dg(e){return e&&b(e.next)&&b(e.error)&&b(e.complete)}function Eg(e){return e&&e instanceof Tt||Dg(e)&&ao(e)}function Hs(e){return b(e?.lift)}function P(e){return t=>{if(Hs(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function R(e,t,n,r,o){return new zs(e,t,n,r,o)}var zs=class extends Tt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function tn(){return P((e,t)=>{let n=null;e._refCount++;let r=R(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var nn=class extends k{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Hs(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new G;let n=this.getSubject();t.add(this.source.subscribe(R(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=G.EMPTY)}return t}refCount(){return tn()(this)}};var zu=Qt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var J=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new lo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new zu}next(n){Jt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Jt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Jt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Fs:(this.currentObservers=null,i.push(n),new G(()=>{this.currentObservers=null,er(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new k;return n.source=this,n}}return e.create=(t,n)=>new lo(t,n),e})(),lo=class extends J{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Fs}};var Q=class extends J{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var oe=new k(e=>e.complete());function qu(e){return e&&b(e.schedule)}function Gu(e){return e[e.length-1]}function fo(e){return b(Gu(e))?e.pop():void 0}function dt(e){return qu(Gu(e))?e.pop():void 0}function Zu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(f){s(f)}}function c(l){try{u(r.throw(l))}catch(f){s(f)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function Wu(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Mt(e){return this instanceof Mt?(this.v=e,this):new Mt(e)}function Yu(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(g){return Promise.resolve(g).then(d,f)}}function a(d,g){r[d]&&(o[d]=function(y){return new Promise(function(I,x){i.push([d,y,I,x])>1||c(d,y)})},g&&(o[d]=g(o[d])))}function c(d,g){try{u(r[d](g))}catch(y){h(i[0][3],y)}}function u(d){d.value instanceof Mt?Promise.resolve(d.value.v).then(l,f):h(i[0][2],d)}function l(d){c("next",d)}function f(d){c("throw",d)}function h(d,g){d(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Qu(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Wu=="function"?Wu(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var ho=e=>e&&typeof e.length=="number"&&typeof e!="function";function po(e){return b(e?.then)}function go(e){return b(e[en])}function mo(e){return Symbol.asyncIterator&&b(e?.[Symbol.asyncIterator])}function yo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function wg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var vo=wg();function Do(e){return b(e?.[vo])}function Eo(e){return Yu(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Mt(n.read());if(o)return yield Mt(void 0);yield yield Mt(r)}}finally{n.releaseLock()}})}function wo(e){return b(e?.getReader)}function W(e){if(e instanceof k)return e;if(e!=null){if(go(e))return Ig(e);if(ho(e))return Cg(e);if(po(e))return bg(e);if(mo(e))return Ku(e);if(Do(e))return Sg(e);if(wo(e))return Tg(e)}throw yo(e)}function Ig(e){return new k(t=>{let n=e[en]();if(b(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Cg(e){return new k(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function bg(e){return new k(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,co)})}function Sg(e){return new k(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Ku(e){return new k(t=>{Mg(e,t).catch(n=>t.error(n))})}function Tg(e){return Ku(Eo(e))}function Mg(e,t){var n,r,o,i;return Zu(this,void 0,void 0,function*(){try{for(n=Qu(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ie(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Io(e,t=0){return P((n,r)=>{n.subscribe(R(r,o=>ie(r,e,()=>r.next(o),t),()=>ie(r,e,()=>r.complete(),t),o=>ie(r,e,()=>r.error(o),t)))})}function Co(e,t=0){return P((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ju(e,t){return W(e).pipe(Co(t),Io(t))}function Xu(e,t){return W(e).pipe(Co(t),Io(t))}function el(e,t){return new k(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function tl(e,t){return new k(n=>{let r;return ie(n,t,()=>{r=e[vo](),ie(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>b(r?.return)&&r.return()})}function bo(e,t){if(!e)throw new Error("Iterable cannot be null");return new k(n=>{ie(n,t,()=>{let r=e[Symbol.asyncIterator]();ie(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function nl(e,t){return bo(Eo(e),t)}function rl(e,t){if(e!=null){if(go(e))return Ju(e,t);if(ho(e))return el(e,t);if(po(e))return Xu(e,t);if(mo(e))return bo(e,t);if(Do(e))return tl(e,t);if(wo(e))return nl(e,t)}throw yo(e)}function B(e,t){return t?rl(e,t):W(e)}function C(...e){let t=dt(e);return B(e,t)}function rn(e,t){let n=b(e)?e:()=>e,r=o=>o.error(n());return new k(t?o=>t.schedule(r,0,o):r)}function qs(e){return!!e&&(e instanceof k||b(e.lift)&&b(e.subscribe))}var Ke=Qt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function _(e,t){return P((n,r)=>{let o=0;n.subscribe(R(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:_g}=Array;function Ng(e,t){return _g(t)?e(...t):e(t)}function So(e){return _(t=>Ng(e,t))}var{isArray:Rg}=Array,{getPrototypeOf:xg,prototype:Ag,keys:Og}=Object;function To(e){if(e.length===1){let t=e[0];if(Rg(t))return{args:t,keys:null};if(kg(t)){let n=Og(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function kg(e){return e&&typeof e=="object"&&xg(e)===Ag}function Mo(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function nr(...e){let t=dt(e),n=fo(e),{args:r,keys:o}=To(e);if(r.length===0)return B([],t);let i=new k(Pg(r,t,o?s=>Mo(o,s):fe));return n?i.pipe(So(n)):i}function Pg(e,t,n=fe){return r=>{ol(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ol(t,()=>{let u=B(e[c],t),l=!1;u.subscribe(R(r,f=>{i[c]=f,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ol(e,t,n){e?ie(n,e,t):t()}function il(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,f=!1,h=()=>{f&&!c.length&&!u&&t.complete()},d=y=>u<r?g(y):c.push(y),g=y=>{i&&t.next(y),u++;let I=!1;W(n(y,l++)).subscribe(R(t,x=>{o?.(x),i?d(x):t.next(x)},()=>{I=!0},void 0,()=>{if(I)try{for(u--;c.length&&u<r;){let x=c.shift();s?ie(t,s,()=>g(x)):g(x)}h()}catch(x){t.error(x)}}))};return e.subscribe(R(t,d,()=>{f=!0,h()})),()=>{a?.()}}function q(e,t,n=1/0){return b(t)?q((r,o)=>_((i,s)=>t(r,i,o,s))(W(e(r,o))),n):(typeof t=="number"&&(n=t),P((r,o)=>il(r,o,e,n)))}function on(e=1/0){return q(fe,e)}function sl(){return on(1)}function sn(...e){return sl()(B(e,dt(e)))}function _o(e){return new k(t=>{W(e()).subscribe(t)})}function Fg(...e){let t=fo(e),{args:n,keys:r}=To(e),o=new k(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let f=!1;W(n[l]).subscribe(R(i,h=>{f||(f=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!f)&&(u||i.next(r?Mo(r,a):a),i.complete())}))}});return t?o.pipe(So(t)):o}function te(e,t){return P((n,r)=>{let o=0;n.subscribe(R(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Je(e){return P((t,n)=>{let r=null,o=!1,i;r=t.subscribe(R(n,void 0,void 0,s=>{i=W(e(s,Je(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function al(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(R(s,l=>{let f=u++;c=a?e(c,l,f):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ke(e,t){return b(t)?q(e,t,1):q(e,1)}function ft(e){return P((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Xe(e){return e<=0?()=>oe:P((t,n)=>{let r=0;t.subscribe(R(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function No(e=Lg){return P((t,n)=>{let r=!1;t.subscribe(R(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Lg(){return new Ke}function ht(e){return P((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function et(e,t){let n=arguments.length>=2;return r=>r.pipe(e?te((o,i)=>e(o,i,r)):fe,Xe(1),n?ft(t):No(()=>new Ke))}function an(e){return e<=0?()=>oe:P((t,n)=>{let r=[];t.subscribe(R(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Gs(e,t){let n=arguments.length>=2;return r=>r.pipe(e?te((o,i)=>e(o,i,r)):fe,an(1),n?ft(t):No(()=>new Ke))}function Ws(e,t){return P(al(e,t,arguments.length>=2,!0))}function Zs(...e){let t=dt(e);return P((n,r)=>{(t?sn(e,n,t):sn(e,n)).subscribe(r)})}function se(e,t){return P((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(R(r,c=>{o?.unsubscribe();let u=0,l=i++;W(e(c,l)).subscribe(o=R(r,f=>r.next(t?t(c,f,l,u++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Ys(e){return P((t,n)=>{W(e).subscribe(R(n,()=>n.complete(),tr)),!n.closed&&t.subscribe(n)})}function z(e,t,n){let r=b(e)||t||n?{next:e,error:t,complete:n}:e;return r?P((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(R(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):fe}var Zl="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(Yl(t,n)),this.code=t}};function jg(e){return`NG0${Math.abs(e)}`}function Yl(e,t){return`${jg(e)}${t?": "+t:""}`}var Ql=Symbol("InputSignalNode#UNSET"),Vg=V(m({},oo),{transformFn:void 0,applyValueToInputSignal(e,t){Jn(e,t)}});function Kl(e,t){let n=Object.create(Vg);n.value=e,n.transformFn=t?.transform;function r(){if(Kn(n),n.value===Ql){let o=null;throw new v(-950,o)}return n.value}return r[be]=n,r}function ui(e){return{toString:e}.toString()}var ua=globalThis;function j(e){for(let t in e)if(e[t]===j)return t;throw Error("Could not find renamed property on target object.")}function Bg(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ue(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ue).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function cl(e,t){return e?t?`${e} ${t}`:e:t||""}var Ug=j({__forward_ref__:j});function Jl(e){return e.__forward_ref__=Jl,e.toString=function(){return ue(this())},e}function ne(e){return Xl(e)?e():e}function Xl(e){return typeof e=="function"&&e.hasOwnProperty(Ug)&&e.__forward_ref__===Jl}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function mt(e){return{providers:e.providers||[],imports:e.imports||[]}}function li(e){return ul(e,td)||ul(e,nd)}function ed(e){return li(e)!==null}function ul(e,t){return e.hasOwnProperty(t)?e[t]:null}function $g(e){let t=e&&(e[td]||e[nd]);return t||null}function ll(e){return e&&(e.hasOwnProperty(dl)||e.hasOwnProperty(Hg))?e[dl]:null}var td=j({\u0275prov:j}),dl=j({\u0275inj:j}),nd=j({ngInjectableDef:j}),Hg=j({ngInjectorDef:j}),E=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=D({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function rd(e){return e&&!!e.\u0275providers}var zg=j({\u0275cmp:j}),qg=j({\u0275dir:j}),Gg=j({\u0275pipe:j}),Wg=j({\u0275mod:j}),Lo=j({\u0275fac:j}),sr=j({__NG_ELEMENT_ID__:j}),fl=j({__NG_ENV_ID__:j});function di(e){return typeof e=="string"?e:e==null?"":String(e)}function Zg(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():di(e)}function od(e,t){throw new v(-200,e)}function Ga(e,t){throw new v(-201,!1)}var M=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(M||{}),la;function id(){return la}function ae(e){let t=la;return la=e,t}function sd(e,t,n){let r=li(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&M.Optional)return null;if(t!==void 0)return t;Ga(e,"Injector")}var Yg={},_t=Yg,Qg="__NG_DI_FLAG__",jo=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?io:_t,r)}},Vo="ngTempTokenPath",Kg="ngTokenPath",Jg=/\n/gm,Xg="\u0275",hl="__source";function em(e,t=M.Default){if(Xn()===void 0)throw new v(-203,!1);if(Xn()===null)return sd(e,void 0,t);{let n=Xn(),r;return n instanceof jo?r=n.injector:r=n,r.get(e,t&M.Optional?null:void 0,t)}}function w(e,t=M.Default){return(id()||em)(ne(e),t)}function p(e,t=M.Default){return w(e,fi(t))}function fi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function da(e){let t=[];for(let n=0;n<e.length;n++){let r=ne(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=M.Default;for(let s=0;s<r.length;s++){let a=r[s],c=tm(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(w(o,i))}else t.push(w(r))}return t}function tm(e){return e[Qg]}function nm(e,t,n,r){let o=e[Vo];throw t[hl]&&o.unshift(t[hl]),e.message=rm(`
`+e.message,o,n,r),e[Kg]=o,e[Vo]=null,e}function rm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Xg?e.slice(2):e;let o=ue(t);if(Array.isArray(t))o=t.map(ue).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ue(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Jg,`
  `)}`}function Rt(e,t){let n=e.hasOwnProperty(Lo);return n?e[Lo]:null}function Wa(e,t){e.forEach(n=>Array.isArray(n)?Wa(n,t):t(n))}function ad(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Bo(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function om(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function im(e,t,n){let r=hr(e,t);return r>=0?e[r|1]=n:(r=~r,om(e,r,t,n)),r}function Qs(e,t){let n=hr(e,t);if(n>=0)return e[n|1]}function hr(e,t){return sm(e,t,1)}function sm(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var xt={},ve=[],ar=new E(""),cd=new E("",-1),ud=new E(""),Uo=class{get(t,n=_t){if(n===_t){let r=new Error(`NullInjectorError: No provider for ${ue(t)}!`);throw r.name="NullInjectorError",r}return n}};function ld(e,t){let n=e[Wg]||null;if(!n&&t===!0)throw new Error(`Type ${ue(e)} does not have '\u0275mod' property.`);return n}function At(e){return e[zg]||null}function am(e){return e[qg]||null}function cm(e){return e[Gg]||null}function Cn(e){return{\u0275providers:e}}function um(...e){return{\u0275providers:dd(!0,e),\u0275fromNgModule:!0}}function dd(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Wa(t,s=>{let a=s;fa(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&fd(o,i),n}function fd(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Za(o,i=>{t(i,r)})}}function fa(e,t,n,r){if(e=ne(e),!e)return!1;let o=null,i=ll(e),s=!i&&At(e);if(!i&&!s){let c=e.ngModule;if(i=ll(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)fa(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Wa(i.imports,l=>{fa(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&fd(u,t)}if(!a){let u=Rt(o)||(()=>new o);t({provide:o,useFactory:u,deps:ve},o),t({provide:ud,useValue:o,multi:!0},o),t({provide:ar,useValue:()=>w(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Za(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Za(e,t){for(let n of e)rd(n)&&(n=n.\u0275providers),Array.isArray(n)?Za(n,t):t(n)}var lm=j({provide:String,useValue:j});function hd(e){return e!==null&&typeof e=="object"&&lm in e}function dm(e){return!!(e&&e.useExisting)}function fm(e){return!!(e&&e.useFactory)}function pn(e){return typeof e=="function"}function hm(e){return!!e.useClass}var hi=new E(""),Ao={},pl={},Ks;function Ya(){return Ks===void 0&&(Ks=new Uo),Ks}var ee=class{},cr=class extends ee{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,pa(t,s=>this.processProvider(s)),this.records.set(cd,cn(void 0,this)),o.has("environment")&&this.records.set(ee,cn(void 0,this));let i=this.records.get(hi);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(ud,ve,M.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?io:_t,r)}destroy(){or(this),this._destroyed=!0;let t=N(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),N(t)}}onDestroy(t){return or(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){or(this);let n=Qe(this),r=ae(void 0),o;try{return t()}finally{Qe(n),ae(r)}}get(t,n=_t,r=M.Default){if(or(this),t.hasOwnProperty(fl))return t[fl](this);r=fi(r);let o,i=Qe(this),s=ae(void 0);try{if(!(r&M.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=vm(t)&&li(t);u&&this.injectableDefInScope(u)?c=cn(ha(t),Ao):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&M.Self?Ya():this.parent;return n=r&M.Optional&&n===_t?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Vo]=a[Vo]||[]).unshift(ue(t)),i)throw a;return nm(a,t,"R3InjectorError",this.source)}else throw a}finally{ae(s),Qe(i)}}resolveInjectorInitializers(){let t=N(null),n=Qe(this),r=ae(void 0),o;try{let i=this.get(ar,ve,M.Self);for(let s of i)s()}finally{Qe(n),ae(r),N(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ue(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=ne(t);let n=pn(t)?t:ne(t&&t.provide),r=gm(t);if(!pn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=cn(void 0,Ao,!0),o.factory=()=>da(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=N(null);try{return n.value===pl?od(ue(t)):n.value===Ao&&(n.value=pl,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&ym(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{N(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ne(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function ha(e){let t=li(e),n=t!==null?t.factory:Rt(e);if(n!==null)return n;if(e instanceof E)throw new v(204,!1);if(e instanceof Function)return pm(e);throw new v(204,!1)}function pm(e){if(e.length>0)throw new v(204,!1);let n=$g(e);return n!==null?()=>n.factory(e):()=>new e}function gm(e){if(hd(e))return cn(void 0,e.useValue);{let t=pd(e);return cn(t,Ao)}}function pd(e,t,n){let r;if(pn(e)){let o=ne(e);return Rt(o)||ha(o)}else if(hd(e))r=()=>ne(e.useValue);else if(fm(e))r=()=>e.useFactory(...da(e.deps||[]));else if(dm(e))r=(o,i)=>w(ne(e.useExisting),i!==void 0&&i&M.Optional?M.Optional:void 0);else{let o=ne(e&&(e.useClass||e.provide));if(mm(e))r=()=>new o(...da(e.deps));else return Rt(o)||ha(o)}return r}function or(e){if(e.destroyed)throw new v(205,!1)}function cn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function mm(e){return!!e.deps}function ym(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function vm(e){return typeof e=="function"||typeof e=="object"&&e instanceof E}function pa(e,t){for(let n of e)Array.isArray(n)?pa(n,t):n&&rd(n)?pa(n.\u0275providers,t):t(n)}function le(e,t){let n;e instanceof cr?(or(e),n=e):n=new jo(e);let r,o=Qe(n),i=ae(void 0);try{return t()}finally{Qe(o),ae(i)}}function gd(){return id()!==void 0||Xn()!=null}function Dm(e){if(!gd())throw new v(-203,!1)}function Em(e){return typeof e=="function"}var rt=0,A=1,S=2,re=3,Me=4,Re=5,$o=6,Ho=7,he=8,gn=9,tt=10,X=11,ur=12,gl=13,bn=14,Fe=15,mn=16,un=17,yn=18,pi=19,md=20,pt=21,Js=22,zo=23,De=24,fn=25,_e=26,yd=1;var Ot=7,qo=8,Go=9,Ee=10;function gt(e){return Array.isArray(e)&&typeof e[yd]=="object"}function ot(e){return Array.isArray(e)&&e[yd]===!0}function vd(e){return(e.flags&4)!==0}function Sn(e){return e.componentOffset>-1}function Qa(e){return(e.flags&1)===1}function Le(e){return!!e.template}function Wo(e){return(e[S]&512)!==0}function Tn(e){return(e[S]&256)===256}var ga=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Dd(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var pr=(()=>{let e=()=>Ed;return e.ngInherit=!0,e})();function Ed(e){return e.type.prototype.ngOnChanges&&(e.setInput=Im),wm}function wm(){let e=Id(this),t=e?.current;if(t){let n=e.previous;if(n===xt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Im(e,t,n,r,o){let i=this.declaredInputs[r],s=Id(e)||Cm(e,{previous:xt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new ga(u&&u.currentValue,n,c===xt),Dd(e,t,o,n)}var wd="__ngSimpleChanges__";function Id(e){return e[wd]||null}function Cm(e,t){return e[wd]=t}var ml=null;var L=function(e,t=null,n){ml?.(e,t,n)},bm="svg",Sm="math";function je(e){for(;Array.isArray(e);)e=e[rt];return e}function Cd(e,t){return je(t[e])}function $e(e,t){return je(t[e.index])}function bd(e,t){return e.data[t]}function Tm(e,t){return e[t]}function Mm(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function Ve(e,t){let n=t[e];return gt(n)?n:n[rt]}function Ka(e){return(e[S]&128)===128}function _m(e){return ot(e[re])}function Zo(e,t){return t==null?null:e[t]}function Sd(e){e[un]=0}function Td(e){e[S]&1024||(e[S]|=1024,Ka(e)&&gr(e))}function Nm(e,t){for(;e>0;)t=t[bn],e--;return t}function gi(e){return!!(e[S]&9216||e[De]?.dirty)}function ma(e){e[tt].changeDetectionScheduler?.notify(8),e[S]&64&&(e[S]|=1024),gi(e)&&gr(e)}function gr(e){e[tt].changeDetectionScheduler?.notify(0);let t=kt(e);for(;t!==null&&!(t[S]&8192||(t[S]|=8192,!Ka(t)));)t=kt(t)}function Md(e,t){if(Tn(e))throw new v(911,!1);e[pt]===null&&(e[pt]=[]),e[pt].push(t)}function Rm(e,t){if(e[pt]===null)return;let n=e[pt].indexOf(t);n!==-1&&e[pt].splice(n,1)}function kt(e){let t=e[re];return ot(t)?t[re]:t}function _d(e){return e[Ho]??=[]}function Nd(e){return e.cleanup??=[]}var O={lFrame:Ld(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var ya=!1;function xm(){return O.lFrame.elementDepthCount}function Am(){O.lFrame.elementDepthCount++}function Om(){O.lFrame.elementDepthCount--}function Rd(){return O.bindingsEnabled}function km(){return O.skipHydrationRootTNode!==null}function Pm(e){return O.skipHydrationRootTNode===e}function Fm(){O.skipHydrationRootTNode=null}function U(){return O.lFrame.lView}function we(){return O.lFrame.tView}function pe(){let e=xd();for(;e!==null&&e.type===64;)e=e.parent;return e}function xd(){return O.lFrame.currentTNode}function Lm(){let e=O.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function mr(e,t){let n=O.lFrame;n.currentTNode=e,n.isParent=t}function Ad(){return O.lFrame.isParent}function jm(){O.lFrame.isParent=!1}function Od(){return ya}function yl(e){let t=ya;return ya=e,t}function Vm(){let e=O.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Bm(e){return O.lFrame.bindingIndex=e}function Ja(){return O.lFrame.bindingIndex++}function Um(e){let t=O.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function $m(){return O.lFrame.inI18n}function Hm(e,t){let n=O.lFrame;n.bindingIndex=n.bindingRootIndex=e,va(t)}function zm(){return O.lFrame.currentDirectiveIndex}function va(e){O.lFrame.currentDirectiveIndex=e}function qm(e){let t=O.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function kd(e){O.lFrame.currentQueryIndex=e}function Gm(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Re]:null}function Pd(e,t,n){if(n&M.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&M.Host);)if(o=Gm(i),o===null||(i=i[bn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=O.lFrame=Fd();return r.currentTNode=t,r.lView=e,!0}function Xa(e){let t=Fd(),n=e[A];O.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Fd(){let e=O.lFrame,t=e===null?null:e.child;return t===null?Ld(e):t}function Ld(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function jd(){let e=O.lFrame;return O.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Vd=jd;function ec(){let e=jd();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Wm(e){return(O.lFrame.contextLView=Nm(e,O.lFrame.contextLView))[he]}function Mn(){return O.lFrame.selectedIndex}function Pt(e){O.lFrame.selectedIndex=e}function Bd(){let e=O.lFrame;return bd(e.tView,e.selectedIndex)}function Zm(){return O.lFrame.currentNamespace}var Ud=!0;function tc(){return Ud}function nc(e){Ud=e}function Ym(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ed(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function $d(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Oo(e,t,n){Hd(e,t,3,n)}function ko(e,t,n,r){(e[S]&3)===n&&Hd(e,t,n,r)}function Xs(e,t){let n=e[S];(n&3)===t&&(n&=16383,n+=1,e[S]=n)}function Hd(e,t,n,r){let o=r!==void 0?e[un]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[un]+=65536),(a<i||i==-1)&&(Qm(e,n,t,c),e[un]=(e[un]&**********)+c+2),c++}function vl(e,t){L(4,e,t);let n=N(null);try{t.call(e)}finally{N(n),L(5,e,t)}}function Qm(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[S]>>14<e[un]>>16&&(e[S]&3)===t&&(e[S]+=16384,vl(a,i)):vl(a,i)}var hn=-1,Ft=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Km(e){return(e.flags&8)!==0}function Jm(e){return(e.flags&16)!==0}function Xm(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];ey(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function zd(e){return e===3||e===4||e===6}function ey(e){return e.charCodeAt(0)===64}function lr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Dl(e,n,o,null,t[++r]):Dl(e,n,o,null,null))}}return e}function Dl(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function qd(e){return e!==hn}function Yo(e){return e&32767}function ty(e){return e>>16}function Qo(e,t){let n=ty(e),r=t;for(;n>0;)r=r[bn],n--;return r}var Da=!0;function Ko(e){let t=Da;return Da=e,t}var ny=256,Gd=ny-1,Wd=5,ry=0,Pe={};function oy(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(sr)&&(r=n[sr]),r==null&&(r=n[sr]=ry++);let o=r&Gd,i=1<<o;t.data[e+(o>>Wd)]|=i}function Jo(e,t){let n=Zd(e,t);if(n!==-1)return n;let r=t[A];r.firstCreatePass&&(e.injectorIndex=t.length,ea(r.data,e),ea(t,null),ea(r.blueprint,null));let o=rc(e,t),i=e.injectorIndex;if(qd(o)){let s=Yo(o),a=Qo(o,t),c=a[A].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function ea(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Zd(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function rc(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Xd(o),r===null)return hn;if(n++,o=o[bn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return hn}function Ea(e,t,n){oy(e,t,n)}function iy(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(zd(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Yd(e,t,n){if(n&M.Optional||e!==void 0)return e;Ga(t,"NodeInjector")}function Qd(e,t,n,r){if(n&M.Optional&&r===void 0&&(r=null),(n&(M.Self|M.Host))===0){let o=e[gn],i=ae(void 0);try{return o?o.get(t,r,n&M.Optional):sd(t,r,n&M.Optional)}finally{ae(i)}}return Yd(r,t,n)}function Kd(e,t,n,r=M.Default,o){if(e!==null){if(t[S]&2048&&!(r&M.Self)){let s=ly(e,t,n,r,Pe);if(s!==Pe)return s}let i=Jd(e,t,n,r,Pe);if(i!==Pe)return i}return Qd(t,n,r,o)}function Jd(e,t,n,r,o){let i=cy(n);if(typeof i=="function"){if(!Pd(t,e,r))return r&M.Host?Yd(o,n,r):Qd(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&M.Optional))Ga(n);else return s}finally{Vd()}}else if(typeof i=="number"){let s=null,a=Zd(e,t),c=hn,u=r&M.Host?t[Fe][Re]:null;for((a===-1||r&M.SkipSelf)&&(c=a===-1?rc(e,t):t[a+8],c===hn||!wl(r,!1)?a=-1:(s=t[A],a=Yo(c),t=Qo(c,t)));a!==-1;){let l=t[A];if(El(i,a,l.data)){let f=sy(a,t,n,s,r,u);if(f!==Pe)return f}c=t[a+8],c!==hn&&wl(r,t[A].data[a+8]===u)&&El(i,a,t)?(s=l,a=Yo(c),t=Qo(c,t)):a=-1}}return o}function sy(e,t,n,r,o,i){let s=t[A],a=s.data[e+8],c=r==null?Sn(a)&&Da:r!=s&&(a.type&3)!==0,u=o&M.Host&&i===a,l=ay(a,s,n,c,u);return l!==null?Xo(t,s,l,a,o):Pe}function ay(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,f=r?a:a+l,h=o?a+l:u;for(let d=f;d<h;d++){let g=s[d];if(d<c&&n===g||d>=c&&g.type===n)return d}if(o){let d=s[c];if(d&&Le(d)&&d.type===n)return c}return null}function Xo(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof Ft){let a=i;a.resolving&&od(Zg(s[n]));let c=Ko(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?ae(a.injectImpl):null,f=Pd(e,r,M.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&Ym(n,s[n],t)}finally{l!==null&&ae(l),Ko(c),a.resolving=!1,Vd()}}return i}function cy(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(sr)?e[sr]:void 0;return typeof t=="number"?t>=0?t&Gd:uy:t}function El(e,t,n){let r=1<<e;return!!(n[t+(e>>Wd)]&r)}function wl(e,t){return!(e&M.Self)&&!(e&M.Host&&t)}var Nt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Kd(this._tNode,this._lView,t,fi(r),n)}};function uy(){return new Nt(pe(),U())}function oc(e){return ui(()=>{let t=e.prototype.constructor,n=t[Lo]||wa(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Lo]||wa(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function wa(e){return Xl(e)?()=>{let t=wa(ne(e));return t&&t()}:Rt(e)}function ly(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[S]&2048&&!Wo(s);){let a=Jd(i,s,n,r|M.Self,Pe);if(a!==Pe)return a;let c=i.parent;if(!c){let u=s[md];if(u){let l=u.get(n,Pe,r);if(l!==Pe)return l}c=Xd(s),s=s[bn]}i=c}return o}function Xd(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Re]:null}function ic(e){return iy(pe(),e)}function Il(e,t=null,n=null,r){let o=ef(e,t,n,r);return o.resolveInjectorInitializers(),o}function ef(e,t=null,n=null,r,o=new Set){let i=[n||ve,um(e)];return r=r||(typeof e=="object"?void 0:ue(e)),new cr(i,t||Ya(),r||null,o)}var Ne=class e{static THROW_IF_NOT_FOUND=_t;static NULL=new Uo;static create(t,n){if(Array.isArray(t))return Il({name:""},n,t,"");{let r=t.name??"";return Il({name:r},t.parent,t.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>w(cd)});static __NG_ELEMENT_ID__=-1};var dy=new E("");dy.__NG_ELEMENT_ID__=e=>{let t=pe();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&M.Optional)return null;throw new v(204,!1)};var tf=!1,Vt=(()=>{class e{static __NG_ELEMENT_ID__=fy;static __NG_ENV_ID__=n=>n}return e})(),Ia=class extends Vt{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Tn(n)?(t(),()=>{}):(Md(n,t),()=>Rm(n,t))}};function fy(){return new Ia(U())}var vn=class{},sc=new E("",{providedIn:"root",factory:()=>!1});var nf=new E(""),rf=new E(""),it=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new Q(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();var Ca=class extends J{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,gd()&&(this.destroyRef=p(Vt,{optional:!0})??void 0,this.pendingTasks=p(it,{optional:!0})??void 0)}emit(t){let n=N(null);try{super.next(t)}finally{N(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof G&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ce=Ca;function ei(...e){}function of(e){let t,n;function r(){e=ei;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Cl(e){return queueMicrotask(()=>e()),()=>{e=ei}}var ac="isAngularZone",ti=ac+"_ID",hy=0,$=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ce(!1);onMicrotaskEmpty=new ce(!1);onStable=new ce(!1);onError=new ce(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=tf}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,my(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ac)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,py,ei,ei);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},py={};function cc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function gy(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){of(()=>{e.callbackScheduled=!1,ba(e),e.isCheckStableRunning=!0,cc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),ba(e)}function my(e){let t=()=>{gy(e)},n=hy++;e._inner=e._inner.fork({name:"angular",properties:{[ac]:!0,[ti]:n,[ti+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(yy(c))return r.invokeTask(i,s,a,c);try{return bl(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Sl(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return bl(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!vy(c)&&t(),Sl(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ba(e),cc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ba(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function bl(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Sl(e){e._nesting--,cc(e)}var Sa=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ce;onMicrotaskEmpty=new ce;onStable=new ce;onError=new ce;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function yy(e){return sf(e,"__ignore_ng_zone__")}function vy(e){return sf(e,"__scheduler_tick__")}function sf(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Be=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Dy=new E("",{providedIn:"root",factory:()=>{let e=p($),t=p(Be);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Tl(e,t){return Kl(e,t)}function Ey(e){return Kl(Ql,e)}var af=(Tl.required=Ey,Tl);function wy(){return mi(pe(),U())}function mi(e,t){return new yr($e(e,t))}var yr=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=wy}return e})();function B0(e,t){let n=As(e,t?.equal),r=n[be];return n.set=o=>Jn(r,o),n.update=o=>Os(r,o),n.asReadonly=Iy.bind(n),n}function Iy(){let e=this[be];if(e.readonlyFn===void 0){let t=()=>this();t[be]=e,e.readonlyFn=t}return e.readonlyFn}function cf(e){return(e.flags&128)===128}var uf=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(uf||{}),lf=new Map,Cy=0;function by(){return Cy++}function Sy(e){lf.set(e[pi],e)}function Ta(e){lf.delete(e[pi])}var Ml="__ngContext__";function vr(e,t){gt(t)?(e[Ml]=t[pi],Sy(t)):e[Ml]=t}function df(e){return hf(e[ur])}function ff(e){return hf(e[Me])}function hf(e){for(;e!==null&&!ot(e);)e=e[Me];return e}var Ma;function pf(e){Ma=e}function Ty(){if(Ma!==void 0)return Ma;if(typeof document<"u")return document;throw new v(210,!1)}var uc=new E("",{providedIn:"root",factory:()=>My}),My="ng",lc=new E(""),_n=new E("",{providedIn:"platform",factory:()=>"unknown"});var dc=new E("",{providedIn:"root",factory:()=>Ty().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var _y="h",Ny="b";var gf=!1,Ry=new E("",{providedIn:"root",factory:()=>gf});var fc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(fc||{}),Nn=new E(""),_l=new Set;function Rn(e){_l.has(e)||(_l.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var mf=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=xy}return e})();function xy(){return new mf(U(),pe())}var ln=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(ln||{}),yf=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),Ay=[ln.EarlyRead,ln.Write,ln.MixedReadWrite,ln.Read],Oy=(()=>{class e{ngZone=p($);scheduler=p(vn);errorHandler=p(Be,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Nn,{optional:!0})}execute(){let n=this.sequences.size>0;n&&L(16),this.executing=!0;for(let r of Ay)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&L(17)}register(n){let{view:r}=n;r!==void 0?((r[fn]??=[]).push(n),gr(r),r[S]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(fc.AFTER_NEXT_RENDER,n):n()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),_a=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[fn];t&&(this.view[fn]=t.filter(n=>n!==this))}};function hc(e,t){!t?.injector&&Dm(hc);let n=t?.injector??p(Ne);return Rn("NgAfterNextRender"),Py(e,n,t,!0)}function ky(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Py(e,t,n,r){let o=t.get(yf);o.impl??=t.get(Oy);let i=t.get(Nn,null,{optional:!0}),s=n?.phase??ln.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Vt):null,c=t.get(mf,null,{optional:!0}),u=new _a(o.impl,ky(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var Fy=(e,t,n,r)=>{};function Ly(e,t,n,r){Fy(e,t,n,r)}var jy=()=>null;function vf(e,t,n=!1){return jy(e,t,n)}function Df(e,t){let n=e.contentQueries;if(n!==null){let r=N(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];kd(i),a.contentQueries(2,t[s],s)}}}finally{N(r)}}}function Na(e,t,n){kd(0);let r=N(null);try{t(e,n)}finally{N(r)}}function Ef(e,t,n){if(vd(t)){let r=N(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{N(r)}}}var Ue=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ue||{});var Ro;function Vy(){if(Ro===void 0&&(Ro=null,ua.trustedTypes))try{Ro=ua.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Ro}function Nl(e){return Vy()?.createScriptURL(e)||e}var ni=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Zl})`}};function yi(e){return e instanceof ni?e.changingThisBreaksApplicationSecurity:e}function wf(e,t){let n=By(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Zl})`)}return n===t}function By(e){return e instanceof ni&&e.getTypeName()||null}var Uy=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function $y(e){return e=String(e),e.match(Uy)?e:"unsafe:"+e}var pc=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(pc||{});function Hy(e){let t=Cf();return t?t.sanitize(pc.URL,e)||"":wf(e,"URL")?yi(e):$y(di(e))}function zy(e){let t=Cf();if(t)return Nl(t.sanitize(pc.RESOURCE_URL,e)||"");if(wf(e,"ResourceURL"))return Nl(yi(e));throw new v(904,!1)}function qy(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?zy:Hy}function If(e,t,n){return qy(t,n)(e)}function Cf(){let e=U();return e&&e[tt].sanitizer}function bf(e){return e instanceof Function?e():e}function Gy(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Sf="ng-template";function Wy(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Gy(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(gc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function gc(e){return e.type===4&&e.value!==Sf}function Zy(e,t,n){let r=e.type===4&&!n?Sf:e.value;return t===r}function Yy(e,t,n){let r=4,o=e.attrs,i=o!==null?Jy(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Te(r)&&!Te(c))return!1;if(s&&Te(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Zy(e,c,n)||c===""&&t.length===1){if(Te(r))return!1;s=!0}}else if(r&8){if(o===null||!Wy(e,o,c,n)){if(Te(r))return!1;s=!0}}else{let u=t[++a],l=Qy(c,o,gc(e),n);if(l===-1){if(Te(r))return!1;s=!0;continue}if(u!==""){let f;if(l>i?f="":f=o[l+1].toLowerCase(),r&2&&u!==f){if(Te(r))return!1;s=!0}}}}return Te(r)||s}function Te(e){return(e&1)===0}function Qy(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Xy(t,e)}function Ky(e,t,n=!1){for(let r=0;r<t.length;r++)if(Yy(e,t[r],n))return!0;return!1}function Jy(e){for(let t=0;t<e.length;t++){let n=e[t];if(zd(n))return t}return e.length}function Xy(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Rl(e,t){return e?":not("+t.trim()+")":t}function ev(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Te(s)&&(t+=Rl(i,o),o=""),r=s,i=i||!Te(r);n++}return o!==""&&(t+=Rl(i,o)),t}function tv(e){return e.map(ev).join(",")}function nv(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Te(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Bt={};function rv(e,t){return e.createText(t)}function ov(e,t,n){e.setValue(t,n)}function Tf(e,t,n){return e.createElement(t,n)}function ri(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Mf(e,t,n){e.appendChild(t,n)}function xl(e,t,n,r,o){r!==null?ri(e,t,n,r,o):Mf(e,t,n)}function iv(e,t,n){e.removeChild(null,t,n)}function sv(e,t,n){e.setAttribute(t,"style",n)}function av(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function _f(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Xm(e,t,r),o!==null&&av(e,t,o),i!==null&&sv(e,t,i)}function mc(e,t,n,r,o,i,s,a,c,u,l){let f=_e+r,h=f+o,d=cv(f,h),g=typeof u=="function"?u():u;return d[A]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function cv(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Bt);return n}function uv(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=mc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function yc(e,t,n,r,o,i,s,a,c,u,l){let f=t.blueprint.slice();return f[rt]=o,f[S]=r|4|128|8|64|1024,(u!==null||e&&e[S]&2048)&&(f[S]|=2048),Sd(f),f[re]=f[bn]=e,f[he]=n,f[tt]=s||e&&e[tt],f[X]=a||e&&e[X],f[gn]=c||e&&e[gn]||null,f[Re]=i,f[pi]=by(),f[$o]=l,f[md]=u,f[Fe]=t.type==2?e[Fe]:f,f}function lv(e,t,n){let r=$e(t,e),o=uv(n),i=e[tt].rendererFactory,s=vc(e,yc(e,o,null,Nf(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Nf(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Rf(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function vc(e,t){return e[ur]?e[gl][Me]=t:e[ur]=t,e[gl]=t,t}function U0(e=1){xf(we(),U(),Mn()+e,!1)}function xf(e,t,n,r){if(!r)if((t[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Oo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&ko(t,i,0,n)}Pt(n)}var vi=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(vi||{});function Ra(e,t,n,r){let o=N(null);try{let[i,s,a]=e.inputs[n],c=null;(s&vi.SignalBased)!==0&&(c=t[i][be]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Dd(t,c,i,r)}finally{N(o)}}function Af(e,t,n,r,o){let i=Mn(),s=r&2;try{Pt(-1),s&&t.length>_e&&xf(e,t,_e,!1),L(s?2:0,o),n(r,o)}finally{Pt(i),L(s?3:1,o)}}function Dc(e,t,n){yv(e,t,n),(n.flags&64)===64&&vv(e,t,n)}function Of(e,t,n=$e){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function dv(e,t,n,r){let i=r.get(Ry,gf)||n===Ue.ShadowDom,s=e.selectRootElement(t,i);return fv(s),s}function fv(e){hv(e)}var hv=()=>null;function pv(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function gv(e,t,n,r,o,i,s,a){if(!a&&Ec(t,e,n,r,o)){Sn(t)&&mv(n,t.index);return}if(t.type&3){let c=$e(t,n);r=pv(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function mv(e,t){let n=Ve(t,e);n[S]&16||(n[S]|=64)}function yv(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Sn(n)&&lv(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Jo(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Xo(t,e,s,n);if(vr(c,t),i!==null&&Iv(t,s-r,c,a,n,i),Le(a)){let u=Ve(n.index,t);u[he]=Xo(t,e,s,n)}}}function vv(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=zm();try{Pt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];va(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Dv(c,u)}}finally{Pt(-1),va(s)}}function Dv(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function kf(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Ky(t,i.selectors,!1)&&(r??=[],Le(i)?r.unshift(i):r.push(i))}return r}function Ev(e,t,n,r,o,i){let s=$e(e,t);wv(t[X],s,i,e.value,n,r,o)}function wv(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?di(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Iv(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Ra(r,n,c,u)}}function Cv(e,t){let n=e[gn],r=n?n.get(Be,null):null;r&&r.handleError(t)}function Ec(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],f=t.data[u];Ra(f,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Ra(l,u,r,o),a=!0}return a}function bv(e,t){let n=Ve(t,e),r=n[A];Sv(r,n);let o=n[rt];o!==null&&n[$o]===null&&(n[$o]=vf(o,n[gn])),L(18),wc(r,n,n[he]),L(19,n[he])}function Sv(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function wc(e,t,n){Xa(t);try{let r=e.viewQuery;r!==null&&Na(1,r,n);let o=e.template;o!==null&&Af(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[yn]?.finishViewCreation(e),e.staticContentQueries&&Df(e,t),e.staticViewQueries&&Na(2,e.viewQuery,n);let i=e.components;i!==null&&Tv(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[S]&=-5,ec()}}function Tv(e,t){for(let n=0;n<t.length;n++)bv(e,t[n])}function Mv(e,t,n,r){let o=N(null);try{let i=t.tView,a=e[S]&4096?4096:16,c=yc(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[mn]=u;let l=e[yn];return l!==null&&(c[yn]=l.createEmbeddedView(i)),wc(i,c,n),c}finally{N(o)}}function Al(e,t){return!t||t.firstChild===null||cf(e)}var _v;function Ic(e,t){return _v(e,t)}var nt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(nt||{});function Pf(e){return(e.flags&32)===32}function dn(e,t,n,r,o){if(r!=null){let i,s=!1;ot(r)?i=r:gt(r)&&(s=!0,r=r[rt]);let a=je(r);e===0&&n!==null?o==null?Mf(t,n,a):ri(t,n,a,o||null,!0):e===1&&n!==null?ri(t,n,a,o||null,!0):e===2?iv(t,a,s):e===3&&t.destroyNode(a),i!=null&&Bv(t,e,i,n,o)}}function Nv(e,t){Ff(e,t),t[rt]=null,t[Re]=null}function Rv(e,t,n,r,o,i){r[rt]=o,r[Re]=t,Di(e,r,n,1,o,i)}function Ff(e,t){t[tt].changeDetectionScheduler?.notify(9),Di(e,t,t[X],2,null,null)}function xv(e){let t=e[ur];if(!t)return ta(e[A],e);for(;t;){let n=null;if(gt(t))n=t[ur];else{let r=t[Ee];r&&(n=r)}if(!n){for(;t&&!t[Me]&&t!==e;)gt(t)&&ta(t[A],t),t=t[re];t===null&&(t=e),gt(t)&&ta(t[A],t),n=t&&t[Me]}t=n}}function Cc(e,t){let n=e[Go],r=n.indexOf(t);n.splice(r,1)}function Lf(e,t){if(Tn(t))return;let n=t[X];n.destroyNode&&Di(e,t,n,3,null,null),xv(t)}function ta(e,t){if(Tn(t))return;let n=N(null);try{t[S]&=-129,t[S]|=256,t[De]&&Ns(t[De]),Ov(e,t),Av(e,t),t[A].type===1&&t[X].destroy();let r=t[mn];if(r!==null&&ot(t[re])){r!==t[re]&&Cc(r,t);let o=t[yn];o!==null&&o.detachView(e)}Ta(t)}finally{N(n)}}function Av(e,t){let n=e.cleanup,r=t[Ho];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Ho]=null);let o=t[pt];if(o!==null){t[pt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[zo];if(i!==null){t[zo]=null;for(let s of i)s.destroy()}}function Ov(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Ft)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];L(4,a,c);try{c.call(a)}finally{L(5,a,c)}}else{L(4,o,i);try{i.call(o)}finally{L(5,o,i)}}}}}function kv(e,t,n){return Pv(e,t.parent,n)}function Pv(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[rt];if(Sn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ue.None||o===Ue.Emulated)return null}return $e(r,n)}function Fv(e,t,n){return jv(e,t,n)}function Lv(e,t,n){return e.type&40?$e(e,n):null}var jv=Lv,Ol;function bc(e,t,n,r){let o=kv(e,r,t),i=t[X],s=r.parent||t[Re],a=Fv(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)xl(i,o,n[c],a,!1);else xl(i,o,n,a,!1);Ol!==void 0&&Ol(i,r,t,n,o)}function ir(e,t){if(t!==null){let n=t.type;if(n&3)return $e(t,e);if(n&4)return xa(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return ir(e,r);{let o=e[t.index];return ot(o)?xa(-1,o):je(o)}}else{if(n&128)return ir(e,t.next);if(n&32)return Ic(t,e)()||je(e[t.index]);{let r=jf(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=kt(e[Fe]);return ir(o,r)}else return ir(e,t.next)}}}return null}function jf(e,t){if(t!==null){let r=e[Fe][Re],o=t.projection;return r.projection[o]}return null}function xa(e,t){let n=Ee+e+1;if(n<t.length){let r=t[n],o=r[A].firstChild;if(o!==null)return ir(r,o)}return t[Ot]}function Sc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&vr(je(a),r),n.flags|=2),!Pf(n))if(c&8)Sc(e,t,n.child,r,o,i,!1),dn(t,e,o,a,i);else if(c&32){let u=Ic(n,r),l;for(;l=u();)dn(t,e,o,l,i);dn(t,e,o,a,i)}else c&16?Vv(e,t,r,n,o,i):dn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Di(e,t,n,r,o,i){Sc(n,r,e.firstChild,t,o,i,!1)}function Vv(e,t,n,r,o,i){let s=n[Fe],c=s[Re].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];dn(t,e,o,l,i)}else{let u=c,l=s[re];cf(r)&&(u.flags|=128),Sc(e,t,u,l,o,i,!0)}}function Bv(e,t,n,r,o){let i=n[Ot],s=je(n);i!==s&&dn(t,e,r,i,o);for(let a=Ee;a<n.length;a++){let c=n[a];Di(c[A],c,e,t,r,i)}}function Uv(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:nt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=nt.Important),e.setStyle(n,r,o,i))}}function oi(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(je(i)),ot(i)&&$v(i,r);let s=n.type;if(s&8)oi(e,t,n.child,r);else if(s&32){let a=Ic(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=jf(t,n);if(Array.isArray(a))r.push(...a);else{let c=kt(t[Fe]);oi(c[A],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function $v(e,t){for(let n=Ee;n<e.length;n++){let r=e[n],o=r[A].firstChild;o!==null&&oi(r[A],r,o,t)}e[Ot]!==e[rt]&&t.push(e[Ot])}function Vf(e){if(e[fn]!==null){for(let t of e[fn])t.impl.addSequence(t);e[fn].length=0}}var Bf=[];function Hv(e){return e[De]??zv(e)}function zv(e){let t=Bf.pop()??Object.create(Gv);return t.lView=e,t}function qv(e){e.lView[De]!==e&&(e.lView=null,Bf.push(e))}var Gv=V(m({},Qn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{gr(e.lView)},consumerOnSignalRead(){this.lView[De]=this}});function Wv(e){let t=e[De]??Object.create(Zv);return t.lView=e,t}var Zv=V(m({},Qn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=kt(e.lView);for(;t&&!Uf(t[A]);)t=kt(t);t&&Td(t)},consumerOnSignalRead(){this.lView[De]=this}});function Uf(e){return e.type!==2}function $f(e){if(e[zo]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[zo])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[S]&8192)}}var Yv=100;function Hf(e,t=!0,n=0){let o=e[tt].rendererFactory,i=!1;i||o.begin?.();try{Qv(e,n)}catch(s){throw t&&Cv(e,s),s}finally{i||o.end?.()}}function Qv(e,t){let n=Od();try{yl(!0),Aa(e,t);let r=0;for(;gi(e);){if(r===Yv)throw new v(103,!1);r++,Aa(e,1)}}finally{yl(n)}}function Kv(e,t,n,r){if(Tn(t))return;let o=t[S],i=!1,s=!1;Xa(t);let a=!0,c=null,u=null;i||(Uf(e)?(u=Hv(t),c=to(u)):Cs()===null?(a=!1,u=Wv(t),c=to(u)):t[De]&&(Ns(t[De]),t[De]=null));try{Sd(t),Bm(e.bindingStartIndex),n!==null&&Af(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let d=e.preOrderCheckHooks;d!==null&&Oo(t,d,null)}else{let d=e.preOrderHooks;d!==null&&ko(t,d,0,null),Xs(t,0)}if(s||Jv(t),$f(t),zf(t,0),e.contentQueries!==null&&Df(e,t),!i)if(l){let d=e.contentCheckHooks;d!==null&&Oo(t,d)}else{let d=e.contentHooks;d!==null&&ko(t,d,1),Xs(t,1)}eD(e,t);let f=e.components;f!==null&&Gf(t,f,0);let h=e.viewQuery;if(h!==null&&Na(2,h,r),!i)if(l){let d=e.viewCheckHooks;d!==null&&Oo(t,d)}else{let d=e.viewHooks;d!==null&&ko(t,d,2),Xs(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Js]){for(let d of t[Js])d();t[Js]=null}i||(Vf(t),t[S]&=-73)}catch(l){throw i||gr(t),l}finally{u!==null&&(Ms(u,c),a&&qv(u)),ec()}}function zf(e,t){for(let n=df(e);n!==null;n=ff(n))for(let r=Ee;r<n.length;r++){let o=n[r];qf(o,t)}}function Jv(e){for(let t=df(e);t!==null;t=ff(t)){if(!(t[S]&2))continue;let n=t[Go];for(let r=0;r<n.length;r++){let o=n[r];Td(o)}}}function Xv(e,t,n){L(18);let r=Ve(t,e);qf(r,n),L(19,r[he])}function qf(e,t){Ka(e)&&Aa(e,t)}function Aa(e,t){let r=e[A],o=e[S],i=e[De],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&_s(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)Kv(r,e,r.template,e[he]);else if(o&8192){$f(e),zf(e,1);let a=r.components;a!==null&&Gf(e,a,1),Vf(e)}}function Gf(e,t,n){for(let r=0;r<t.length;r++)Xv(e,t[r],n)}function eD(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Pt(~o);else{let i=o,s=n[++r],a=n[++r];Hm(s,i);let c=t[i];L(24,c),a(2,c),L(25,c)}}}finally{Pt(-1)}}function Tc(e,t){let n=Od()?64:1088;for(e[tt].changeDetectionScheduler?.notify(t);e;){e[S]|=n;let r=kt(e);if(Wo(e)&&!r)return e;e=r}return null}function Wf(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function tD(e,t,n,r=!0){let o=t[A];if(nD(o,t,e,n),r){let s=xa(n,e),a=t[X],c=a.parentNode(e[Ot]);c!==null&&Rv(o,e[Re],a,t,c,s)}let i=t[$o];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Oa(e,t){if(e.length<=Ee)return;let n=Ee+t,r=e[n];if(r){let o=r[mn];o!==null&&o!==e&&Cc(o,r),t>0&&(e[n-1][Me]=r[Me]);let i=Bo(e,Ee+t);Nv(r[A],r);let s=i[yn];s!==null&&s.detachView(i[A]),r[re]=null,r[Me]=null,r[S]&=-129}return r}function nD(e,t,n,r){let o=Ee+r,i=n.length;r>0&&(n[o-1][Me]=t),r<i-Ee?(t[Me]=n[o],ad(n,Ee+r,t)):(n.push(t),t[Me]=null),t[re]=n;let s=t[mn];s!==null&&n!==s&&Zf(s,t);let a=t[yn];a!==null&&a.insertView(e),ma(t),t[S]|=128}function Zf(e,t){let n=e[Go],r=t[re];if(gt(r))e[S]|=2;else{let o=r[re][Fe];t[Fe]!==o&&(e[S]|=2)}n===null?e[Go]=[t]:n.push(t)}var dr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[A];return oi(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[he]}set context(t){this._lView[he]=t}get destroyed(){return Tn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[re];if(ot(t)){let n=t[qo],r=n?n.indexOf(this):-1;r>-1&&(Oa(t,r),Bo(n,r))}this._attachedToViewContainer=!1}Lf(this._lView[A],this._lView)}onDestroy(t){Md(this._lView,t)}markForCheck(){Tc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){ma(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,Hf(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Wo(this._lView),n=this._lView[mn];n!==null&&!t&&Cc(n,this._lView),Ff(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=Wo(this._lView),r=this._lView[mn];r!==null&&!n&&Zf(r,this._lView),ma(this._lView)}};var Mc=(()=>{class e{static __NG_ELEMENT_ID__=iD}return e})(),rD=Mc,oD=class extends rD{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Mv(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new dr(o)}};function iD(){return sD(pe(),U())}function sD(e,t){return e.type&4?new oD(t,e,mi(e,t)):null}function _c(e,t,n,r,o){let i=e.data[t];if(i===null)i=aD(e,t,n,r,o),$m()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Lm();i.injectorIndex=s===null?-1:s.injectorIndex}return mr(i,!0),i}function aD(e,t,n,r,o){let i=xd(),s=Ad(),a=s?i:i&&i.parent,c=e.data[t]=uD(e,a,n,t,r,o);return cD(e,c,i,s),c}function cD(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function uD(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return km()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var z0=new RegExp(`^(\\d+)*(${Ny}|${_y})*(.*)`);var lD=()=>null;function kl(e,t){return lD(e,t)}var dD=class{},Yf=class{},ka=class{resolveComponentFactory(t){throw Error(`No component factory found for ${ue(t)}.`)}},Ei=class{static NULL=new ka},Dn=class{},wi=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>fD()}return e})();function fD(){let e=U(),t=pe(),n=Ve(t.index,e);return(gt(n)?n:e)[X]}var hD=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var na={},Pa=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=fi(r);let o=this.injector.get(t,na,r);return o!==na||n===na?o:this.parentInjector.get(t,n,r)}};function Pl(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=cl(o,a);else if(i==2){let c=a,u=t[++s];r=cl(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function ge(e,t=M.Default){let n=U();if(n===null)return w(e,t);let r=pe();return Kd(r,n,ne(e),t)}function Qf(){let e="invalid";throw new Error(e)}function Kf(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=gD(s);l===null?a=s:[a,c,u]=l,vD(e,t,n,a,i,c,u)}i!==null&&r!==null&&pD(n,r,i)}function pD(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function gD(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Le(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,mD(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function mD(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function yD(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function vD(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let d=r[h];!c&&Le(d)&&(c=!0,yD(e,n,h)),Ea(Jo(n,t),e,d.type)}bD(n,e.data.length,a);for(let h=0;h<a;h++){let d=r[h];d.providersResolver&&d.providersResolver(d)}let u=!1,l=!1,f=Rf(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let d=r[h];if(n.mergedAttrs=lr(n.mergedAttrs,d.hostAttrs),ED(e,n,t,f,d),CD(f,d,o),s!==null&&s.has(d)){let[y,I]=s.get(d);n.directiveToIndex.set(d.type,[f,y+n.directiveStart,I+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let g=d.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),f++}DD(e,n,i)}function DD(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Fl(0,t,o,r),Fl(1,t,o,r),jl(t,r,!1);else{let i=n.get(o);Ll(0,t,i,r),Ll(1,t,i,r),jl(t,r,!0)}}}function Fl(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Jf(t,i)}}function Ll(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Jf(t,s)}}function Jf(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function jl(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||gc(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function ED(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Rt(o.type,!0)),s=new Ft(i,Le(o),ge);e.blueprint[r]=s,n[r]=s,wD(e,t,r,Rf(e,n,o.hostVars,Bt),o)}function wD(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ID(s)!=a&&s.push(a),s.push(n,r,i)}}function ID(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function CD(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Le(t)&&(n[""]=e)}}function bD(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Xf(e,t,n,r,o,i,s,a){let c=t.consts,u=Zo(c,s),l=_c(t,e,2,r,u);return i&&Kf(t,n,l,Zo(c,a),o),l.mergedAttrs=lr(l.mergedAttrs,l.attrs),l.attrs!==null&&Pl(l,l.attrs,!1),l.mergedAttrs!==null&&Pl(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function eh(e,t){$d(e,t),vd(t)&&e.queries.elementEnd(t)}var ii=class extends Ei{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=At(t);return new En(n,this.ngModule)}};function SD(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&vi.SignalBased)!==0};return o&&(i.transform=o),i})}function TD(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function MD(e,t,n){let r=t instanceof ee?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Pa(n,r):n}function _D(e){let t=e.get(Dn,null);if(t===null)throw new v(407,!1);let n=e.get(hD,null),r=e.get(vn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function ND(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Tf(t,n,n==="svg"?bm:n==="math"?Sm:null)}var En=class extends Yf{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=SD(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=TD(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=tv(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){L(22);let i=N(null);try{let s=this.componentDef,a=r?["ng-version","19.2.13"]:nv(this.componentDef.selectors[0]),c=mc(0,null,null,1,0,null,null,null,null,[a],null),u=MD(s,o||this.ngModule,t),l=_D(u),f=l.rendererFactory.createRenderer(null,s),h=r?dv(f,r,s.encapsulation,u):ND(s,f),d=yc(null,c,null,512|Nf(s),null,null,l,f,u,null,vf(h,u,!0));d[_e]=h,Xa(d);let g=null;try{let y=Xf(_e,c,d,"#host",()=>[this.componentDef],!0,0);h&&(_f(f,h,y),vr(h,d)),Dc(c,d,y),Ef(c,y,d),eh(c,y),n!==void 0&&RD(y,this.ngContentSelectors,n),g=Ve(y.index,d),d[he]=g[he],wc(c,d,null)}catch(y){throw g!==null&&Ta(g),Ta(d),y}finally{L(23),ec()}return new Fa(this.componentType,d)}finally{N(i)}}},Fa=class extends dD{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=bd(n[A],_e),this.location=mi(this._tNode,n),this.instance=Ve(this._tNode.index,n)[he],this.hostView=this.changeDetectorRef=new dr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Ec(r,o[A],o,t,n);this.previousInputValues.set(t,n);let s=Ve(r.index,o);Tc(s,1)}get injector(){return new Nt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function RD(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Dr=(()=>{class e{static __NG_ELEMENT_ID__=xD}return e})();function xD(){let e=pe();return OD(e,U())}var AD=Dr,th=class extends AD{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return mi(this._hostTNode,this._hostLView)}get injector(){return new Nt(this._hostTNode,this._hostLView)}get parentInjector(){let t=rc(this._hostTNode,this._hostLView);if(qd(t)){let n=Qo(t,this._hostLView),r=Yo(t),o=n[A].data[r+8];return new Nt(o,n)}else return new Nt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Vl(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Ee}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=kl(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Al(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Em(t),a;if(s)a=n;else{let g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}let c=s?t:new En(At(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let y=(s?u:this.parentInjector).get(ee,null);y&&(i=y)}let l=At(c.componentType??{}),f=kl(this._lContainer,l?.id??null),h=f?.firstChild??null,d=c.create(u,o,h,i);return this.insertImpl(d.hostView,a,Al(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(_m(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[re],u=new th(c,c[Re],c[re]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return tD(s,o,i,r),t.attachToViewContainerRef(),ad(ra(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Vl(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Oa(this._lContainer,n);r&&(Bo(ra(this._lContainer),n),Lf(r[A],r))}detach(t){let n=this._adjustIndex(t,-1),r=Oa(this._lContainer,n);return r&&Bo(ra(this._lContainer),n)!=null?new dr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Vl(e){return e[qo]}function ra(e){return e[qo]||(e[qo]=[])}function OD(e,t){let n,r=t[e.index];return ot(r)?n=r:(n=Wf(r,t,null,e),t[e.index]=n,vc(t,n)),PD(n,t,e,r),new th(n,e,t)}function kD(e,t){let n=e[X],r=n.createComment(""),o=$e(t,e),i=n.parentNode(o);return ri(n,i,r,n.nextSibling(o),!1),r}var PD=jD,FD=()=>!1;function LD(e,t,n){return FD(e,t,n)}function jD(e,t,n,r){if(e[Ot])return;let o;n.type&8?o=je(r):o=kD(t,n),e[Ot]=o}var wn=class{},Nc=class{};var La=class extends wn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new ii(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=ld(t);this._bootstrapComponents=bf(i.bootstrap),this._r3Injector=ef(t,n,[{provide:wn,useValue:this},{provide:Ei,useValue:this.componentFactoryResolver},...r],ue(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ja=class extends Nc{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new La(this.moduleType,t,[])}};var si=class extends wn{injector;componentFactoryResolver=new ii(this);instance=null;constructor(t){super();let n=new cr([...t.providers,{provide:wn,useValue:this},{provide:Ei,useValue:this.componentFactoryResolver}],t.parent||Ya(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Er(e,t,n=null){return new si({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var VD=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=dd(!1,n.type),o=r.length>0?Er([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(w(ee))})}return e})();function nh(e){return ui(()=>{let t=oh(e),n=V(m({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===uf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(VD).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ue.Emulated,styles:e.styles||ve,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Rn("NgStandalone"),ih(n);let r=e.dependencies;return n.directiveDefs=Bl(r,!1),n.pipeDefs=Bl(r,!0),n.id=zD(n),n})}function BD(e){return At(e)||am(e)}function UD(e){return e!==null}function yt(e){return ui(()=>({type:e.type,bootstrap:e.bootstrap||ve,declarations:e.declarations||ve,imports:e.imports||ve,exports:e.exports||ve,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function $D(e,t){if(e==null)return xt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=vi.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function HD(e){if(e==null)return xt;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function xn(e){return ui(()=>{let t=oh(e);return ih(t),t})}function rh(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function oh(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||xt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||ve,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:$D(e.inputs,t),outputs:HD(e.outputs),debugInfo:null}}function ih(e){e.features?.forEach(t=>t(e))}function Bl(e,t){if(!e)return null;let n=t?cm:BD;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(UD)}function zD(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function qD(e){return Object.getPrototypeOf(e.prototype).constructor}function GD(e){let t=qD(e.type),n=!0,r=[e];for(;t;){let o;if(Le(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new v(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=oa(e.inputs),s.declaredInputs=oa(e.declaredInputs),s.outputs=oa(e.outputs);let a=o.hostBindings;a&&KD(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&YD(e,c),u&&QD(e,u),WD(e,o),Bg(e.outputs,o.outputs),Le(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===GD&&(n=!1)}}t=Object.getPrototypeOf(t)}ZD(r)}function WD(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function ZD(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=lr(o.hostAttrs,n=lr(n,o.hostAttrs))}}function oa(e){return e===xt?{}:e===ve?[]:e}function YD(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function QD(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function KD(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function JD(e,t,n){return e[t]=n}function wr(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function XD(e,t,n,r,o,i,s,a,c){let u=t.consts,l=_c(t,e,4,s||null,a||null);Rd()&&Kf(t,n,l,Zo(u,c),kf),l.mergedAttrs=lr(l.mergedAttrs,l.attrs),$d(t,l);let f=l.tView=mc(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),f.queries=t.queries.embeddedTView(l)),l}function eE(e,t,n,r,o,i,s,a,c,u){let l=n+_e,f=t.firstCreatePass?XD(l,t,e,r,o,i,s,a,c):t.data[l];mr(f,!1);let h=nE(t,e,f,n);tc()&&bc(t,e,h,f),vr(h,e);let d=Wf(h,e,h,f);return e[l]=d,vc(e,d),LD(d,f,e),Qa(f)&&Dc(t,e,f),c!=null&&Of(e,f,u),f}function tE(e,t,n,r,o,i,s,a){let c=U(),u=we(),l=Zo(u.consts,i);return eE(c,u,e,t,n,r,o,l,s,a),tE}var nE=rE;function rE(e,t,n,r){return nc(!0),t[X].createComment("")}var sh=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var ah=new E("");var oE=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new Va})}return e})(),Va=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Ir(e){return!!e&&typeof e.then=="function"}function ch(e){return!!e&&typeof e.subscribe=="function"}var uh=new E("");function Rc(e){return Cn([{provide:uh,multi:!0,useValue:e}])}var lh=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(uh,{optional:!0})??[];injector=p(Ne);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=le(this.injector,o);if(Ir(i))n.push(i);else if(ch(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ii=new E("");function iE(){xs(()=>{throw new v(600,!1)})}function sE(e){return e.isBoundToModule}var aE=10;var Lt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Dy);afterRenderManager=p(yf);zonelessEnabled=p(sc);rootEffectScheduler=p(oE);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new J;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=p(it).hasPendingTasks.pipe(_(n=>!n));constructor(){p(Nn,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(ee);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Ne.NULL){L(10);let i=n instanceof Yf;if(!this._injector.get(lh).done){let d="";throw new v(405,d)}let a;i?a=n:a=this._injector.get(Ei).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=sE(a)?void 0:this._injector.get(wn),u=r||a.selector,l=a.create(o,[],u,c),f=l.location.nativeElement,h=l.injector.get(ah,null);return h?.registerApplication(f),l.onDestroy(()=>{this.detachView(l.hostView),Po(this.components,l),h?.unregisterApplication(f)}),this._loadComponent(l),L(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){L(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(fc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=N(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,N(n),this.afterTick.next(),L(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Dn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<aE;)L(14),this.synchronizeOnce(),L(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)cE(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>gi(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Po(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Ii,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Po(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Po(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function cE(e,t,n,r){if(!n&&!gi(e))return;Hf(e,t,n&&!r?0:1)}function xc(e,t,n,r){let o=U(),i=Ja();if(wr(o,i,t)){let s=we(),a=Bd();Ev(a,o,e,t,n,r)}return xc}function uE(e,t,n,r){return wr(e,Ja(),n)?t+di(n)+r:Bt}function xo(e,t){return e<<17|t<<2}function jt(e){return e>>17&32767}function lE(e){return(e&2)==2}function dE(e,t){return e&131071|t<<17}function Ba(e){return e|2}function In(e){return(e&131068)>>2}function ia(e,t){return e&-131069|t<<2}function fE(e){return(e&1)===1}function Ua(e){return e|1}function hE(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=jt(s),c=In(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let f=n;l=f[1],(l===null||hr(f,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=jt(e[a+1]);e[r+1]=xo(h,a),h!==0&&(e[h+1]=ia(e[h+1],r)),e[a+1]=dE(e[a+1],r)}else e[r+1]=xo(a,0),a!==0&&(e[a+1]=ia(e[a+1],r)),a=r;else e[r+1]=xo(c,0),a===0?a=r:e[c+1]=ia(e[c+1],r),c=r;u&&(e[r+1]=Ba(e[r+1])),Ul(e,l,r,!0),Ul(e,l,r,!1),pE(t,l,e,r,i),s=xo(a,c),i?t.classBindings=s:t.styleBindings=s}function pE(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&hr(i,t)>=0&&(n[r+1]=Ua(n[r+1]))}function Ul(e,t,n,r){let o=e[n+1],i=t===null,s=r?jt(o):In(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];gE(c,t)&&(a=!0,e[s+1]=r?Ua(u):Ba(u)),s=r?jt(u):In(u)}a&&(e[n+1]=r?Ba(o):Ua(o))}function gE(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?hr(e,t)>=0:!1}function mE(e,t,n){let r=U(),o=Ja();if(wr(r,o,t)){let i=we(),s=Bd();gv(i,s,r,e,t,r[X],n,!1)}return mE}function $l(e,t,n,r,o){Ec(t,e,n,o?"class":"style",r)}function yE(e,t){return vE(e,t,null,!0),yE}function vE(e,t,n,r){let o=U(),i=we(),s=Um(2);if(i.firstUpdatePass&&EE(i,e,s,r),t!==Bt&&wr(o,s,t)){let a=i.data[Mn()];SE(i,a,o,o[X],e,o[s+1]=TE(t,n),r,s)}}function DE(e,t){return t>=e.expandoStartIndex}function EE(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Mn()],s=DE(e,n);ME(i,r)&&t===null&&!s&&(t=!1),t=wE(o,i,t,r),hE(o,i,t,n,s,r)}}function wE(e,t,n,r){let o=qm(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=sa(null,e,t,n,r),n=fr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=sa(o,e,t,n,r),i===null){let c=IE(e,t,r);c!==void 0&&Array.isArray(c)&&(c=sa(null,e,t,c[1],r),c=fr(c,t.attrs,r),CE(e,t,r,c))}else i=bE(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function IE(e,t,n){let r=n?t.classBindings:t.styleBindings;if(In(r)!==0)return e[jt(r)]}function CE(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[jt(o)]=r}function bE(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=fr(r,s,n)}return fr(r,t.attrs,n)}function sa(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=fr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function fr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),im(e,s,n?!0:t[++i]))}return e===void 0?null:e}function SE(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=fE(u)?Hl(c,t,n,o,In(u),s):void 0;if(!ai(l)){ai(i)||lE(u)&&(i=Hl(c,null,n,o,a,s));let f=Cd(Mn(),n);Uv(r,s,f,o,i)}}function Hl(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,f=l===null,h=n[o+1];h===Bt&&(h=f?ve:void 0);let d=f?Qs(h,r):l===r?h:void 0;if(u&&!ai(d)&&(d=Qs(c,r)),ai(d)&&(a=d,s))return a;let g=e[o+1];o=s?jt(g):In(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Qs(c,r))}return a}function ai(e){return e!==void 0}function TE(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=ue(yi(e)))),e}function ME(e,t){return(e.flags&(t?8:16))!==0}function dh(e,t,n,r){let o=U(),i=we(),s=_e+e,a=o[X],c=i.firstCreatePass?Xf(s,i,o,t,kf,Rd(),n,r):i.data[s],u=_E(i,o,c,a,t,e);o[s]=u;let l=Qa(c);return mr(c,!0),_f(a,u,c),!Pf(c)&&tc()&&bc(i,o,u,c),(xm()===0||l)&&vr(u,o),Am(),l&&(Dc(i,o,c),Ef(i,c,o)),r!==null&&Of(o,c),dh}function fh(){let e=pe();Ad()?jm():(e=e.parent,mr(e,!1));let t=e;Pm(t)&&Fm(),Om();let n=we();return n.firstCreatePass&&eh(n,t),t.classesWithoutHost!=null&&Km(t)&&$l(n,t,U(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Jm(t)&&$l(n,t,U(),t.stylesWithoutHost,!1),fh}function Ac(e,t,n,r){return dh(e,t,n,r),fh(),Ac}var _E=(e,t,n,r,o,i)=>(nc(!0),Tf(r,o,Zm()));var ci="en-US";var NE=ci;function RE(e){typeof e=="string"&&(NE=e.toLowerCase().replace(/_/g,"-"))}function zl(e,t,n){return function r(o){if(o===Function)return n;let i=Sn(e)?Ve(e.index,t):t;Tc(i,5);let s=t[he],a=ql(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=ql(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ql(e,t,n,r){let o=N(null);try{return L(6,t,n),n(r)!==!1}catch(i){return xE(e,i),!1}finally{L(7,t,n),N(o)}}function xE(e,t){let n=e[gn],r=n?n.get(Be,null):null;r&&r.handleError(t)}function Gl(e,t,n,r,o,i){let s=t[n],a=t[A],u=a.data[n].outputs[r],l=s[u],f=a.firstCreatePass?Nd(a):null,h=_d(t),d=l.subscribe(i),g=h.length;h.push(i,d),f&&f.push(o,e.index,g,-(g+1))}function Oc(e,t,n,r){let o=U(),i=we(),s=pe();return OE(i,o,o[X],s,e,t,r),Oc}function AE(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Ho],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function OE(e,t,n,r,o,i,s){let a=Qa(r),u=e.firstCreatePass?Nd(e):null,l=_d(t),f=!0;if(r.type&3||s){let h=$e(r,t),d=s?s(h):h,g=l.length,y=s?x=>s(je(x[r.index])):r.index,I=null;if(!s&&a&&(I=AE(e,t,o,r.index)),I!==null){let x=I.__ngLastListenerFn__||I;x.__ngNextListenerFn__=i,I.__ngLastListenerFn__=i,f=!1}else{i=zl(r,t,i),Ly(t,d,o,i);let x=n.listen(d,o,i);l.push(i,x),u&&u.push(o,y,g,g+1)}}else i=zl(r,t,i);if(f){let h=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let g=0;g<d.length;g+=2){let y=d[g],I=d[g+1];Gl(r,t,y,I,o,i)}if(h&&h.length)for(let g of h)Gl(r,t,g,o,o,i)}}function K0(e=1){return Wm(e)}function J0(e,t=""){let n=U(),r=we(),o=e+_e,i=r.firstCreatePass?_c(r,o,1,t,null):r.data[o],s=kE(r,n,i,t,e);n[o]=s,tc()&&bc(r,n,s,i),mr(i,!1)}var kE=(e,t,n,r,o)=>(nc(!0),rv(t[X],r));function PE(e){return hh("",e,""),PE}function hh(e,t,n){let r=U(),o=uE(r,e,t,n);return o!==Bt&&FE(r,Mn(),o),hh}function FE(e,t,n){let r=Cd(t,e);ov(e[X],r,n)}function LE(e,t,n){let r=we();if(r.firstCreatePass){let o=Le(e);$a(n,r.data,r.blueprint,o,!0),$a(t,r.data,r.blueprint,o,!1)}}function $a(e,t,n,r,o){if(e=ne(e),Array.isArray(e))for(let i=0;i<e.length;i++)$a(e[i],t,n,r,o);else{let i=we(),s=U(),a=pe(),c=pn(e)?e:ne(e.provide),u=pd(e),l=a.providerIndexes&1048575,f=a.directiveStart,h=a.providerIndexes>>20;if(pn(e)||!e.multi){let d=new Ft(u,o,ge),g=ca(c,t,o?l:l+h,f);g===-1?(Ea(Jo(a,s),i,c),aa(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[g]=d,s[g]=d)}else{let d=ca(c,t,l+h,f),g=ca(c,t,l,l+h),y=d>=0&&n[d],I=g>=0&&n[g];if(o&&!I||!o&&!y){Ea(Jo(a,s),i,c);let x=BE(o?VE:jE,n.length,o,r,u);!o&&I&&(n[g].providerFactory=x),aa(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(x),s.push(x)}else{let x=ph(n[o?g:d],u,!o&&r);aa(i,e,d>-1?d:g,x)}!o&&r&&I&&n[g].componentProviders++}}}function aa(e,t,n,r){let o=pn(t),i=hm(t);if(o||i){let c=(i?ne(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function ph(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ca(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function jE(e,t,n,r,o){return Ha(this.multi,[])}function VE(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Xo(r,r[A],this.providerFactory.index,o);s=c.slice(0,a),Ha(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Ha(i,s);return s}function Ha(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function BE(e,t,n,r,o){let i=new Ft(e,n,ge);return i.multi=[],i.index=t,i.componentProviders=0,ph(i,o,r&&!n),i}function X0(e,t=[]){return n=>{n.providersResolver=(r,o)=>LE(r,o?o(e):e,t)}}function UE(e,t){let n=e[t];return n===Bt?void 0:n}function $E(e,t,n,r,o,i){let s=t+n;return wr(e,s,o)?JD(e,s+1,i?r.call(i,o):r(o)):UE(e,s+1)}function eN(e,t){let n=we(),r,o=e+_e;n.firstCreatePass?(r=HE(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Rt(r.type,!0)),s,a=ae(ge);try{let c=Ko(!1),u=i();return Ko(c),Mm(n,U(),o,u),u}finally{ae(a)}}function HE(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function tN(e,t,n){let r=e+_e,o=U(),i=Tm(o,r);return zE(o,r)?$E(o,Vm(),t,i.transform,n,i):i.transform(n)}function zE(e,t){return e[A].data[t].pure}var za=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},gh=(()=>{class e{compileModuleSync(n){return new ja(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=ld(n),i=bf(o.declarations).reduce((s,a)=>{let c=At(a);return c&&s.push(new En(c)),s},[]);return new za(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var qE=(()=>{class e{zone=p($);changeDetectionScheduler=p(vn);applicationRef=p(Lt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),GE=new E("",{factory:()=>!1});function mh({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new $(V(m({},yh()),{scheduleInRootZone:n})),[{provide:$,useFactory:e},{provide:ar,multi:!0,useFactory:()=>{let r=p(qE,{optional:!0});return()=>r.initialize()}},{provide:ar,multi:!0,useFactory:()=>{let r=p(WE);return()=>{r.initialize()}}},t===!0?{provide:nf,useValue:!0}:[],{provide:rf,useValue:n??tf}]}function nN(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=mh({ngZoneFactory:()=>{let o=yh(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Rn("NgZone_CoalesceEvent"),new $(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Cn([{provide:GE,useValue:!0},{provide:sc,useValue:!1},r])}function yh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var WE=(()=>{class e{subscription=new G;initialized=!1;zone=p($);pendingTasks=p(it);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ZE=(()=>{class e{appRef=p(Lt);taskService=p(it);ngZone=p($);zonelessEnabled=p(sc);tracing=p(Nn,{optional:!0});disableScheduling=p(nf,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new G;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ti):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(rf,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Sa||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Cl:of;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ti+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Cl(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function YE(){return typeof $localize<"u"&&$localize.locale||ci}var kc=new E("",{providedIn:"root",factory:()=>p(kc,M.Optional|M.SkipSelf)||YE()});var qa=new E(""),QE=new E("");function rr(e){return!e.moduleRef}function KE(e){let t=rr(e)?e.r3Injector:e.moduleRef.injector,n=t.get($);return n.run(()=>{rr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Be,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),rr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(qa);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(qa);s.add(i),e.moduleRef.onDestroy(()=>{Po(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return XE(r,n,()=>{let i=t.get(lh);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(kc,ci);if(RE(s||ci),!t.get(QE,!0))return rr(e)?t.get(Lt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(rr(e)){let c=t.get(Lt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return JE(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function JE(e,t){let n=e.injector.get(Lt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new v(-403,!1);t.push(e)}function XE(e,t,n){try{let r=n();return Ir(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Fo=null;function ew(e=[],t){return Ne.create({name:t,providers:[{provide:hi,useValue:"platform"},{provide:qa,useValue:new Set([()=>Fo=null])},...e]})}function tw(e=[]){if(Fo)return Fo;let t=ew(e);return Fo=t,iE(),nw(t),t}function nw(e){let t=e.get(lc,null);le(e,()=>{t?.forEach(n=>n())})}var Cr=(()=>{class e{static __NG_ELEMENT_ID__=rw}return e})();function rw(e){return ow(pe(),U(),(e&16)===16)}function ow(e,t,n){if(Sn(e)&&!n){let r=Ve(e.index,t);return new dr(r,r)}else if(e.type&175){let r=t[Fe];return new dr(r,t)}return null}function vh(e){L(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=tw(r),i=[mh({}),{provide:vn,useExisting:ZE},...n||[]],s=new si({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return KE({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{L(9)}}function br(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function iw(e){return ks(e)}function rN(e,t){return Rs(e,t?.equal)}var Wl=class{[be];constructor(t){this[be]=t}destroy(){this[be].destroy()}};function Dh(e){let t=At(e);if(!t)return null;let n=new En(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var Z=new E("");var Ih=null;function st(){return Ih}function Pc(e){Ih??=e}var Sr=class{},Tr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Ch),providedIn:"platform"})}return e})(),Fc=new E(""),Ch=(()=>{class e extends Tr{_location;_history;_doc=p(Z);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return st().getBaseHref(this._doc)}onPopState(n){let r=st().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=st().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Ci(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Eh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function xe(e){return e&&e[0]!=="?"?`?${e}`:e}var Ae=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Si),providedIn:"root"})}return e})(),bi=new E(""),Si=(()=>{class e extends Ae{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(Z).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Ci(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+xe(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+xe(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+xe(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(w(Tr),w(bi,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),vt=(()=>{class e{_subject=new J;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=cw(Eh(wh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+xe(r))}normalize(n){return e.stripTrailingSlash(aw(this._basePath,wh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+xe(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+xe(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=xe;static joinWithSlash=Ci;static stripTrailingSlash=Eh;static \u0275fac=function(r){return new(r||e)(w(Ae))};static \u0275prov=D({token:e,factory:()=>sw(),providedIn:"root"})}return e})();function sw(){return new vt(w(Ae))}function aw(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function wh(e){return e.replace(/\/index.html$/,"")}function cw(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Lc=(()=>{class e extends Ae{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Ci(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+xe(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+xe(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(w(Tr),w(bi,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var uw=(()=>{class e{_viewContainer;_context=new Ti;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){bh(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){bh(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(ge(Dr),ge(Mc))};static \u0275dir=xn({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ti=class{$implicit=null;ngIf=null};function bh(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}function lw(e,t){return new v(2100,!1)}var dw=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,fw=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw lw(e,n);return n.replace(dw,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=rh({name:"titlecase",type:e,pure:!0})}return e})();var Sh=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=yt({type:e});static \u0275inj=mt({})}return e})();function Mr(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var jc="browser",Th="server";function Mi(e){return e===Th}var Ut=class{};var Mh=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new Vc(p(Z),window)})}return e})(),Vc=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=pw(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function pw(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Ri=new E(""),Hc=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(w(Ri),w($))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),_r=class{_doc;constructor(t){this._doc=t}manager},_i="ng-app-id";function _h(e){for(let t of e)t.remove()}function Nh(e,t){let n=t.createElement("style");return n.textContent=e,n}function mw(e,t,n,r){let o=e.head?.querySelectorAll(`style[${_i}="${t}"],link[${_i}="${t}"]`);if(o)for(let i of o)i.removeAttribute(_i),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Uc(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var zc=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Mi(i),mw(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Nh);r?.forEach(o=>this.addUsage(o,this.external,Uc))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(_h(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])_h(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Nh(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Uc(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(_i,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(w(Z),w(uc),w(dc,8),w(_n))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Bc={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},qc=/%COMP%/g;var xh="%COMP%",yw=`_nghost-${xh}`,vw=`_ngcontent-${xh}`,Dw=!0,Ew=new E("",{providedIn:"root",factory:()=>Dw});function ww(e){return vw.replace(qc,e)}function Iw(e){return yw.replace(qc,e)}function Ah(e,t){return t.map(n=>n.replace(qc,e))}var Gc=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Mi(a),this.defaultRenderer=new Nr(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ue.ShadowDom&&(r=V(m({},r),{encapsulation:Ue.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Ni?o.applyToHost(n):o instanceof Rr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,f=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Ue.Emulated:i=new Ni(c,u,r,this.appId,l,s,a,f,h);break;case Ue.ShadowDom:return new $c(c,u,n,r,s,a,this.nonce,f,h);default:i=new Rr(c,u,r,l,s,a,f,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(w(Hc),w(zc),w(uc),w(Ew),w(Z),w(_n),w($),w(dc),w(Nn,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Nr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Bc[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Rh(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Rh(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Bc[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Bc[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(nt.DashCase|nt.Important)?t.style.setProperty(n,r,o&nt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&nt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=st().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Rh(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var $c=class extends Nr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=Ah(o.id,l);for(let h of l){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=h,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let h of f){let d=Uc(h,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Rr=class extends Nr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?Ah(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ni=class extends Rr{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=ww(l),this.hostAttr=Iw(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var xi=class e extends Sr{supportsDOMEvents=!0;static makeCurrent(){Pc(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Cw();return n==null?null:bw(n)}resetBaseElement(){xr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Mr(document.cookie,t)}},xr=null;function Cw(){return xr=xr||document.head.querySelector("base"),xr?xr.getAttribute("href"):null}function bw(e){return new URL(e,document.baseURI).pathname}var Sw=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),kh=(()=>{class e extends _r{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(w(Z))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Oh=["alt","control","meta","shift"],Tw={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Mw={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Ph=(()=>{class e extends _r{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>st().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Oh.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=Tw[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Oh.forEach(s=>{if(s!==o){let a=Mw[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(w(Z))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function _w(e,t){return vh(m({rootComponent:e},Nw(t)))}function Nw(e){return{appProviders:[...kw,...e?.providers??[]],platformProviders:Ow}}function Rw(){xi.makeCurrent()}function xw(){return new Be}function Aw(){return pf(document),document}var Ow=[{provide:_n,useValue:jc},{provide:lc,useValue:Rw,multi:!0},{provide:Z,useFactory:Aw}];var kw=[{provide:hi,useValue:"root"},{provide:Be,useFactory:xw},{provide:Ri,useClass:kh,multi:!0,deps:[Z]},{provide:Ri,useClass:Ph,multi:!0,deps:[Z]},Gc,zc,Hc,{provide:Dn,useExisting:Gc},{provide:Ut,useClass:Sw},[]];var On=class{},kn=class{},Ie=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var ki=class{encodeKey(t){return Fh(t)}encodeValue(t){return Fh(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function Pw(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var Fw=/%(\d[a-f0-9])/gi,Lw={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Fh(e){return encodeURIComponent(e).replace(Fw,(t,n)=>Lw[n]??t)}function Ai(e){return`${e}`}var ct=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new ki,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=Pw(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Ai):[Ai(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Ai(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Ai(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Pi=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function jw(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Lh(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function jh(e){return typeof Blob<"u"&&e instanceof Blob}function Vh(e){return typeof FormData<"u"&&e instanceof FormData}function Vw(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Ar="Content-Type",Fi="Accept",Kc="X-Request-URL",$h="text/plain",Hh="application/json",zh=`${Hh}, ${$h}, */*`,An=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(jw(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Ie,this.context??=new Pi,!this.params)this.params=new ct,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Lh(this.body)||jh(this.body)||Vh(this.body)||Vw(this.body)?this.body:this.body instanceof ct?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Vh(this.body)?null:jh(this.body)?this.body.type||null:Lh(this.body)?null:typeof this.body=="string"?$h:this.body instanceof ct?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Hh:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,f=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,d)=>h.set(d,t.setHeaders[d]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,d)=>h.set(d,t.setParams[d]),l)),new e(n,r,s,{params:l,headers:u,context:f,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},ut=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(ut||{}),Pn=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Ie,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Or=class e extends Pn{constructor(t={}){super(t)}type=ut.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Fn=class e extends Pn{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=ut.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},at=class extends Pn{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},qh=200,Bw=204;function Wc(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var ji=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof An)i=n;else{let c;o.headers instanceof Ie?c=o.headers:c=new Ie(o.headers);let u;o.params&&(o.params instanceof ct?u=o.params:u=new ct({fromObject:o.params})),i=new An(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=C(i).pipe(ke(c=>this.handler.handle(c)));if(n instanceof An||o.observe==="events")return s;let a=s.pipe(te(c=>c instanceof Fn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(_(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(_(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(_(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new ct().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Wc(o,r))}post(n,r,o={}){return this.request("POST",n,Wc(o,r))}put(n,r,o={}){return this.request("PUT",n,Wc(o,r))}static \u0275fac=function(r){return new(r||e)(w(On))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Uw=/^\)\]\}',?\n/;function Bh(e){if(e.url)return e.url;let t=Kc.toLocaleLowerCase();return e.headers.get(t)}var Gh=new E(""),Oi=(()=>{class e{fetchImpl=p(Zc,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=p($);destroyRef=p(Vt);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new k(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(Yc,i=>r.error(new at({error:i}))),()=>o.abort()})}doRequest(n,r,o){return Zn(this,null,function*(){let i=this.createRequestInit(n),s;try{let d=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,m({signal:r},i)));$w(d),o.next({type:ut.Sent}),s=yield d}catch(d){o.error(new at({error:d,status:d.status??0,statusText:d.statusText,url:n.urlWithParams,headers:d.headers}));return}let a=new Ie(s.headers),c=s.statusText,u=Bh(s)??n.urlWithParams,l=s.status,f=null;if(n.reportProgress&&o.next(new Or({headers:a,status:l,statusText:c,url:u})),s.body){let d=s.headers.get("content-length"),g=[],y=s.body.getReader(),I=0,x,lt,H=typeof Zone<"u"&&Zone.current,Yt=!1;if(yield this.ngZone.runOutsideAngular(()=>Zn(this,null,function*(){for(;;){if(this.destroyed){yield y.cancel(),Yt=!0;break}let{done:bt,value:ys}=yield y.read();if(bt)break;if(g.push(ys),I+=ys.length,n.reportProgress){lt=n.responseType==="text"?(lt??"")+(x??=new TextDecoder).decode(ys,{stream:!0}):void 0;let Nu=()=>o.next({type:ut.DownloadProgress,total:d?+d:void 0,loaded:I,partialText:lt});H?H.run(Nu):Nu()}}})),Yt){o.complete();return}let ms=this.concatChunks(g,I);try{let bt=s.headers.get(Ar)??"";f=this.parseBody(n,ms,bt)}catch(bt){o.error(new at({error:bt,headers:new Ie(s.headers),status:s.status,statusText:s.statusText,url:Bh(s)??n.urlWithParams}));return}}l===0&&(l=f?qh:0),l>=200&&l<300?(o.next(new Fn({body:f,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new at({error:f,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(Uw,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(Fi)||(r[Fi]=zh),!n.headers.has(Ar)){let i=n.detectContentTypeHeader();i!==null&&(r[Ar]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Zc=class{};function Yc(){}function $w(e){e.then(Yc,Yc)}function Wh(e,t){return t(e)}function Hw(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function zw(e,t,n){return(r,o)=>le(n,()=>t(r,i=>e(i,o)))}var Zh=new E(""),Jc=new E(""),Yh=new E(""),Xc=new E("",{providedIn:"root",factory:()=>!0});function qw(){let e=null;return(t,n)=>{e===null&&(e=(p(Zh,{optional:!0})??[]).reduceRight(Hw,Wh));let r=p(it);if(p(Xc)){let i=r.add();return e(t,n).pipe(ht(()=>r.remove(i)))}else return e(t,n)}}var Li=(()=>{class e extends On{backend;injector;chain=null;pendingTasks=p(it);contributeToStability=p(Xc);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Jc),...this.injector.get(Yh,[])]));this.chain=r.reduceRight((o,i)=>zw(o,i,this.injector),Wh)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(ht(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(w(kn),w(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();var Gw=/^\)\]\}',?\n/,Ww=RegExp(`^${Kc}:`,"m");function Zw(e){return"responseURL"in e&&e.responseURL?e.responseURL:Ww.test(e.getAllResponseHeaders())?e.getResponseHeader(Kc):null}var Qc=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?B(r.\u0275loadImpl()):C(null)).pipe(se(()=>new k(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((y,I)=>s.setRequestHeader(y,I.join(","))),n.headers.has(Fi)||s.setRequestHeader(Fi,zh),!n.headers.has(Ar)){let y=n.detectContentTypeHeader();y!==null&&s.setRequestHeader(Ar,y)}if(n.responseType){let y=n.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let y=s.statusText||"OK",I=new Ie(s.getAllResponseHeaders()),x=Zw(s)||n.url;return c=new Or({headers:I,status:s.status,statusText:y,url:x}),c},l=()=>{let{headers:y,status:I,statusText:x,url:lt}=u(),H=null;I!==Bw&&(H=typeof s.response>"u"?s.responseText:s.response),I===0&&(I=H?qh:0);let Yt=I>=200&&I<300;if(n.responseType==="json"&&typeof H=="string"){let ms=H;H=H.replace(Gw,"");try{H=H!==""?JSON.parse(H):null}catch(bt){H=ms,Yt&&(Yt=!1,H={error:bt,text:H})}}Yt?(i.next(new Fn({body:H,headers:y,status:I,statusText:x,url:lt||void 0})),i.complete()):i.error(new at({error:H,headers:y,status:I,statusText:x,url:lt||void 0}))},f=y=>{let{url:I}=u(),x=new at({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:I||void 0});i.error(x)},h=!1,d=y=>{h||(i.next(u()),h=!0);let I={type:ut.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(I.total=y.total),n.responseType==="text"&&s.responseText&&(I.partialText=s.responseText),i.next(I)},g=y=>{let I={type:ut.UploadProgress,loaded:y.loaded};y.lengthComputable&&(I.total=y.total),i.next(I)};return s.addEventListener("load",l),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),n.reportProgress&&(s.addEventListener("progress",d),a!==null&&s.upload&&s.upload.addEventListener("progress",g)),s.send(a),i.next({type:ut.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",l),s.removeEventListener("timeout",f),n.reportProgress&&(s.removeEventListener("progress",d),a!==null&&s.upload&&s.upload.removeEventListener("progress",g)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(w(Ut))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Qh=new E(""),Yw="XSRF-TOKEN",Qw=new E("",{providedIn:"root",factory:()=>Yw}),Kw="X-XSRF-TOKEN",Jw=new E("",{providedIn:"root",factory:()=>Kw}),kr=class{},Xw=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Mr(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(w(Z),w(Qw))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function eI(e,t){let n=e.url.toLowerCase();if(!p(Qh)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=p(kr).getToken(),o=p(Jw);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Vi=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Vi||{});function Kh(e,t){return{\u0275kind:e,\u0275providers:t}}function Jh(...e){let t=[ji,Qc,Li,{provide:On,useExisting:Li},{provide:kn,useFactory:()=>p(Gh,{optional:!0})??p(Qc)},{provide:Jc,useValue:eI,multi:!0},{provide:Qh,useValue:!0},{provide:kr,useClass:Xw}];for(let n of e)t.push(...n.\u0275providers);return Cn(t)}var Uh=new E("");function Xh(){return Kh(Vi.LegacyInterceptors,[{provide:Uh,useFactory:qw},{provide:Jc,useExisting:Uh,multi:!0}])}function tI(){return Kh(Vi.Fetch,[Oi,{provide:Gh,useExisting:Oi},{provide:kn,useExisting:Oi}])}var nI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=yt({type:e});static \u0275inj=mt({providers:[Jh(Xh())]})}return e})();var ep=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(w(Z))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var T="primary",Wr=Symbol("RouteTitle"),ou=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function zt(e){return new ou(e)}function cp(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function iI(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!ze(e[n],t[n]))return!1;return!0}function ze(e,t){let n=e?iu(e):void 0,r=t?iu(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!up(e[o],t[o]))return!1;return!0}function iu(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function up(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function lp(e){return e.length>0?e[e.length-1]:null}function Ct(e){return qs(e)?e:Ir(e)?B(Promise.resolve(e)):C(e)}var sI={exact:fp,subset:hp},dp={exact:aI,subset:cI,ignored:()=>!0};function tp(e,t,n){return sI[n.paths](e.root,t.root,n.matrixParams)&&dp[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function aI(e,t){return ze(e,t)}function fp(e,t,n){if(!$t(e.segments,t.segments)||!$i(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!fp(e.children[r],t.children[r],n))return!1;return!0}function cI(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>up(e[n],t[n]))}function hp(e,t,n){return pp(e,t,t.segments,n)}function pp(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!$t(o,n)||t.hasChildren()||!$i(o,n,r))}else if(e.segments.length===n.length){if(!$t(e.segments,n)||!$i(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!hp(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!$t(e.segments,o)||!$i(e.segments,o,r)||!e.children[T]?!1:pp(e.children[T],t,i,r)}}function $i(e,t,n){return t.every((r,o)=>dp[n](e[o].parameters,r.parameters))}var Ge=class{root;queryParams;fragment;_queryParamMap;constructor(t=new F([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=zt(this.queryParams),this._queryParamMap}toString(){return dI.serialize(this)}},F=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Hi(this)}},Dt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=zt(this.parameters),this._parameterMap}toString(){return mp(this)}};function uI(e,t){return $t(e,t)&&e.every((n,r)=>ze(n.parameters,t[r].parameters))}function $t(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function lI(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===T&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==T&&(n=n.concat(t(o,r)))}),n}var qt=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new Et,providedIn:"root"})}return e})(),Et=class{parse(t){let n=new au(t);return new Ge(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Pr(t.root,!0)}`,r=pI(t.queryParams),o=typeof t.fragment=="string"?`#${fI(t.fragment)}`:"";return`${n}${r}${o}`}},dI=new Et;function Hi(e){return e.segments.map(t=>mp(t)).join("/")}function Pr(e,t){if(!e.hasChildren())return Hi(e);if(t){let n=e.children[T]?Pr(e.children[T],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==T&&r.push(`${o}:${Pr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=lI(e,(r,o)=>o===T?[Pr(e.children[T],!1)]:[`${o}:${Pr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[T]!=null?`${Hi(e)}/${n[0]}`:`${Hi(e)}/(${n.join("//")})`}}function gp(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Bi(e){return gp(e).replace(/%3B/gi,";")}function fI(e){return encodeURI(e)}function su(e){return gp(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function zi(e){return decodeURIComponent(e)}function np(e){return zi(e.replace(/\+/g,"%20"))}function mp(e){return`${su(e.path)}${hI(e.parameters)}`}function hI(e){return Object.entries(e).map(([t,n])=>`;${su(t)}=${su(n)}`).join("")}function pI(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Bi(n)}=${Bi(o)}`).join("&"):`${Bi(n)}=${Bi(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var gI=/^[^\/()?;#]+/;function eu(e){let t=e.match(gI);return t?t[0]:""}var mI=/^[^\/()?;=#]+/;function yI(e){let t=e.match(mI);return t?t[0]:""}var vI=/^[^=?&#]+/;function DI(e){let t=e.match(vI);return t?t[0]:""}var EI=/^[^&#]+/;function wI(e){let t=e.match(EI);return t?t[0]:""}var au=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new F([],{}):new F([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[T]=new F(t,n)),r}parseSegment(){let t=eu(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new Dt(zi(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=yI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=eu(this.remaining);o&&(r=o,this.capture(r))}t[zi(n)]=zi(r)}parseQueryParam(t){let n=DI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=wI(this.remaining);s&&(r=s,this.capture(r))}let o=np(n),i=np(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=eu(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=T);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[T]:new F([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function yp(e){return e.segments.length>0?new F([],{[T]:e}):e}function vp(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=vp(o);if(r===T&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new F(e.segments,t);return II(n)}function II(e){if(e.numberOfChildren===1&&e.children[T]){let t=e.children[T];return new F(e.segments.concat(t.segments),t.children)}return e}function wt(e){return e instanceof Ge}function Dp(e,t,n=null,r=null){let o=Ep(e);return wp(o,t,n,r)}function Ep(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new F(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=yp(r);return t??o}function wp(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return tu(o,o,o,n,r);let i=CI(t);if(i.toRoot())return tu(o,o,new F([],{}),n,r);let s=bI(i,o,e),a=s.processChildren?Lr(s.segmentGroup,s.index,i.commands):Cp(s.segmentGroup,s.index,i.commands);return tu(o,s.segmentGroup,a,n,r)}function Gi(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Vr(e){return typeof e=="object"&&e!=null&&e.outlets}function tu(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=Ip(e,t,n);let a=yp(vp(s));return new Ge(a,i,o)}function Ip(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Ip(i,t,n)}),new F(e.segments,r)}var Wi=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Gi(r[0]))throw new v(4003,!1);let o=r.find(Vr);if(o&&o!==lp(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function CI(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Wi(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Wi(n,t,r)}var Vn=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function bI(e,t,n){if(e.isAbsolute)return new Vn(t,!0,0);if(!n)return new Vn(t,!1,NaN);if(n.parent===null)return new Vn(n,!0,0);let r=Gi(e.commands[0])?0:1,o=n.segments.length-1+r;return SI(n,o,e.numberOfDoubleDots)}function SI(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new Vn(r,!1,o-i)}function TI(e){return Vr(e[0])?e[0].outlets:{[T]:e}}function Cp(e,t,n){if(e??=new F([],{}),e.segments.length===0&&e.hasChildren())return Lr(e,t,n);let r=MI(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new F(e.segments.slice(0,r.pathIndex),{});return i.children[T]=new F(e.segments.slice(r.pathIndex),e.children),Lr(i,0,o)}else return r.match&&o.length===0?new F(e.segments,{}):r.match&&!e.hasChildren()?cu(e,t,n):r.match?Lr(e,0,o):cu(e,t,n)}function Lr(e,t,n){if(n.length===0)return new F(e.segments,{});{let r=TI(n),o={};if(Object.keys(r).some(i=>i!==T)&&e.children[T]&&e.numberOfChildren===1&&e.children[T].segments.length===0){let i=Lr(e.children[T],t,n);return new F(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Cp(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new F(e.segments,o)}}function MI(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(Vr(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!op(c,u,s))return i;r+=2}else{if(!op(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function cu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(Vr(i)){let c=_I(i.outlets);return new F(r,c)}if(o===0&&Gi(n[0])){let c=e.segments[t];r.push(new Dt(c.path,rp(n[0]))),o++;continue}let s=Vr(i)?i.outlets[T]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Gi(a)?(r.push(new Dt(s,rp(a))),o+=2):(r.push(new Dt(s,{})),o++)}return new F(r,{})}function _I(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=cu(new F([],{}),0,r))}),t}function rp(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function op(e,t,n){return e==n.path&&ze(t,n.parameters)}var qi="imperative",K=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(K||{}),ye=class{id;url;constructor(t,n){this.id=t,this.url=n}},It=class extends ye{type=K.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Ce=class extends ye{urlAfterRedirects;type=K.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},de=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(de||{}),Un=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Un||{}),qe=class extends ye{reason;code;type=K.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},We=class extends ye{reason;code;type=K.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},$n=class extends ye{error;target;type=K.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Br=class extends ye{urlAfterRedirects;state;type=K.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Zi=class extends ye{urlAfterRedirects;state;type=K.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Yi=class extends ye{urlAfterRedirects;state;shouldActivate;type=K.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Qi=class extends ye{urlAfterRedirects;state;type=K.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ki=class extends ye{urlAfterRedirects;state;type=K.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ji=class{route;type=K.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Xi=class{route;type=K.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},es=class{snapshot;type=K.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ts=class{snapshot;type=K.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ns=class{snapshot;type=K.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},rs=class{snapshot;type=K.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Hn=class{routerEvent;position;anchor;type=K.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},Ur=class{},zn=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function NI(e,t){return e.providers&&!e._injector&&(e._injector=Er(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Oe(e){return e.outlet||T}function RI(e,t){let n=e.filter(r=>Oe(r)===t);return n.push(...e.filter(r=>Oe(r)!==t)),n}function Zr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var os=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Zr(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Gt(this.rootInjector)}},Gt=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new os(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(w(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),is=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=uu(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=uu(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=lu(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return lu(t,this._root).map(n=>n.value)}};function uu(e,t){if(e===t.value)return t;for(let n of t.children){let r=uu(e,n);if(r)return r}return null}function lu(e,t){if(e===t.value)return[t];for(let n of t.children){let r=lu(e,n);if(r.length)return r.unshift(t),r}return[]}var me=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function jn(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var $r=class extends is{snapshot;constructor(t,n){super(t),this.snapshot=n,vu(this,t)}toString(){return this.snapshot.toString()}};function bp(e){let t=xI(e),n=new Q([new Dt("",{})]),r=new Q({}),o=new Q({}),i=new Q({}),s=new Q(""),a=new Ze(n,r,i,s,o,T,e,t.root);return a.snapshot=t.root,new $r(new me(a,[]),t)}function xI(e){let t={},n={},r={},o="",i=new Ht([],t,r,o,n,T,e,null,{});return new Hr("",new me(i,[]))}var Ze=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(_(u=>u[Wr]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(_(t=>zt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(_(t=>zt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ss(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:m(m({},t.params),e.params),data:m(m({},t.data),e.data),resolve:m(m(m(m({},e.data),t.data),o?.data),e._resolvedData)}:r={params:m({},e.params),data:m({},e.data),resolve:m(m({},e.data),e._resolvedData??{})},o&&Tp(o)&&(r.resolve[Wr]=o.title),r}var Ht=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Wr]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=zt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=zt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Hr=class extends is{url;constructor(t,n){super(n),this.url=t,vu(this,n)}toString(){return Sp(this._root)}};function vu(e,t){t.value._routerState=e,t.children.forEach(n=>vu(e,n))}function Sp(e){let t=e.children.length>0?` { ${e.children.map(Sp).join(", ")} } `:"";return`${e.value}${t}`}function nu(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,ze(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),ze(t.params,n.params)||e.paramsSubject.next(n.params),iI(t.url,n.url)||e.urlSubject.next(n.url),ze(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function du(e,t){let n=ze(e.params,t.params)&&uI(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||du(e.parent,t.parent))}function Tp(e){return typeof e.title=="string"||e.title===null}var Mp=new E(""),Du=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=T;activateEvents=new ce;deactivateEvents=new ce;attachEvents=new ce;detachEvents=new ce;routerOutletData=af(void 0);parentContexts=p(Gt);location=p(Dr);changeDetector=p(Cr);inputBinder=p(Yr,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new fu(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=xn({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[pr]})}return e})(),fu=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Ze?this.route:t===Gt?this.childContexts:t===Mp?this.outletData:this.parent.get(t,n)}},Yr=new E(""),Eu=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=nr([r.queryParams,r.params,r.data]).pipe(se(([i,s,a],c)=>(a=m(m(m({},i),s),a),c===0?C(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Dh(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),wu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=nh({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Ac(0,"router-outlet")},dependencies:[Du],encapsulation:2})}return e})();function Iu(e){let t=e.children&&e.children.map(Iu),n=t?V(m({},e),{children:t}):m({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==T&&(n.component=wu),n}function AI(e,t,n){let r=zr(e,t._root,n?n._root:void 0);return new $r(r,t)}function zr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=OI(e,t,n);return new me(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>zr(e,a)),s}}let r=kI(t.value),o=t.children.map(i=>zr(e,i));return new me(r,o)}}function OI(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return zr(e,r,o);return zr(e,r)})}function kI(e){return new Ze(new Q(e.url),new Q(e.params),new Q(e.queryParams),new Q(e.fragment),new Q(e.data),e.outlet,e.component,e)}var qn=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},_p="ngNavigationCancelingError";function as(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=wt(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Np(!1,de.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Np(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[_p]=!0,n.cancellationCode=t,n}function PI(e){return Rp(e)&&wt(e.url)}function Rp(e){return!!e&&e[_p]}var FI=(e,t,n,r)=>_(o=>(new hu(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),hu=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),nu(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=jn(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=jn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=jn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=jn(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new rs(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ts(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(nu(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),nu(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},cs=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Bn=class{component;route;constructor(t,n){this.component=t,this.route=n}};function LI(e,t,n){let r=e._root,o=t?t._root:null;return Fr(r,o,n,[r.value])}function jI(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Wn(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!ed(e)?e:t.get(e):r}function Fr(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=jn(t);return e.children.forEach(s=>{VI(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>jr(a,n.getContext(s),o)),o}function VI(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=BI(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new cs(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Fr(e,t,a?a.children:null,r,o):Fr(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Bn(a.outlet.component,s))}else s&&jr(t,a,o),o.canActivateChecks.push(new cs(r)),i.component?Fr(e,null,a?a.children:null,r,o):Fr(e,null,n,r,o);return o}function BI(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!$t(e.url,t.url);case"pathParamsOrQueryParamsChange":return!$t(e.url,t.url)||!ze(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!du(e,t)||!ze(e.queryParams,t.queryParams);case"paramsChange":default:return!du(e,t)}}function jr(e,t,n){let r=jn(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?jr(s,t.children.getContext(i),n):jr(s,null,n):jr(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Bn(t.outlet.component,o)):n.canDeactivateChecks.push(new Bn(null,o)):n.canDeactivateChecks.push(new Bn(null,o))}function Qr(e){return typeof e=="function"}function UI(e){return typeof e=="boolean"}function $I(e){return e&&Qr(e.canLoad)}function HI(e){return e&&Qr(e.canActivate)}function zI(e){return e&&Qr(e.canActivateChild)}function qI(e){return e&&Qr(e.canDeactivate)}function GI(e){return e&&Qr(e.canMatch)}function xp(e){return e instanceof Ke||e?.name==="EmptyError"}var Ui=Symbol("INITIAL_VALUE");function Gn(){return se(e=>nr(e.map(t=>t.pipe(Xe(1),Zs(Ui)))).pipe(_(t=>{for(let n of t)if(n!==!0){if(n===Ui)return Ui;if(n===!1||WI(n))return n}return!0}),te(t=>t!==Ui),Xe(1)))}function WI(e){return wt(e)||e instanceof qn}function ZI(e,t){return q(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(V(m({},n),{guardsResult:!0})):YI(s,r,o,e).pipe(q(a=>a&&UI(a)?QI(r,i,e,t):C(a)),_(a=>V(m({},n),{guardsResult:a})))})}function YI(e,t,n,r){return B(e).pipe(q(o=>tC(o.component,o.route,n,t,r)),et(o=>o!==!0,!0))}function QI(e,t,n,r){return B(t).pipe(ke(o=>sn(JI(o.route.parent,r),KI(o.route,r),eC(e,o.path,n),XI(e,o.route,n))),et(o=>o!==!0,!0))}function KI(e,t){return e!==null&&t&&t(new ns(e)),C(!0)}function JI(e,t){return e!==null&&t&&t(new es(e)),C(!0)}function XI(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>_o(()=>{let s=Zr(t)??n,a=Wn(i,s),c=HI(a)?a.canActivate(t,e):le(s,()=>a(t,e));return Ct(c).pipe(et())}));return C(o).pipe(Gn())}function eC(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>jI(s)).filter(s=>s!==null).map(s=>_o(()=>{let a=s.guards.map(c=>{let u=Zr(s.node)??n,l=Wn(c,u),f=zI(l)?l.canActivateChild(r,e):le(u,()=>l(r,e));return Ct(f).pipe(et())});return C(a).pipe(Gn())}));return C(i).pipe(Gn())}function tC(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=Zr(t)??o,u=Wn(a,c),l=qI(u)?u.canDeactivate(e,t,n,r):le(c,()=>u(e,t,n,r));return Ct(l).pipe(et())});return C(s).pipe(Gn())}function nC(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=Wn(s,e),c=$I(a)?a.canLoad(t,n):le(e,()=>a(t,n));return Ct(c)});return C(i).pipe(Gn(),Ap(r))}function Ap(e){return Us(z(t=>{if(typeof t!="boolean")throw as(e,t)}),_(t=>t===!0))}function rC(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=Wn(s,e),c=GI(a)?a.canMatch(t,n):le(e,()=>a(t,n));return Ct(c)});return C(i).pipe(Gn(),Ap(r))}var qr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Gr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Ln(e){return rn(new qr(e))}function oC(e){return rn(new v(4e3,!1))}function iC(e){return rn(Np(!1,de.GuardRejected))}var pu=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[T])return oC(`${t.redirectTo}`);o=o.children[T]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,params:d,data:g,title:y}=o,I=le(i,()=>a({params:d,data:g,queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,title:y}));if(I instanceof Ge)throw new Gr(I);n=I}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Gr(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Ge(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new F(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},gu={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function sC(e,t,n,r,o){let i=Op(e,t,n);return i.matched?(r=NI(t,r),rC(r,t,n,o).pipe(_(s=>s===!0?i:m({},gu)))):C(i)}function Op(e,t,n){if(t.path==="**")return aC(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?m({},gu):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||cp)(n,e,t);if(!o)return m({},gu);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?m(m({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function aC(e){return{matched:!0,parameters:e.length>0?lp(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ip(e,t,n,r){return n.length>0&&lC(e,n,r)?{segmentGroup:new F(t,uC(r,new F(n,e.children))),slicedSegments:[]}:n.length===0&&dC(e,n,r)?{segmentGroup:new F(e.segments,cC(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new F(e.segments,e.children),slicedSegments:n}}function cC(e,t,n,r){let o={};for(let i of n)if(ls(e,t,i)&&!r[Oe(i)]){let s=new F([],{});o[Oe(i)]=s}return m(m({},r),o)}function uC(e,t){let n={};n[T]=t;for(let r of e)if(r.path===""&&Oe(r)!==T){let o=new F([],{});n[Oe(r)]=o}return n}function lC(e,t,n){return n.some(r=>ls(e,t,r)&&Oe(r)!==T)}function dC(e,t,n){return n.some(r=>ls(e,t,r))}function ls(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function fC(e,t,n){return t.length===0&&!e.children[n]}var mu=class{};function hC(e,t,n,r,o,i,s="emptyOnly"){return new yu(e,t,n,r,o,s,i).recognize()}var pC=31,yu=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new pu(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=ip(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(_(({children:n,rootSnapshot:r})=>{let o=new me(r,n),i=new Hr("",o),s=Dp(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Ht([],Object.freeze({}),Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),T,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,T,n).pipe(_(r=>({children:r,rootSnapshot:n})),Je(r=>{if(r instanceof Gr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof qr?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(_(s=>s instanceof me?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return B(i).pipe(ke(s=>{let a=r.children[s],c=RI(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Ws((s,a)=>(s.push(...a),s)),ft(null),Gs(),q(s=>{if(s===null)return Ln(r);let a=kp(s);return gC(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return B(n).pipe(ke(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(Je(u=>{if(u instanceof qr)return C(null);throw u}))),et(c=>!!c),Je(c=>{if(xp(c))return fC(r,o,i)?C(new mu):Ln(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Oe(r)!==s&&(s===T||!ls(o,i,r))?Ln(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Ln(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:f,remainingSegments:h}=Op(n,o,i);if(!c)return Ln(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>pC&&(this.allowRedirects=!1));let d=new Ht(i,u,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,sp(o),Oe(o),o.component??o._loadedComponent??null,o,ap(o)),g=ss(d,a,this.paramsInheritanceStrategy);d.params=Object.freeze(g.params),d.data=Object.freeze(g.data);let y=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,f,d,t);return this.applyRedirects.lineralizeSegments(o,y).pipe(q(I=>this.processSegment(t,r,n,I.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=sC(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(se(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(se(({routes:u})=>{let l=r._loadedInjector??t,{parameters:f,consumedSegments:h,remainingSegments:d}=c,g=new Ht(h,f,Object.freeze(m({},this.urlTree.queryParams)),this.urlTree.fragment,sp(r),Oe(r),r.component??r._loadedComponent??null,r,ap(r)),y=ss(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(y.params),g.data=Object.freeze(y.data);let{segmentGroup:I,slicedSegments:x}=ip(n,h,d,u);if(x.length===0&&I.hasChildren())return this.processChildren(l,u,I,g).pipe(_(H=>new me(g,H)));if(u.length===0&&x.length===0)return C(new me(g,[]));let lt=Oe(r)===i;return this.processSegment(l,u,I,x,lt?T:i,!0,g).pipe(_(H=>new me(g,H instanceof me?[H]:[])))}))):Ln(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):nC(t,n,r,this.urlSerializer).pipe(q(o=>o?this.configLoader.loadChildren(t,n).pipe(z(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):iC(n))):C({routes:[],injector:t})}};function gC(e){e.sort((t,n)=>t.value.outlet===T?-1:n.value.outlet===T?1:t.value.outlet.localeCompare(n.value.outlet))}function mC(e){let t=e.value.routeConfig;return t&&t.path===""}function kp(e){let t=[],n=new Set;for(let r of e){if(!mC(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=kp(r.children);t.push(new me(r.value,o))}return t.filter(r=>!n.has(r))}function sp(e){return e.data||{}}function ap(e){return e.resolve||{}}function yC(e,t,n,r,o,i){return q(s=>hC(e,t,n,r,s.extractedUrl,o,i).pipe(_(({state:a,tree:c})=>V(m({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function vC(e,t){return q(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Pp(c))s.add(u);let a=0;return B(s).pipe(ke(c=>i.has(c)?DC(c,r,e,t):(c.data=ss(c,c.parent,e).resolve,C(void 0))),z(()=>a++),an(1),q(c=>a===s.size?C(n):oe))})}function Pp(e){let t=e.children.map(n=>Pp(n)).flat();return[e,...t]}function DC(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Tp(o)&&(i[Wr]=o.title),EC(i,e,t,r).pipe(_(s=>(e._resolvedData=s,e.data=ss(e,e.parent,n).resolve,null)))}function EC(e,t,n,r){let o=iu(e);if(o.length===0)return C({});let i={};return B(o).pipe(q(s=>wC(e[s],t,n,r).pipe(et(),z(a=>{if(a instanceof qn)throw as(new Et,a);i[s]=a}))),an(1),_(()=>i),Je(s=>xp(s)?oe:rn(s)))}function wC(e,t,n,r){let o=Zr(t)??r,i=Wn(e,o),s=i.resolve?i.resolve(t,n):le(o,()=>i(t,n));return Ct(s)}function ru(e){return se(t=>{let n=e(t);return n?B(n).pipe(_(()=>t)):C(t)})}var Cu=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===T);return r}getResolvedTitleForRoute(n){return n.data[Wr]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Fp),providedIn:"root"})}return e})(),Fp=(()=>{class e extends Cu{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(w(ep))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wt=new E("",{providedIn:"root",factory:()=>({})}),Zt=new E(""),ds=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(gh);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Ct(n.loadComponent()).pipe(_(jp),z(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),ht(()=>{this.componentLoaders.delete(n)})),o=new nn(r,()=>new J).pipe(tn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Lp(r,this.compiler,n,this.onLoadEndListener).pipe(ht(()=>{this.childrenLoaders.delete(r)})),s=new nn(i,()=>new J).pipe(tn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Lp(e,t,n,r){return Ct(e.loadChildren()).pipe(_(jp),q(o=>o instanceof Nc||Array.isArray(o)?C(o):B(t.compileModuleAsync(o))),_(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Zt,[],{optional:!0,self:!0}).flat()),{routes:s.map(Iu),injector:i}}))}function IC(e){return e&&typeof e=="object"&&"default"in e}function jp(e){return IC(e)?e.default:e}var fs=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(CC),providedIn:"root"})}return e})(),CC=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bu=new E(""),Su=new E("");function Vp(e,t,n){let r=e.get(Su),o=e.get(Z);return e.get($).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),bC(e))),{onViewTransitionCreated:c}=r;return c&&le(e,()=>c({transition:a,from:t,to:n})),s})}function bC(e){return new Promise(t=>{hc({read:()=>setTimeout(t)},{injector:e})})}var Tu=new E(""),hs=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new J;transitionAbortSubject=new J;configLoader=p(ds);environmentInjector=p(ee);destroyRef=p(Vt);urlSerializer=p(qt);rootContexts=p(Gt);location=p(vt);inputBindingEnabled=p(Yr,{optional:!0})!==null;titleStrategy=p(Cu);options=p(Wt,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(fs);createViewTransition=p(bu,{optional:!0});navigationErrorHandler=p(Tu,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Ji(o)),r=o=>this.events.next(new Xi(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(V(m({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new Q(null),this.transitions.pipe(te(r=>r!==null),se(r=>{let o=!1,i=!1;return C(r).pipe(se(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),oe;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?V(m({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new We(s.id,this.urlSerializer.serialize(s.rawUrl),u,Un.IgnoredSameUrlNavigation)),s.resolve(!1),oe}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(se(u=>(this.events.next(new It(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?oe:Promise.resolve(u))),yC(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),z(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=V(m({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new Br(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:f,restoredState:h,extras:d}=s,g=new It(u,this.urlSerializer.serialize(l),f,h);this.events.next(g);let y=bp(this.rootComponentType).snapshot;return this.currentTransition=r=V(m({},s),{targetSnapshot:y,urlAfterRedirects:l,extras:V(m({},d),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let u="";return this.events.next(new We(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Un.IgnoredByUrlHandlingStrategy)),s.resolve(!1),oe}}),z(s=>{let a=new Zi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),_(s=>(this.currentTransition=r=V(m({},s),{guards:LI(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),ZI(this.environmentInjector,s=>this.events.next(s)),z(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw as(this.urlSerializer,s.guardsResult);let a=new Yi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),te(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",de.GuardRejected),!1)),ru(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(z(a=>{let c=new Qi(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),se(a=>{let c=!1;return C(a).pipe(vC(this.paramsInheritanceStrategy,this.environmentInjector),z({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",de.NoDataFromResolver)}}))}),z(a=>{let c=new Ki(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),ru(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(z(l=>{c.component=l}),_(()=>{})));for(let l of c.children)u.push(...a(l));return u};return nr(a(s.targetSnapshot.root)).pipe(ft(null),Xe(1))}),ru(()=>this.afterPreactivation()),se(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?B(c).pipe(_(()=>r)):C(r)}),_(s=>{let a=AI(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=V(m({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),z(()=>{this.events.next(new Ur)}),FI(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Xe(1),z({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Ce(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Ys(this.transitionAbortSubject.pipe(z(s=>{throw s}))),ht(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",de.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),Je(s=>{if(this.destroyed)return r.resolve(!1),oe;if(i=!0,Rp(s))this.events.next(new qe(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),PI(s)?this.events.next(new zn(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new $n(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=le(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof qn){let{message:u,cancellationCode:l}=as(this.urlSerializer,c);this.events.next(new qe(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new zn(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return oe}))}))}cancelNavigationTransition(n,r,o){let i=new qe(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function SC(e){return e!==qi}var Bp=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(TC),providedIn:"root"})}return e})(),us=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},TC=(()=>{class e extends us{static \u0275fac=(()=>{let n;return function(o){return(n||(n=oc(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Up=(()=>{class e{urlSerializer=p(qt);options=p(Wt,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(vt);urlHandlingStrategy=p(fs);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Ge;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Ge?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=bp(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(MC),providedIn:"root"})}return e})(),MC=(()=>{class e extends Up{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof It?this.updateStateMemento():n instanceof We?this.commitTransition(r):n instanceof Br?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Ur?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof qe&&(n.code===de.GuardRejected||n.code===de.NoDataFromResolver)?this.restoreHistory(r):n instanceof $n?this.restoreHistory(r,!0):n instanceof Ce&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=m(m({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=m(m({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=oc(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ps(e,t){e.events.pipe(te(n=>n instanceof Ce||n instanceof qe||n instanceof $n||n instanceof We),_(n=>n instanceof Ce||n instanceof We?0:(n instanceof qe?n.code===de.Redirect||n.code===de.SupersededByNewNavigation:!1)?2:1),te(n=>n!==2),Xe(1)).subscribe(()=>{t()})}var _C={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},NC={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ye=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(sh);stateManager=p(Up);options=p(Wt,{optional:!0})||{};pendingTasks=p(it);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(hs);urlSerializer=p(qt);location=p(vt);urlHandlingStrategy=p(fs);_events=new J;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Bp);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Zt,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Yr,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new G;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof qe&&r.code!==de.Redirect&&r.code!==de.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Ce)this.navigated=!0;else if(r instanceof zn){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=m({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||SC(o.source)},s);this.scheduleNavigation(a,qi,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}xC(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),qi,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=m({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Iu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=m(m({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let f;try{let h=o?o.snapshot:this.routerState.snapshot.root;f=Ep(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return wp(f,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=wt(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,qi,null,r)}navigate(n,r={skipLocationChange:!1}){return RC(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=m({},_C):r===!1?o=m({},NC):o=r,wt(n))return tp(this.currentUrlTree,n,o);let i=this.parseUrl(n);return tp(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((f,h)=>{a=f,c=h});let l=this.pendingTasks.add();return ps(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function RC(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}function xC(e){return!(e instanceof Ur)&&!(e instanceof zn)}var $p=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new J;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof Ce&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(wt(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:If(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:wt(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(ge(Ye),ge(Ze),ic("tabindex"),ge(wi),ge(yr),ge(Ae))};static \u0275dir=xn({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&Oc("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&xc("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",br],skipLocationChange:[2,"skipLocationChange","skipLocationChange",br],replaceUrl:[2,"replaceUrl","replaceUrl",br],routerLink:"routerLink"},features:[pr]})}return e})();var Kr=class{};var Hp=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(te(n=>n instanceof Ce),ke(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Er(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return B(o).pipe(on())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=C(null);let i=o.pipe(q(s=>s===null?C(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return B([i,s]).pipe(on())}else return i})}static \u0275fac=function(r){return new(r||e)(w(Ye),w(ee),w(Kr),w(ds))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),zp=new E(""),AC=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof It?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Ce?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof We&&n.code===Un.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Hn&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Hn(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Qf()};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function OC(e,...t){return Cn([{provide:Zt,multi:!0,useValue:e},[],{provide:Ze,useFactory:qp,deps:[Ye]},{provide:Ii,multi:!0,useFactory:Gp},t.map(n=>n.\u0275providers)])}function qp(e){return e.routerState.root}function Jr(e,t){return{\u0275kind:e,\u0275providers:t}}function Gp(){let e=p(Ne);return t=>{let n=e.get(Lt);if(t!==n.components[0])return;let r=e.get(Ye),o=e.get(Wp);e.get(_u)===1&&r.initialNavigation(),e.get(Qp,null,M.Optional)?.setUpPreloading(),e.get(zp,null,M.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Wp=new E("",{factory:()=>new J}),_u=new E("",{providedIn:"root",factory:()=>1});function Zp(){let e=[{provide:_u,useValue:0},Rc(()=>{let t=p(Ne);return t.get(Fc,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(Ye),i=t.get(Wp);ps(o,()=>{r(!0)}),t.get(hs).afterPreactivation=()=>(r(!0),i.closed?C(void 0):i),o.initialNavigation()}))})];return Jr(2,e)}function Yp(){let e=[Rc(()=>{p(Ye).setUpLocationChangeListener()}),{provide:_u,useValue:2}];return Jr(3,e)}var Qp=new E("");function Kp(e){return Jr(0,[{provide:Qp,useExisting:Hp},{provide:Kr,useExisting:e}])}function Jp(){return Jr(8,[Eu,{provide:Yr,useExisting:Eu}])}function Xp(e){Rn("NgRouterViewTransitions");let t=[{provide:bu,useValue:Vp},{provide:Su,useValue:m({skipNextTransition:!!e?.skipInitialTransition},e)}];return Jr(9,t)}var eg=[vt,{provide:qt,useClass:Et},Ye,Gt,{provide:Ze,useFactory:qp,deps:[Ye]},ds,[]],kC=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[eg,[],{provide:Zt,multi:!0,useValue:n},[],r?.errorHandler?{provide:Tu,useValue:r.errorHandler}:[],{provide:Wt,useValue:r||{}},r?.useHash?FC():LC(),PC(),r?.preloadingStrategy?Kp(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?jC(r):[],r?.bindToComponentInputs?Jp().\u0275providers:[],r?.enableViewTransitions?Xp().\u0275providers:[],VC()]}}static forChild(n){return{ngModule:e,providers:[{provide:Zt,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=yt({type:e});static \u0275inj=mt({})}return e})();function PC(){return{provide:zp,useFactory:()=>{let e=p(Mh),t=p($),n=p(Wt),r=p(hs),o=p(qt);return n.scrollOffset&&e.setOffset(n.scrollOffset),new AC(o,r,e,t,n)}}}function FC(){return{provide:Ae,useClass:Lc}}function LC(){return{provide:Ae,useClass:Si}}function jC(e){return[e.initialNavigation==="disabled"?Yp().\u0275providers:[],e.initialNavigation==="enabledBlocking"?Zp().\u0275providers:[]]}var Mu=new E("");function VC(){return[{provide:Mu,useFactory:Gp},{provide:Ii,multi:!0,useExisting:Mu}]}var tg={production:!1,apiUrl:"http://localhost:3000/api"};var gs=class e{constructor(t){this.http=t}apiUrl=tg.apiUrl;getHeaders(){let t=localStorage.getItem("auth_token"),n=new Ie({"Content-Type":"application/json"});return t&&(n=n.set("Authorization",`Bearer ${t}`)),n}get(t){return this.http.get(`${this.apiUrl}/${t}`,{headers:this.getHeaders()})}post(t,n){return this.http.post(`${this.apiUrl}/${t}`,n,{headers:this.getHeaders()})}put(t,n){return this.http.put(`${this.apiUrl}/${t}`,n,{headers:this.getHeaders()})}delete(t){return this.http.delete(`${this.apiUrl}/${t}`,{headers:this.getHeaders()})}static \u0275fac=function(n){return new(n||e)(w(ji))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})};var ng=class e{constructor(t){this.apiService=t;this.loadUserFromStorage()}currentUserSubject=new Q(null);currentUser$=this.currentUserSubject.asObservable();tokenKey="auth_token";loadUserFromStorage(){let t=localStorage.getItem(this.tokenKey);if(t)try{let n=JSON.parse(atob(t.split(".")[1]));n.exp*1e3>Date.now()?this.getUserProfile(n.id).subscribe({error:()=>{this.logout()}}):this.logout()}catch{this.logout()}}login(t,n){return this.apiService.post("auth/login",{email:t,password:n}).pipe(z(r=>{localStorage.setItem(this.tokenKey,r.token),this.currentUserSubject.next(r.user)}))}register(t,n,r,o="patient"){return this.apiService.post("auth/register",{name:t,email:n,password:r,role:o}).pipe(z(i=>{localStorage.setItem(this.tokenKey,i.token),this.currentUserSubject.next(i.user)}))}logout(){localStorage.removeItem(this.tokenKey),this.currentUserSubject.next(null)}getUserProfile(t){let n=t;if(!n){let r=this.getToken();if(r)try{n=JSON.parse(atob(r.split(".")[1])).id}catch{throw new Error("Invalid token")}else throw new Error("No token found")}return this.apiService.get(`users/${n}`).pipe(z(r=>{this.currentUserSubject.next(r)}))}isLoggedIn(){return!!localStorage.getItem(this.tokenKey)}getToken(){return localStorage.getItem(this.tokenKey)}static \u0275fac=function(n){return new(n||e)(w(gs))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})};export{m as a,V as b,J as c,B as d,_ as e,Fg as f,v as g,Jl as h,D as i,mt as j,E as k,p as l,um as m,pr as n,oc as o,ce as p,yr as q,B0 as r,U0 as s,wi as t,ge as u,nh as v,yt as w,xn as x,GD as y,tE as z,Ir as A,mE as B,yE as C,dh as D,fh as E,Ac as F,Oc as G,K0 as H,J0 as I,PE as J,hh as K,X0 as L,eN as M,tN as N,nN as O,iw as P,rN as Q,st as R,uw as S,fw as T,Sh as U,_w as V,Jh as W,tI as X,nI as Y,Du as Z,Ye as _,$p as $,OC as aa,kC as ba,ng as ca};
