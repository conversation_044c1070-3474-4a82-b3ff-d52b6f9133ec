async function testLogin() {
  try {
    console.log('Testing login API...');

    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Login successful!');
    console.log('Response:', data);

    // Test user profile
    const token = data.token;
    const profileResponse = await fetch(`http://localhost:3000/api/users/${data.user.id}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!profileResponse.ok) {
      throw new Error(`HTTP error! status: ${profileResponse.status}`);
    }

    const profileData = await profileResponse.json();
    console.log('✅ Profile fetch successful!');
    console.log('Profile:', profileData);

  } catch (error) {
    console.error('❌ API Test failed:', error.message);
  }
}

testLogin();
