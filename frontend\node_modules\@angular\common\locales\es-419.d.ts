/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: (string | undefined)[];
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    CAD: (string | undefined)[];
    CNY: (string | undefined)[];
    ESP: string[];
    EUR: (string | undefined)[];
    FKP: (string | undefined)[];
    GBP: (string | undefined)[];
    HKD: (string | undefined)[];
    ILS: (string | undefined)[];
    INR: (string | undefined)[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NZD: (string | undefined)[];
    PHP: (string | undefined)[];
    RON: (string | undefined)[];
    SSP: (string | undefined)[];
    SYP: (string | undefined)[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    VEF: (string | undefined)[];
    VND: (string | undefined)[];
    XAF: never[];
    XCD: (string | undefined)[];
    XOF: never[];
} | undefined)[];
export default _default;
