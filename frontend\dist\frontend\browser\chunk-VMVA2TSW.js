var Dg=Object.defineProperty,Eg=Object.defineProperties;var wg=Object.getOwnPropertyDescriptors;var zu=Object.getOwnPropertySymbols;var Ig=Object.prototype.hasOwnProperty,Cg=Object.prototype.propertyIsEnumerable;var qu=(e,t,n)=>t in e?Dg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,y=(e,t)=>{for(var n in t||={})Ig.call(t,n)&&qu(e,n,t[n]);if(zu)for(var n of zu(t))Cg.call(t,n)&&qu(e,n,t[n]);return e},B=(e,t)=>Eg(e,wg(t));var Qn=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(u){o(u)}},s=c=>{try{a(n.throw(c))}catch(u){o(u)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function Ms(e,t){return Object.is(e,t)}var Z=null,no=!1,Ts=1,Me=Symbol("SIGNAL");function N(e){let t=Z;return Z=e,t}function Ss(){return Z}var Jn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Xn(e){if(no)throw new Error("");if(Z===null)return;Z.consumerOnSignalRead(e);let t=Z.nextProducerIndex++;if(so(Z),t<Z.producerNode.length&&Z.producerNode[t]!==e&&Kn(Z)){let n=Z.producerNode[t];io(n,Z.producerIndexOfThis[t])}Z.producerNode[t]!==e&&(Z.producerNode[t]=e,Z.producerIndexOfThis[t]=Kn(Z)?Wu(e,Z,t):0),Z.producerLastReadVersion[t]=e.version}function Gu(){Ts++}function _s(e){if(!(Kn(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ts)){if(!e.producerMustRecompute(e)&&!As(e)){bs(e);return}e.producerRecomputeValue(e),bs(e)}}function Ns(e){if(e.liveConsumerNode===void 0)return;let t=no;no=!0;try{for(let n of e.liveConsumerNode)n.dirty||bg(n)}finally{no=t}}function Rs(){return Z?.consumerAllowSignalWrites!==!1}function bg(e){e.dirty=!0,Ns(e),e.consumerMarkedDirty?.(e)}function bs(e){e.dirty=!1,e.lastCleanEpoch=Ts}function oo(e){return e&&(e.nextProducerIndex=0),N(e)}function xs(e,t){if(N(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Kn(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)io(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function As(e){so(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(_s(n),r!==n.version))return!0}return!1}function Os(e){if(so(e),Kn(e))for(let t=0;t<e.producerNode.length;t++)io(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Wu(e,t,n){if(Zu(e),e.liveConsumerNode.length===0&&Yu(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Wu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function io(e,t){if(Zu(e),e.liveConsumerNode.length===1&&Yu(e))for(let r=0;r<e.producerNode.length;r++)io(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];so(o),o.producerIndexOfThis[r]=t}}function Kn(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function so(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Zu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Yu(e){return e.producerNode!==void 0}function ks(e,t){let n=Object.create(Mg);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(_s(n),Xn(n),n.value===ro)throw n.error;return n.value};return r[Me]=n,r}var Is=Symbol("UNSET"),Cs=Symbol("COMPUTING"),ro=Symbol("ERRORED"),Mg=B(y({},Jn),{value:Is,dirty:!0,error:null,equal:Ms,kind:"computed",producerMustRecompute(e){return e.value===Is||e.value===Cs},producerRecomputeValue(e){if(e.value===Cs)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Cs;let n=oo(e),r,o=!1;try{r=e.computation(),N(null),o=t!==Is&&t!==ro&&r!==ro&&e.equal(t,r)}catch(i){r=ro,e.error=i}finally{xs(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Tg(){throw new Error}var Qu=Tg;function Ku(e){Qu(e)}function Ps(e){Qu=e}var Sg=null;function Fs(e,t){let n=Object.create(ao);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(Xn(n),n.value);return r[Me]=n,r}function er(e,t){Rs()||Ku(e),e.equal(e.value,t)||(e.value=t,_g(e))}function Ls(e,t){Rs()||Ku(e),er(e,t(e.value))}var ao=B(y({},Jn),{equal:Ms,value:void 0,kind:"signal"});function _g(e){e.version++,Gu(),Ns(e),Sg?.()}function js(e){let t=N(null);try{return e()}finally{N(t)}}var Vs;function tr(){return Vs}function Qe(e){let t=Vs;return Vs=e,t}var co=Symbol("NotFound");function b(e){return typeof e=="function"}function Xt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var uo=Xt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function nr(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var q=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(b(r))try{r()}catch(i){t=i instanceof uo?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ju(i)}catch(s){t=t??[],s instanceof uo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new uo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Ju(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&nr(n,t)}remove(t){let{_finalizers:n}=this;n&&nr(n,t),t instanceof e&&t._removeParent(this)}};q.EMPTY=(()=>{let e=new q;return e.closed=!0,e})();var Bs=q.EMPTY;function lo(e){return e instanceof q||e&&"closed"in e&&b(e.remove)&&b(e.add)&&b(e.unsubscribe)}function Ju(e){b(e)?e():e.unsubscribe()}var Te={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var en={setTimeout(e,t,...n){let{delegate:r}=en;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=en;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function fo(e){en.setTimeout(()=>{let{onUnhandledError:t}=Te;if(t)t(e);else throw e})}function rr(){}var Xu=Us("C",void 0,void 0);function el(e){return Us("E",void 0,e)}function tl(e){return Us("N",e,void 0)}function Us(e,t,n){return{kind:e,value:t,error:n}}var St=null;function tn(e){if(Te.useDeprecatedSynchronousErrorHandling){let t=!St;if(t&&(St={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=St;if(St=null,n)throw r}}else e()}function nl(e){Te.useDeprecatedSynchronousErrorHandling&&St&&(St.errorThrown=!0,St.error=e)}var _t=class extends q{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,lo(t)&&t.add(this)):this.destination=kg}static create(t,n,r){return new nn(t,n,r)}next(t){this.isStopped?Hs(tl(t),this):this._next(t)}error(t){this.isStopped?Hs(el(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Hs(Xu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Ag=Function.prototype.bind;function $s(e,t){return Ag.call(e,t)}var zs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){ho(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){ho(r)}else ho(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){ho(n)}}},nn=class extends _t{constructor(t,n,r){super();let o;if(b(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Te.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&$s(t.next,i),error:t.error&&$s(t.error,i),complete:t.complete&&$s(t.complete,i)}):o=t}this.destination=new zs(o)}};function ho(e){Te.useDeprecatedSynchronousErrorHandling?nl(e):fo(e)}function Og(e){throw e}function Hs(e,t){let{onStoppedNotification:n}=Te;n&&en.setTimeout(()=>n(e,t))}var kg={closed:!0,next:rr,error:Og,complete:rr};var rn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ge(e){return e}function qs(...e){return Gs(e)}function Gs(e){return e.length===0?ge:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var k=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Fg(n)?n:new nn(n,r,o);return tn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=rl(r),new r((o,i)=>{let s=new nn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[rn](){return this}pipe(...n){return Gs(n)(this)}toPromise(n){return n=rl(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function rl(e){var t;return(t=e??Te.Promise)!==null&&t!==void 0?t:Promise}function Pg(e){return e&&b(e.next)&&b(e.error)&&b(e.complete)}function Fg(e){return e&&e instanceof _t||Pg(e)&&lo(e)}function Ws(e){return b(e?.lift)}function P(e){return t=>{if(Ws(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function O(e,t,n,r,o){return new Zs(e,t,n,r,o)}var Zs=class extends _t{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function on(){return P((e,t)=>{let n=null;e._refCount++;let r=O(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var sn=class extends k{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ws(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new q;let n=this.getSubject();t.add(this.source.subscribe(O(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=q.EMPTY)}return t}refCount(){return on()(this)}};var ol=Xt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Q=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new po(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new ol}next(n){tn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){tn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){tn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Bs:(this.currentObservers=null,i.push(n),new q(()=>{this.currentObservers=null,nr(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new k;return n.source=this,n}}return e.create=(t,n)=>new po(t,n),e})(),po=class extends Q{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Bs}};var J=class extends Q{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ae=new k(e=>e.complete());function il(e){return e&&b(e.schedule)}function sl(e){return e[e.length-1]}function go(e){return b(sl(e))?e.pop():void 0}function dt(e){return il(sl(e))?e.pop():void 0}function cl(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(f){s(f)}}function c(l){try{u(r.throw(l))}catch(f){s(f)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function al(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Nt(e){return this instanceof Nt?(this.v=e,this):new Nt(e)}function ul(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(g){return Promise.resolve(g).then(d,f)}}function a(d,g){r[d]&&(o[d]=function(m){return new Promise(function(w,S){i.push([d,m,w,S])>1||c(d,m)})},g&&(o[d]=g(o[d])))}function c(d,g){try{u(r[d](g))}catch(m){h(i[0][3],m)}}function u(d){d.value instanceof Nt?Promise.resolve(d.value.v).then(l,f):h(i[0][2],d)}function l(d){c("next",d)}function f(d){c("throw",d)}function h(d,g){d(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ll(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof al=="function"?al(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var mo=e=>e&&typeof e.length=="number"&&typeof e!="function";function yo(e){return b(e?.then)}function vo(e){return b(e[rn])}function Do(e){return Symbol.asyncIterator&&b(e?.[Symbol.asyncIterator])}function Eo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Lg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var wo=Lg();function Io(e){return b(e?.[wo])}function Co(e){return ul(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Nt(n.read());if(o)return yield Nt(void 0);yield yield Nt(r)}}finally{n.releaseLock()}})}function bo(e){return b(e?.getReader)}function G(e){if(e instanceof k)return e;if(e!=null){if(vo(e))return jg(e);if(mo(e))return Vg(e);if(yo(e))return Bg(e);if(Do(e))return dl(e);if(Io(e))return Ug(e);if(bo(e))return $g(e)}throw Eo(e)}function jg(e){return new k(t=>{let n=e[rn]();if(b(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Vg(e){return new k(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Bg(e){return new k(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,fo)})}function Ug(e){return new k(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function dl(e){return new k(t=>{Hg(e,t).catch(n=>t.error(n))})}function $g(e){return dl(Co(e))}function Hg(e,t){var n,r,o,i;return cl(this,void 0,void 0,function*(){try{for(n=ll(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ce(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Mo(e,t=0){return P((n,r)=>{n.subscribe(O(r,o=>ce(r,e,()=>r.next(o),t),()=>ce(r,e,()=>r.complete(),t),o=>ce(r,e,()=>r.error(o),t)))})}function To(e,t=0){return P((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function fl(e,t){return G(e).pipe(To(t),Mo(t))}function hl(e,t){return G(e).pipe(To(t),Mo(t))}function pl(e,t){return new k(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function gl(e,t){return new k(n=>{let r;return ce(n,t,()=>{r=e[wo](),ce(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>b(r?.return)&&r.return()})}function So(e,t){if(!e)throw new Error("Iterable cannot be null");return new k(n=>{ce(n,t,()=>{let r=e[Symbol.asyncIterator]();ce(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ml(e,t){return So(Co(e),t)}function yl(e,t){if(e!=null){if(vo(e))return fl(e,t);if(mo(e))return pl(e,t);if(yo(e))return hl(e,t);if(Do(e))return So(e,t);if(Io(e))return gl(e,t);if(bo(e))return ml(e,t)}throw Eo(e)}function U(e,t){return t?yl(e,t):G(e)}function C(...e){let t=dt(e);return U(e,t)}function an(e,t){let n=b(e)?e:()=>e,r=o=>o.error(n());return new k(t?o=>t.schedule(r,0,o):r)}function Ys(e){return!!e&&(e instanceof k||b(e.lift)&&b(e.subscribe))}var Ke=Xt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function R(e,t){return P((n,r)=>{let o=0;n.subscribe(O(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:zg}=Array;function qg(e,t){return zg(t)?e(...t):e(t)}function _o(e){return R(t=>qg(e,t))}var{isArray:Gg}=Array,{getPrototypeOf:Wg,prototype:Zg,keys:Yg}=Object;function No(e){if(e.length===1){let t=e[0];if(Gg(t))return{args:t,keys:null};if(Qg(t)){let n=Yg(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Qg(e){return e&&typeof e=="object"&&Wg(e)===Zg}function Ro(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function or(...e){let t=dt(e),n=go(e),{args:r,keys:o}=No(e);if(r.length===0)return U([],t);let i=new k(Kg(r,t,o?s=>Ro(o,s):ge));return n?i.pipe(_o(n)):i}function Kg(e,t,n=ge){return r=>{vl(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)vl(t,()=>{let u=U(e[c],t),l=!1;u.subscribe(O(r,f=>{i[c]=f,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function vl(e,t,n){e?ce(n,e,t):t()}function Dl(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,f=!1,h=()=>{f&&!c.length&&!u&&t.complete()},d=m=>u<r?g(m):c.push(m),g=m=>{i&&t.next(m),u++;let w=!1;G(n(m,l++)).subscribe(O(t,S=>{o?.(S),i?d(S):t.next(S)},()=>{w=!0},void 0,()=>{if(w)try{for(u--;c.length&&u<r;){let S=c.shift();s?ce(t,s,()=>g(S)):g(S)}h()}catch(S){t.error(S)}}))};return e.subscribe(O(t,d,()=>{f=!0,h()})),()=>{a?.()}}function z(e,t,n=1/0){return b(t)?z((r,o)=>R((i,s)=>t(r,i,o,s))(G(e(r,o))),n):(typeof t=="number"&&(n=t),P((r,o)=>Dl(r,o,e,n)))}function cn(e=1/0){return z(ge,e)}function El(){return cn(1)}function un(...e){return El()(U(e,dt(e)))}function xo(e){return new k(t=>{G(e()).subscribe(t)})}function Jg(...e){let t=go(e),{args:n,keys:r}=No(e),o=new k(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let f=!1;G(n[l]).subscribe(O(i,h=>{f||(f=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!f)&&(u||i.next(r?Ro(r,a):a),i.complete())}))}});return t?o.pipe(_o(t)):o}function ne(e,t){return P((n,r)=>{let o=0;n.subscribe(O(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Je(e){return P((t,n)=>{let r=null,o=!1,i;r=t.subscribe(O(n,void 0,void 0,s=>{i=G(e(s,Je(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function wl(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(O(s,l=>{let f=u++;c=a?e(c,l,f):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Pe(e,t){return b(t)?z(e,t,1):z(e,1)}function ft(e){return P((t,n)=>{let r=!1;t.subscribe(O(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Xe(e){return e<=0?()=>ae:P((t,n)=>{let r=0;t.subscribe(O(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Ao(e=Xg){return P((t,n)=>{let r=!1;t.subscribe(O(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Xg(){return new Ke}function ht(e){return P((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function et(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ne((o,i)=>e(o,i,r)):ge,Xe(1),n?ft(t):Ao(()=>new Ke))}function ln(e){return e<=0?()=>ae:P((t,n)=>{let r=[];t.subscribe(O(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Qs(e,t){let n=arguments.length>=2;return r=>r.pipe(e?ne((o,i)=>e(o,i,r)):ge,ln(1),n?ft(t):Ao(()=>new Ke))}function Ks(e,t){return P(wl(e,t,arguments.length>=2,!0))}function Js(...e){let t=dt(e);return P((n,r)=>{(t?un(e,n,t):un(e,n)).subscribe(r)})}function ue(e,t){return P((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(O(r,c=>{o?.unsubscribe();let u=0,l=i++;G(e(c,l)).subscribe(o=O(r,f=>r.next(t?t(c,f,l,u++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Xs(e){return P((t,n)=>{G(e).subscribe(O(n,()=>n.complete(),rr)),!n.closed&&t.subscribe(n)})}function K(e,t,n){let r=b(e)||t||n?{next:e,error:t,complete:n}:e;return r?P((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(O(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ge}var dd="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",v=class extends Error{code;constructor(t,n){super(fd(t,n)),this.code=t}};function em(e){return`NG0${Math.abs(e)}`}function fd(e,t){return`${em(e)}${t?": "+t:""}`}var hd=Symbol("InputSignalNode#UNSET"),tm=B(y({},ao),{transformFn:void 0,applyValueToInputSignal(e,t){er(e,t)}});function pd(e,t){let n=Object.create(tm);n.value=e,n.transformFn=t?.transform;function r(){if(Xn(n),n.value===hd){let o=null;throw new v(-950,o)}return n.value}return r[Me]=n,r}function hi(e){return{toString:e}.toString()}var pa=globalThis;function V(e){for(let t in e)if(e[t]===V)return t;throw Error("Could not find renamed property on target object.")}function nm(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function fe(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(fe).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Il(e,t){return e?t?`${e} ${t}`:e:t||""}var rm=V({__forward_ref__:V});function gd(e){return e.__forward_ref__=gd,e.toString=function(){return fe(this())},e}function re(e){return md(e)?e():e}function md(e){return typeof e=="function"&&e.hasOwnProperty(rm)&&e.__forward_ref__===gd}function E(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function mt(e){return{providers:e.providers||[],imports:e.imports||[]}}function pi(e){return Cl(e,vd)||Cl(e,Dd)}function yd(e){return pi(e)!==null}function Cl(e,t){return e.hasOwnProperty(t)?e[t]:null}function om(e){let t=e&&(e[vd]||e[Dd]);return t||null}function bl(e){return e&&(e.hasOwnProperty(Ml)||e.hasOwnProperty(im))?e[Ml]:null}var vd=V({\u0275prov:V}),Ml=V({\u0275inj:V}),Dd=V({ngInjectableDef:V}),im=V({ngInjectorDef:V}),D=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=E({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Ed(e){return e&&!!e.\u0275providers}var sm=V({\u0275cmp:V}),am=V({\u0275dir:V}),cm=V({\u0275pipe:V}),um=V({\u0275mod:V}),Bo=V({\u0275fac:V}),cr=V({__NG_ELEMENT_ID__:V}),Tl=V({__NG_ENV_ID__:V});function gi(e){return typeof e=="string"?e:e==null?"":String(e)}function lm(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():gi(e)}function wd(e,t){throw new v(-200,e)}function nc(e,t){throw new v(-201,!1)}var _=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(_||{}),ga;function Id(){return ga}function le(e){let t=ga;return ga=e,t}function Cd(e,t,n){let r=pi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&_.Optional)return null;if(t!==void 0)return t;nc(e,"Injector")}var dm={},Rt=dm,fm="__NG_DI_FLAG__",Uo=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?co:Rt,r)}},$o="ngTempTokenPath",hm="ngTokenPath",pm=/\n/gm,gm="\u0275",Sl="__source";function mm(e,t=_.Default){if(tr()===void 0)throw new v(-203,!1);if(tr()===null)return Cd(e,void 0,t);{let n=tr(),r;return n instanceof Uo?r=n.injector:r=n,r.get(e,t&_.Optional?null:void 0,t)}}function I(e,t=_.Default){return(Id()||mm)(re(e),t)}function p(e,t=_.Default){return I(e,mi(t))}function mi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ma(e){let t=[];for(let n=0;n<e.length;n++){let r=re(e[n]);if(Array.isArray(r)){if(r.length===0)throw new v(900,!1);let o,i=_.Default;for(let s=0;s<r.length;s++){let a=r[s],c=ym(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function ym(e){return e[fm]}function vm(e,t,n,r){let o=e[$o];throw t[Sl]&&o.unshift(t[Sl]),e.message=Dm(`
`+e.message,o,n,r),e[hm]=o,e[$o]=null,e}function Dm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==gm?e.slice(2):e;let o=fe(t);if(Array.isArray(t))o=t.map(fe).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):fe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(pm,`
  `)}`}function At(e,t){let n=e.hasOwnProperty(Bo);return n?e[Bo]:null}function rc(e,t){e.forEach(n=>Array.isArray(n)?rc(n,t):t(n))}function bd(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ho(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Em(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function wm(e,t,n){let r=yr(e,t);return r>=0?e[r|1]=n:(r=~r,Em(e,r,t,n)),r}function ea(e,t){let n=yr(e,t);if(n>=0)return e[n|1]}function yr(e,t){return Im(e,t,1)}function Im(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ot={},we=[],ur=new D(""),Md=new D("",-1),Td=new D(""),zo=class{get(t,n=Rt){if(n===Rt){let r=new Error(`NullInjectorError: No provider for ${fe(t)}!`);throw r.name="NullInjectorError",r}return n}};function Sd(e,t){let n=e[um]||null;if(!n&&t===!0)throw new Error(`Type ${fe(e)} does not have '\u0275mod' property.`);return n}function kt(e){return e[sm]||null}function Cm(e){return e[am]||null}function bm(e){return e[cm]||null}function Sn(e){return{\u0275providers:e}}function Mm(...e){return{\u0275providers:_d(!0,e),\u0275fromNgModule:!0}}function _d(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return rc(t,s=>{let a=s;ya(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Nd(o,i),n}function Nd(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];oc(o,i=>{t(i,r)})}}function ya(e,t,n,r){if(e=re(e),!e)return!1;let o=null,i=bl(e),s=!i&&kt(e);if(!i&&!s){let c=e.ngModule;if(i=bl(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)ya(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{rc(i.imports,l=>{ya(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Nd(u,t)}if(!a){let u=At(o)||(()=>new o);t({provide:o,useFactory:u,deps:we},o),t({provide:Td,useValue:o,multi:!0},o),t({provide:ur,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;oc(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function oc(e,t){for(let n of e)Ed(n)&&(n=n.\u0275providers),Array.isArray(n)?oc(n,t):t(n)}var Tm=V({provide:String,useValue:V});function Rd(e){return e!==null&&typeof e=="object"&&Tm in e}function Sm(e){return!!(e&&e.useExisting)}function _m(e){return!!(e&&e.useFactory)}function yn(e){return typeof e=="function"}function Nm(e){return!!e.useClass}var yi=new D(""),Po={},_l={},ta;function ic(){return ta===void 0&&(ta=new zo),ta}var te=class{},lr=class extends te{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Da(t,s=>this.processProvider(s)),this.records.set(Md,dn(void 0,this)),o.has("environment")&&this.records.set(te,dn(void 0,this));let i=this.records.get(yi);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Td,we,_.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?co:Rt,r)}destroy(){sr(this),this._destroyed=!0;let t=N(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),N(t)}}onDestroy(t){return sr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){sr(this);let n=Qe(this),r=le(void 0),o;try{return t()}finally{Qe(n),le(r)}}get(t,n=Rt,r=_.Default){if(sr(this),t.hasOwnProperty(Tl))return t[Tl](this);r=mi(r);let o,i=Qe(this),s=le(void 0);try{if(!(r&_.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=km(t)&&pi(t);u&&this.injectableDefInScope(u)?c=dn(va(t),Po):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,r)}let a=r&_.Self?ic():this.parent;return n=r&_.Optional&&n===Rt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[$o]=a[$o]||[]).unshift(fe(t)),i)throw a;return vm(a,t,"R3InjectorError",this.source)}else throw a}finally{le(s),Qe(i)}}resolveInjectorInitializers(){let t=N(null),n=Qe(this),r=le(void 0),o;try{let i=this.get(ur,we,_.Self);for(let s of i)s()}finally{Qe(n),le(r),N(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(fe(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=re(t);let n=yn(t)?t:re(t&&t.provide),r=xm(t);if(!yn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=dn(void 0,Po,!0),o.factory=()=>ma(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=N(null);try{return n.value===_l?wd(fe(t)):n.value===Po&&(n.value=_l,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&Om(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{N(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=re(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function va(e){let t=pi(e),n=t!==null?t.factory:At(e);if(n!==null)return n;if(e instanceof D)throw new v(204,!1);if(e instanceof Function)return Rm(e);throw new v(204,!1)}function Rm(e){if(e.length>0)throw new v(204,!1);let n=om(e);return n!==null?()=>n.factory(e):()=>new e}function xm(e){if(Rd(e))return dn(void 0,e.useValue);{let t=xd(e);return dn(t,Po)}}function xd(e,t,n){let r;if(yn(e)){let o=re(e);return At(o)||va(o)}else if(Rd(e))r=()=>re(e.useValue);else if(_m(e))r=()=>e.useFactory(...ma(e.deps||[]));else if(Sm(e))r=(o,i)=>I(re(e.useExisting),i!==void 0&&i&_.Optional?_.Optional:void 0);else{let o=re(e&&(e.useClass||e.provide));if(Am(e))r=()=>new o(...ma(e.deps));else return At(o)||va(o)}return r}function sr(e){if(e.destroyed)throw new v(205,!1)}function dn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Am(e){return!!e.deps}function Om(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function km(e){return typeof e=="function"||typeof e=="object"&&e instanceof D}function Da(e,t){for(let n of e)Array.isArray(n)?Da(n,t):n&&Ed(n)?Da(n.\u0275providers,t):t(n)}function he(e,t){let n;e instanceof lr?(sr(e),n=e):n=new Uo(e);let r,o=Qe(n),i=le(void 0);try{return t()}finally{Qe(o),le(i)}}function Ad(){return Id()!==void 0||tr()!=null}function Pm(e){if(!Ad())throw new v(-203,!1)}function Fm(e){return typeof e=="function"}var ot=0,x=1,M=2,ie=3,_e=4,xe=5,dr=6,qo=7,X=8,vn=9,tt=10,ee=11,fr=12,Nl=13,_n=14,Ne=15,Dn=16,fn=17,En=18,vi=19,Od=20,pt=21,na=22,Go=23,Ie=24,gn=25,Ce=26,kd=1;var Pt=7,Wo=8,Zo=9,oe=10;function gt(e){return Array.isArray(e)&&typeof e[kd]=="object"}function it(e){return Array.isArray(e)&&e[kd]===!0}function Pd(e){return(e.flags&4)!==0}function Nn(e){return e.componentOffset>-1}function sc(e){return(e.flags&1)===1}function Le(e){return!!e.template}function Yo(e){return(e[M]&512)!==0}function Rn(e){return(e[M]&256)===256}var Ea=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Fd(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var vr=(()=>{let e=()=>Ld;return e.ngInherit=!0,e})();function Ld(e){return e.type.prototype.ngOnChanges&&(e.setInput=jm),Lm}function Lm(){let e=Vd(this),t=e?.current;if(t){let n=e.previous;if(n===Ot)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function jm(e,t,n,r,o){let i=this.declaredInputs[r],s=Vd(e)||Vm(e,{previous:Ot,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Ea(u&&u.currentValue,n,c===Ot),Fd(e,t,o,n)}var jd="__ngSimpleChanges__";function Vd(e){return e[jd]||null}function Vm(e,t){return e[jd]=t}var Rl=null;var j=function(e,t=null,n){Rl?.(e,t,n)},Bd="svg",Bm="math";function je(e){for(;Array.isArray(e);)e=e[ot];return e}function Ud(e,t){return je(t[e])}function $e(e,t){return je(t[e.index])}function ac(e,t){return e.data[t]}function Um(e,t){return e[t]}function $m(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function Ve(e,t){let n=t[e];return gt(n)?n:n[ot]}function cc(e){return(e[M]&128)===128}function Hm(e){return it(e[ie])}function wn(e,t){return t==null?null:e[t]}function $d(e){e[fn]=0}function Hd(e){e[M]&1024||(e[M]|=1024,cc(e)&&Dr(e))}function zm(e,t){for(;e>0;)t=t[_n],e--;return t}function Di(e){return!!(e[M]&9216||e[Ie]?.dirty)}function wa(e){e[tt].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),Di(e)&&Dr(e)}function Dr(e){e[tt].changeDetectionScheduler?.notify(0);let t=Ft(e);for(;t!==null&&!(t[M]&8192||(t[M]|=8192,!cc(t)));)t=Ft(t)}function zd(e,t){if(Rn(e))throw new v(911,!1);e[pt]===null&&(e[pt]=[]),e[pt].push(t)}function qm(e,t){if(e[pt]===null)return;let n=e[pt].indexOf(t);n!==-1&&e[pt].splice(n,1)}function Ft(e){let t=e[ie];return it(t)?t[ie]:t}function qd(e){return e[qo]??=[]}function Gd(e){return e.cleanup??=[]}var A={lFrame:ef(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Ia=!1;function Gm(){return A.lFrame.elementDepthCount}function Wm(){A.lFrame.elementDepthCount++}function Zm(){A.lFrame.elementDepthCount--}function Wd(){return A.bindingsEnabled}function Ym(){return A.skipHydrationRootTNode!==null}function Qm(e){return A.skipHydrationRootTNode===e}function Km(){A.skipHydrationRootTNode=null}function L(){return A.lFrame.lView}function me(){return A.lFrame.tView}function ye(){let e=Zd();for(;e!==null&&e.type===64;)e=e.parent;return e}function Zd(){return A.lFrame.currentTNode}function Jm(){let e=A.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Er(e,t){let n=A.lFrame;n.currentTNode=e,n.isParent=t}function Yd(){return A.lFrame.isParent}function Xm(){A.lFrame.isParent=!1}function Qd(){return Ia}function xl(e){let t=Ia;return Ia=e,t}function uc(){let e=A.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ey(e){return A.lFrame.bindingIndex=e}function Ei(){return A.lFrame.bindingIndex++}function ty(e){let t=A.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function ny(){return A.lFrame.inI18n}function ry(e,t){let n=A.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ca(t)}function oy(){return A.lFrame.currentDirectiveIndex}function Ca(e){A.lFrame.currentDirectiveIndex=e}function iy(e){let t=A.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Kd(e){A.lFrame.currentQueryIndex=e}function sy(e){let t=e[x];return t.type===2?t.declTNode:t.type===1?e[xe]:null}function Jd(e,t,n){if(n&_.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&_.Host);)if(o=sy(i),o===null||(i=i[_n],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=A.lFrame=Xd();return r.currentTNode=t,r.lView=e,!0}function lc(e){let t=Xd(),n=e[x];A.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Xd(){let e=A.lFrame,t=e===null?null:e.child;return t===null?ef(e):t}function ef(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function tf(){let e=A.lFrame;return A.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var nf=tf;function dc(){let e=tf();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function ay(e){return(A.lFrame.contextLView=zm(e,A.lFrame.contextLView))[X]}function Ut(){return A.lFrame.selectedIndex}function Lt(e){A.lFrame.selectedIndex=e}function rf(){let e=A.lFrame;return ac(e.tView,e.selectedIndex)}function tN(){A.lFrame.currentNamespace=Bd}function nN(){cy()}function cy(){A.lFrame.currentNamespace=null}function uy(){return A.lFrame.currentNamespace}var of=!0;function fc(){return of}function hc(e){of=e}function ly(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ld(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function sf(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Fo(e,t,n){af(e,t,3,n)}function Lo(e,t,n,r){(e[M]&3)===n&&af(e,t,n,r)}function ra(e,t){let n=e[M];(n&3)===t&&(n&=16383,n+=1,e[M]=n)}function af(e,t,n,r){let o=r!==void 0?e[fn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[fn]+=65536),(a<i||i==-1)&&(dy(e,n,t,c),e[fn]=(e[fn]&**********)+c+2),c++}function Al(e,t){j(4,e,t);let n=N(null);try{t.call(e)}finally{N(n),j(5,e,t)}}function dy(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[M]>>14<e[fn]>>16&&(e[M]&3)===t&&(e[M]+=16384,Al(a,i)):Al(a,i)}var mn=-1,jt=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function fy(e){return(e.flags&8)!==0}function hy(e){return(e.flags&16)!==0}function py(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];gy(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function cf(e){return e===3||e===4||e===6}function gy(e){return e.charCodeAt(0)===64}function hr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ol(e,n,o,null,t[++r]):Ol(e,n,o,null,null))}}return e}function Ol(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function uf(e){return e!==mn}function Qo(e){return e&32767}function my(e){return e>>16}function Ko(e,t){let n=my(e),r=t;for(;n>0;)r=r[_n],n--;return r}var ba=!0;function Jo(e){let t=ba;return ba=e,t}var yy=256,lf=yy-1,df=5,vy=0,Fe={};function Dy(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(cr)&&(r=n[cr]),r==null&&(r=n[cr]=vy++);let o=r&lf,i=1<<o;t.data[e+(o>>df)]|=i}function Xo(e,t){let n=ff(e,t);if(n!==-1)return n;let r=t[x];r.firstCreatePass&&(e.injectorIndex=t.length,oa(r.data,e),oa(t,null),oa(r.blueprint,null));let o=pc(e,t),i=e.injectorIndex;if(uf(o)){let s=Qo(o),a=Ko(o,t),c=a[x].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function oa(e,t){e.push(0,0,0,0,0,0,0,0,t)}function ff(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function pc(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=yf(o),r===null)return mn;if(n++,o=o[_n],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return mn}function Ma(e,t,n){Dy(e,t,n)}function Ey(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(cf(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function hf(e,t,n){if(n&_.Optional||e!==void 0)return e;nc(t,"NodeInjector")}function pf(e,t,n,r){if(n&_.Optional&&r===void 0&&(r=null),(n&(_.Self|_.Host))===0){let o=e[vn],i=le(void 0);try{return o?o.get(t,r,n&_.Optional):Cd(t,r,n&_.Optional)}finally{le(i)}}return hf(r,t,n)}function gf(e,t,n,r=_.Default,o){if(e!==null){if(t[M]&2048&&!(r&_.Self)){let s=My(e,t,n,r,Fe);if(s!==Fe)return s}let i=mf(e,t,n,r,Fe);if(i!==Fe)return i}return pf(t,n,r,o)}function mf(e,t,n,r,o){let i=Cy(n);if(typeof i=="function"){if(!Jd(t,e,r))return r&_.Host?hf(o,n,r):pf(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&_.Optional))nc(n);else return s}finally{nf()}}else if(typeof i=="number"){let s=null,a=ff(e,t),c=mn,u=r&_.Host?t[Ne][xe]:null;for((a===-1||r&_.SkipSelf)&&(c=a===-1?pc(e,t):t[a+8],c===mn||!Pl(r,!1)?a=-1:(s=t[x],a=Qo(c),t=Ko(c,t)));a!==-1;){let l=t[x];if(kl(i,a,l.data)){let f=wy(a,t,n,s,r,u);if(f!==Fe)return f}c=t[a+8],c!==mn&&Pl(r,t[x].data[a+8]===u)&&kl(i,a,t)?(s=l,a=Qo(c),t=Ko(c,t)):a=-1}}return o}function wy(e,t,n,r,o,i){let s=t[x],a=s.data[e+8],c=r==null?Nn(a)&&ba:r!=s&&(a.type&3)!==0,u=o&_.Host&&i===a,l=Iy(a,s,n,c,u);return l!==null?ei(t,s,l,a,o):Fe}function Iy(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,f=r?a:a+l,h=o?a+l:u;for(let d=f;d<h;d++){let g=s[d];if(d<c&&n===g||d>=c&&g.type===n)return d}if(o){let d=s[c];if(d&&Le(d)&&d.type===n)return c}return null}function ei(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof jt){let a=i;a.resolving&&wd(lm(s[n]));let c=Jo(a.canSeeViewProviders);a.resolving=!0;let u,l=a.injectImpl?le(a.injectImpl):null,f=Jd(e,r,_.Default);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&ly(n,s[n],t)}finally{l!==null&&le(l),Jo(c),a.resolving=!1,nf()}}return i}function Cy(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(cr)?e[cr]:void 0;return typeof t=="number"?t>=0?t&lf:by:t}function kl(e,t,n){let r=1<<e;return!!(n[t+(e>>df)]&r)}function Pl(e,t){return!(e&_.Self)&&!(e&_.Host&&t)}var xt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return gf(this._tNode,this._lView,t,mi(r),n)}};function by(){return new xt(ye(),L())}function gc(e){return hi(()=>{let t=e.prototype.constructor,n=t[Bo]||Ta(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Bo]||Ta(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ta(e){return md(e)?()=>{let t=Ta(re(e));return t&&t()}:At(e)}function My(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[M]&2048&&!Yo(s);){let a=mf(i,s,n,r|_.Self,Fe);if(a!==Fe)return a;let c=i.parent;if(!c){let u=s[Od];if(u){let l=u.get(n,Fe,r);if(l!==Fe)return l}c=yf(s),s=s[_n]}i=c}return o}function yf(e){let t=e[x],n=t.type;return n===2?t.declTNode:n===1?e[xe]:null}function mc(e){return Ey(ye(),e)}function Fl(e,t=null,n=null,r){let o=vf(e,t,n,r);return o.resolveInjectorInitializers(),o}function vf(e,t=null,n=null,r,o=new Set){let i=[n||we,Mm(e)];return r=r||(typeof e=="object"?void 0:fe(e)),new lr(i,t||ic(),r||null,o)}var Re=class e{static THROW_IF_NOT_FOUND=Rt;static NULL=new zo;static create(t,n){if(Array.isArray(t))return Fl({name:""},n,t,"");{let r=t.name??"";return Fl({name:r},t.parent,t.providers,r)}}static \u0275prov=E({token:e,providedIn:"any",factory:()=>I(Md)});static __NG_ELEMENT_ID__=-1};var Ty=new D("");Ty.__NG_ELEMENT_ID__=e=>{let t=ye();if(t===null)throw new v(204,!1);if(t.type&2)return t.value;if(e&_.Optional)return null;throw new v(204,!1)};var Df=!1,$t=(()=>{class e{static __NG_ELEMENT_ID__=Sy;static __NG_ENV_ID__=n=>n}return e})(),Sa=class extends $t{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Rn(n)?(t(),()=>{}):(zd(n,t),()=>qm(n,t))}};function Sy(){return new Sa(L())}var In=class{},yc=new D("",{providedIn:"root",factory:()=>!1});var Ef=new D(""),wf=new D(""),st=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new J(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})();var _a=class extends Q{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Ad()&&(this.destroyRef=p($t,{optional:!0})??void 0,this.pendingTasks=p(st,{optional:!0})??void 0)}emit(t){let n=N(null);try{super.next(t)}finally{N(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof q&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},de=_a;function ti(...e){}function If(e){let t,n;function r(){e=ti;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ll(e){return queueMicrotask(()=>e()),()=>{e=ti}}var vc="isAngularZone",ni=vc+"_ID",_y=0,$=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new de(!1);onMicrotaskEmpty=new de(!1);onStable=new de(!1);onError=new de(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Df}=t;if(typeof Zone>"u")throw new v(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,xy(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(vc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new v(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new v(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Ny,ti,ti);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Ny={};function Dc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ry(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){If(()=>{e.callbackScheduled=!1,Na(e),e.isCheckStableRunning=!0,Dc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Na(e)}function xy(e){let t=()=>{Ry(e)},n=_y++;e._inner=e._inner.fork({name:"angular",properties:{[vc]:!0,[ni]:n,[ni+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Ay(c))return r.invokeTask(i,s,a,c);try{return jl(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Vl(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return jl(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Oy(c)&&t(),Vl(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Na(e),Dc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Na(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function jl(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Vl(e){e._nesting--,Dc(e)}var Ra=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new de;onMicrotaskEmpty=new de;onStable=new de;onError=new de;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Ay(e){return Cf(e,"__ignore_ng_zone__")}function Oy(e){return Cf(e,"__scheduler_tick__")}function Cf(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Be=class{_console=console;handleError(t){this._console.error("ERROR",t)}},ky=new D("",{providedIn:"root",factory:()=>{let e=p($),t=p(Be);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Bl(e,t){return pd(e,t)}function Py(e){return pd(hd,e)}var bf=(Bl.required=Py,Bl);function Fy(){return wi(ye(),L())}function wi(e,t){return new wr($e(e,t))}var wr=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Fy}return e})();function rN(e,t){let n=Fs(e,t?.equal),r=n[Me];return n.set=o=>er(r,o),n.update=o=>Ls(r,o),n.asReadonly=Ly.bind(n),n}function Ly(){let e=this[Me];if(e.readonlyFn===void 0){let t=()=>this();t[Me]=e,e.readonlyFn=t}return e.readonlyFn}function Mf(e){return(e.flags&128)===128}var Tf=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Tf||{}),Sf=new Map,jy=0;function Vy(){return jy++}function By(e){Sf.set(e[vi],e)}function xa(e){Sf.delete(e[vi])}var Ul="__ngContext__";function Ir(e,t){gt(t)?(e[Ul]=t[vi],By(t)):e[Ul]=t}function _f(e){return Rf(e[fr])}function Nf(e){return Rf(e[_e])}function Rf(e){for(;e!==null&&!it(e);)e=e[_e];return e}var Aa;function xf(e){Aa=e}function Uy(){if(Aa!==void 0)return Aa;if(typeof document<"u")return document;throw new v(210,!1)}var Ec=new D("",{providedIn:"root",factory:()=>$y}),$y="ng",wc=new D(""),xn=new D("",{providedIn:"platform",factory:()=>"unknown"});var Ic=new D("",{providedIn:"root",factory:()=>Uy().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Hy="h",zy="b";var Af=!1,qy=new D("",{providedIn:"root",factory:()=>Af});var Cc=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Cc||{}),An=new D(""),$l=new Set;function Ht(e){$l.has(e)||($l.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Of=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Gy}return e})();function Gy(){return new Of(L(),ye())}var hn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(hn||{}),kf=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),Wy=[hn.EarlyRead,hn.Write,hn.MixedReadWrite,hn.Read],Zy=(()=>{class e{ngZone=p($);scheduler=p(In);errorHandler=p(Be,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(An,{optional:!0})}execute(){let n=this.sequences.size>0;n&&j(16),this.executing=!0;for(let r of Wy)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&j(17)}register(n){let{view:r}=n;r!==void 0?((r[gn]??=[]).push(n),Dr(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Cc.AFTER_NEXT_RENDER,n):n()}static \u0275prov=E({token:e,providedIn:"root",factory:()=>new e})}return e})(),Oa=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[gn];t&&(this.view[gn]=t.filter(n=>n!==this))}};function bc(e,t){!t?.injector&&Pm(bc);let n=t?.injector??p(Re);return Ht("NgAfterNextRender"),Qy(e,n,t,!0)}function Yy(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Qy(e,t,n,r){let o=t.get(kf);o.impl??=t.get(Zy);let i=t.get(An,null,{optional:!0}),s=n?.phase??hn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get($t):null,c=t.get(Of,null,{optional:!0}),u=new Oa(o.impl,Yy(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(u),u}var Ky=(e,t,n,r)=>{};function Jy(e,t,n,r){Ky(e,t,n,r)}var Xy=()=>null;function Pf(e,t,n=!1){return Xy(e,t,n)}function Ff(e,t){let n=e.contentQueries;if(n!==null){let r=N(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Kd(i),a.contentQueries(2,t[s],s)}}}finally{N(r)}}}function ka(e,t,n){Kd(0);let r=N(null);try{t(e,n)}finally{N(r)}}function Lf(e,t,n){if(Pd(t)){let r=N(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{N(r)}}}var Ue=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ue||{});var Oo;function ev(){if(Oo===void 0&&(Oo=null,pa.trustedTypes))try{Oo=pa.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Oo}function Hl(e){return ev()?.createScriptURL(e)||e}var ri=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${dd})`}};function Ii(e){return e instanceof ri?e.changingThisBreaksApplicationSecurity:e}function jf(e,t){let n=tv(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${dd})`)}return n===t}function tv(e){return e instanceof ri&&e.getTypeName()||null}var nv=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function rv(e){return e=String(e),e.match(nv)?e:"unsafe:"+e}var Mc=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Mc||{});function ov(e){let t=Bf();return t?t.sanitize(Mc.URL,e)||"":jf(e,"URL")?Ii(e):rv(gi(e))}function iv(e){let t=Bf();if(t)return Hl(t.sanitize(Mc.RESOURCE_URL,e)||"");if(jf(e,"ResourceURL"))return Hl(Ii(e));throw new v(904,!1)}function sv(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?iv:ov}function Vf(e,t,n){return sv(t,n)(e)}function Bf(){let e=L();return e&&e[tt].sanitizer}function Uf(e){return e instanceof Function?e():e}function av(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var $f="ng-template";function cv(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&av(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Tc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Tc(e){return e.type===4&&e.value!==$f}function uv(e,t,n){let r=e.type===4&&!n?$f:e.value;return t===r}function lv(e,t,n){let r=4,o=e.attrs,i=o!==null?hv(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Se(r)&&!Se(c))return!1;if(s&&Se(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!uv(e,c,n)||c===""&&t.length===1){if(Se(r))return!1;s=!0}}else if(r&8){if(o===null||!cv(e,o,c,n)){if(Se(r))return!1;s=!0}}else{let u=t[++a],l=dv(c,o,Tc(e),n);if(l===-1){if(Se(r))return!1;s=!0;continue}if(u!==""){let f;if(l>i?f="":f=o[l+1].toLowerCase(),r&2&&u!==f){if(Se(r))return!1;s=!0}}}}return Se(r)||s}function Se(e){return(e&1)===0}function dv(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return pv(t,e)}function fv(e,t,n=!1){for(let r=0;r<t.length;r++)if(lv(e,t[r],n))return!0;return!1}function hv(e){for(let t=0;t<e.length;t++){let n=e[t];if(cf(n))return t}return e.length}function pv(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function zl(e,t){return e?":not("+t.trim()+")":t}function gv(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Se(s)&&(t+=zl(i,o),o=""),r=s,i=i||!Se(r);n++}return o!==""&&(t+=zl(i,o)),t}function mv(e){return e.map(gv).join(",")}function yv(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Se(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var yt={};function vv(e,t){return e.createText(t)}function Dv(e,t,n){e.setValue(t,n)}function Hf(e,t,n){return e.createElement(t,n)}function oi(e,t,n,r,o){e.insertBefore(t,n,r,o)}function zf(e,t,n){e.appendChild(t,n)}function ql(e,t,n,r,o){r!==null?oi(e,t,n,r,o):zf(e,t,n)}function Ev(e,t,n){e.removeChild(null,t,n)}function wv(e,t,n){e.setAttribute(t,"style",n)}function Iv(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function qf(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&py(e,t,r),o!==null&&Iv(e,t,o),i!==null&&wv(e,t,i)}function Sc(e,t,n,r,o,i,s,a,c,u,l){let f=Ce+r,h=f+o,d=Cv(f,h),g=typeof u=="function"?u():u;return d[x]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:l}}function Cv(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:yt);return n}function bv(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Sc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function _c(e,t,n,r,o,i,s,a,c,u,l){let f=t.blueprint.slice();return f[ot]=o,f[M]=r|4|128|8|64|1024,(u!==null||e&&e[M]&2048)&&(f[M]|=2048),$d(f),f[ie]=f[_n]=e,f[X]=n,f[tt]=s||e&&e[tt],f[ee]=a||e&&e[ee],f[vn]=c||e&&e[vn]||null,f[xe]=i,f[vi]=Vy(),f[dr]=l,f[Od]=u,f[Ne]=t.type==2?e[Ne]:f,f}function Mv(e,t,n){let r=$e(t,e),o=bv(n),i=e[tt].rendererFactory,s=Nc(e,_c(e,o,null,Gf(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Gf(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Wf(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Nc(e,t){return e[fr]?e[Nl][_e]=t:e[fr]=t,e[Nl]=t,t}function oN(e=1){Zf(me(),L(),Ut()+e,!1)}function Zf(e,t,n,r){if(!r)if((t[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Fo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Lo(t,i,0,n)}Lt(n)}var Ci=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ci||{});function Pa(e,t,n,r){let o=N(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Ci.SignalBased)!==0&&(c=t[i][Me]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Fd(t,c,i,r)}finally{N(o)}}function Yf(e,t,n,r,o){let i=Ut(),s=r&2;try{Lt(-1),s&&t.length>Ce&&Zf(e,t,Ce,!1),j(s?2:0,o),n(r,o)}finally{Lt(i),j(s?3:1,o)}}function Rc(e,t,n){Av(e,t,n),(n.flags&64)===64&&Ov(e,t,n)}function Qf(e,t,n=$e){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function Tv(e,t,n,r){let i=r.get(qy,Af)||n===Ue.ShadowDom,s=e.selectRootElement(t,i);return Sv(s),s}function Sv(e){_v(e)}var _v=()=>null;function Nv(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Rv(e,t,n,r,o,i,s,a){if(!a&&xc(t,e,n,r,o)){Nn(t)&&xv(n,t.index);return}if(t.type&3){let c=$e(t,n);r=Nv(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function xv(e,t){let n=Ve(t,e);n[M]&16||(n[M]|=64)}function Av(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Nn(n)&&Mv(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Xo(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=ei(t,e,s,n);if(Ir(c,t),i!==null&&Lv(t,s-r,c,a,n,i),Le(a)){let u=Ve(n.index,t);u[X]=ei(t,e,s,n)}}}function Ov(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=oy();try{Lt(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];Ca(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&kv(c,u)}}finally{Lt(-1),Ca(s)}}function kv(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Kf(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];fv(t,i.selectors,!1)&&(r??=[],Le(i)?r.unshift(i):r.push(i))}return r}function Pv(e,t,n,r,o,i){let s=$e(e,t);Fv(t[ee],s,i,e.value,n,r,o)}function Fv(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?gi(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Lv(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Pa(r,n,c,u)}}function jv(e,t){let n=e[vn],r=n?n.get(Be,null):null;r&&r.handleError(t)}function xc(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],f=t.data[u];Pa(f,n[u],l,o),a=!0}if(i)for(let c of i){let u=n[c],l=t.data[c];Pa(l,u,r,o),a=!0}return a}function Vv(e,t){let n=Ve(t,e),r=n[x];Bv(r,n);let o=n[ot];o!==null&&n[dr]===null&&(n[dr]=Pf(o,n[vn])),j(18),Ac(r,n,n[X]),j(19,n[X])}function Bv(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Ac(e,t,n){lc(t);try{let r=e.viewQuery;r!==null&&ka(1,r,n);let o=e.template;o!==null&&Yf(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[En]?.finishViewCreation(e),e.staticContentQueries&&Ff(e,t),e.staticViewQueries&&ka(2,e.viewQuery,n);let i=e.components;i!==null&&Uv(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[M]&=-5,dc()}}function Uv(e,t){for(let n=0;n<t.length;n++)Vv(e,t[n])}function Oc(e,t,n,r){let o=N(null);try{let i=t.tView,a=e[M]&4096?4096:16,c=_c(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[Dn]=u;let l=e[En];return l!==null&&(c[En]=l.createEmbeddedView(i)),Ac(i,c,n),c}finally{N(o)}}function ii(e,t){return!t||t.firstChild===null||Mf(e)}var $v;function kc(e,t){return $v(e,t)}var nt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(nt||{});function Jf(e){return(e.flags&32)===32}function pn(e,t,n,r,o){if(r!=null){let i,s=!1;it(r)?i=r:gt(r)&&(s=!0,r=r[ot]);let a=je(r);e===0&&n!==null?o==null?zf(t,n,a):oi(t,n,a,o||null,!0):e===1&&n!==null?oi(t,n,a,o||null,!0):e===2?Ev(t,a,s):e===3&&t.destroyNode(a),i!=null&&eD(t,e,i,n,o)}}function Hv(e,t){Xf(e,t),t[ot]=null,t[xe]=null}function zv(e,t,n,r,o,i){r[ot]=o,r[xe]=t,Mi(e,r,n,1,o,i)}function Xf(e,t){t[tt].changeDetectionScheduler?.notify(9),Mi(e,t,t[ee],2,null,null)}function qv(e){let t=e[fr];if(!t)return ia(e[x],e);for(;t;){let n=null;if(gt(t))n=t[fr];else{let r=t[oe];r&&(n=r)}if(!n){for(;t&&!t[_e]&&t!==e;)gt(t)&&ia(t[x],t),t=t[ie];t===null&&(t=e),gt(t)&&ia(t[x],t),n=t&&t[_e]}t=n}}function Pc(e,t){let n=e[Zo],r=n.indexOf(t);n.splice(r,1)}function bi(e,t){if(Rn(t))return;let n=t[ee];n.destroyNode&&Mi(e,t,n,3,null,null),qv(t)}function ia(e,t){if(Rn(t))return;let n=N(null);try{t[M]&=-129,t[M]|=256,t[Ie]&&Os(t[Ie]),Wv(e,t),Gv(e,t),t[x].type===1&&t[ee].destroy();let r=t[Dn];if(r!==null&&it(t[ie])){r!==t[ie]&&Pc(r,t);let o=t[En];o!==null&&o.detachView(e)}xa(t)}finally{N(n)}}function Gv(e,t){let n=e.cleanup,r=t[qo];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[qo]=null);let o=t[pt];if(o!==null){t[pt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Go];if(i!==null){t[Go]=null;for(let s of i)s.destroy()}}function Wv(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof jt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];j(4,a,c);try{c.call(a)}finally{j(5,a,c)}}else{j(4,o,i);try{i.call(o)}finally{j(5,o,i)}}}}}function Zv(e,t,n){return Yv(e,t.parent,n)}function Yv(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[ot];if(Nn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ue.None||o===Ue.Emulated)return null}return $e(r,n)}function Qv(e,t,n){return Jv(e,t,n)}function Kv(e,t,n){return e.type&40?$e(e,n):null}var Jv=Kv,Gl;function Fc(e,t,n,r){let o=Zv(e,r,t),i=t[ee],s=r.parent||t[xe],a=Qv(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)ql(i,o,n[c],a,!1);else ql(i,o,n,a,!1);Gl!==void 0&&Gl(i,r,t,n,o)}function ar(e,t){if(t!==null){let n=t.type;if(n&3)return $e(t,e);if(n&4)return Fa(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return ar(e,r);{let o=e[t.index];return it(o)?Fa(-1,o):je(o)}}else{if(n&128)return ar(e,t.next);if(n&32)return kc(t,e)()||je(e[t.index]);{let r=eh(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Ft(e[Ne]);return ar(o,r)}else return ar(e,t.next)}}}return null}function eh(e,t){if(t!==null){let r=e[Ne][xe],o=t.projection;return r.projection[o]}return null}function Fa(e,t){let n=oe+e+1;if(n<t.length){let r=t[n],o=r[x].firstChild;if(o!==null)return ar(r,o)}return t[Pt]}function Lc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Ir(je(a),r),n.flags|=2),!Jf(n))if(c&8)Lc(e,t,n.child,r,o,i,!1),pn(t,e,o,a,i);else if(c&32){let u=kc(n,r),l;for(;l=u();)pn(t,e,o,l,i);pn(t,e,o,a,i)}else c&16?Xv(e,t,r,n,o,i):pn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Mi(e,t,n,r,o,i){Lc(n,r,e.firstChild,t,o,i,!1)}function Xv(e,t,n,r,o,i){let s=n[Ne],c=s[xe].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];pn(t,e,o,l,i)}else{let u=c,l=s[ie];Mf(r)&&(u.flags|=128),Lc(e,t,u,l,o,i,!0)}}function eD(e,t,n,r,o){let i=n[Pt],s=je(n);i!==s&&pn(t,e,r,i,o);for(let a=oe;a<n.length;a++){let c=n[a];Mi(c[x],c,e,t,r,i)}}function tD(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:nt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=nt.Important),e.setStyle(n,r,o,i))}}function si(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(je(i)),it(i)&&nD(i,r);let s=n.type;if(s&8)si(e,t,n.child,r);else if(s&32){let a=kc(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=eh(t,n);if(Array.isArray(a))r.push(...a);else{let c=Ft(t[Ne]);si(c[x],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function nD(e,t){for(let n=oe;n<e.length;n++){let r=e[n],o=r[x].firstChild;o!==null&&si(r[x],r,o,t)}e[Pt]!==e[ot]&&t.push(e[Pt])}function th(e){if(e[gn]!==null){for(let t of e[gn])t.impl.addSequence(t);e[gn].length=0}}var nh=[];function rD(e){return e[Ie]??oD(e)}function oD(e){let t=nh.pop()??Object.create(sD);return t.lView=e,t}function iD(e){e.lView[Ie]!==e&&(e.lView=null,nh.push(e))}var sD=B(y({},Jn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Dr(e.lView)},consumerOnSignalRead(){this.lView[Ie]=this}});function aD(e){let t=e[Ie]??Object.create(cD);return t.lView=e,t}var cD=B(y({},Jn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Ft(e.lView);for(;t&&!rh(t[x]);)t=Ft(t);t&&Hd(t)},consumerOnSignalRead(){this.lView[Ie]=this}});function rh(e){return e.type!==2}function oh(e){if(e[Go]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Go])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[M]&8192)}}var uD=100;function ih(e,t=!0,n=0){let o=e[tt].rendererFactory,i=!1;i||o.begin?.();try{lD(e,n)}catch(s){throw t&&jv(e,s),s}finally{i||o.end?.()}}function lD(e,t){let n=Qd();try{xl(!0),La(e,t);let r=0;for(;Di(e);){if(r===uD)throw new v(103,!1);r++,La(e,1)}}finally{xl(n)}}function dD(e,t,n,r){if(Rn(t))return;let o=t[M],i=!1,s=!1;lc(t);let a=!0,c=null,u=null;i||(rh(e)?(u=rD(t),c=oo(u)):Ss()===null?(a=!1,u=aD(t),c=oo(u)):t[Ie]&&(Os(t[Ie]),t[Ie]=null));try{$d(t),ey(e.bindingStartIndex),n!==null&&Yf(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let d=e.preOrderCheckHooks;d!==null&&Fo(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Lo(t,d,0,null),ra(t,0)}if(s||fD(t),oh(t),sh(t,0),e.contentQueries!==null&&Ff(e,t),!i)if(l){let d=e.contentCheckHooks;d!==null&&Fo(t,d)}else{let d=e.contentHooks;d!==null&&Lo(t,d,1),ra(t,1)}pD(e,t);let f=e.components;f!==null&&ch(t,f,0);let h=e.viewQuery;if(h!==null&&ka(2,h,r),!i)if(l){let d=e.viewCheckHooks;d!==null&&Fo(t,d)}else{let d=e.viewHooks;d!==null&&Lo(t,d,2),ra(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[na]){for(let d of t[na])d();t[na]=null}i||(th(t),t[M]&=-73)}catch(l){throw i||Dr(t),l}finally{u!==null&&(xs(u,c),a&&iD(u)),dc()}}function sh(e,t){for(let n=_f(e);n!==null;n=Nf(n))for(let r=oe;r<n.length;r++){let o=n[r];ah(o,t)}}function fD(e){for(let t=_f(e);t!==null;t=Nf(t)){if(!(t[M]&2))continue;let n=t[Zo];for(let r=0;r<n.length;r++){let o=n[r];Hd(o)}}}function hD(e,t,n){j(18);let r=Ve(t,e);ah(r,n),j(19,r[X])}function ah(e,t){cc(e)&&La(e,t)}function La(e,t){let r=e[x],o=e[M],i=e[Ie],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&As(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)dD(r,e,r.template,e[X]);else if(o&8192){oh(e),sh(e,1);let a=r.components;a!==null&&ch(e,a,1),th(e)}}function ch(e,t,n){for(let r=0;r<t.length;r++)hD(e,t[r],n)}function pD(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Lt(~o);else{let i=o,s=n[++r],a=n[++r];ry(s,i);let c=t[i];j(24,c),a(2,c),j(25,c)}}}finally{Lt(-1)}}function jc(e,t){let n=Qd()?64:1088;for(e[tt].changeDetectionScheduler?.notify(t);e;){e[M]|=n;let r=Ft(e);if(Yo(e)&&!r)return e;e=r}return null}function uh(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function gD(e,t){let n=oe+t;if(n<e.length)return e[n]}function Vc(e,t,n,r=!0){let o=t[x];if(yD(o,t,e,n),r){let s=Fa(n,e),a=t[ee],c=a.parentNode(e[Pt]);c!==null&&zv(o,e[xe],a,t,c,s)}let i=t[dr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function mD(e,t){let n=pr(e,t);return n!==void 0&&bi(n[x],n),n}function pr(e,t){if(e.length<=oe)return;let n=oe+t,r=e[n];if(r){let o=r[Dn];o!==null&&o!==e&&Pc(o,r),t>0&&(e[n-1][_e]=r[_e]);let i=Ho(e,oe+t);Hv(r[x],r);let s=i[En];s!==null&&s.detachView(i[x]),r[ie]=null,r[_e]=null,r[M]&=-129}return r}function yD(e,t,n,r){let o=oe+r,i=n.length;r>0&&(n[o-1][_e]=t),r<i-oe?(t[_e]=n[o],bd(n,oe+r,t)):(n.push(t),t[_e]=null),t[ie]=n;let s=t[Dn];s!==null&&n!==s&&lh(s,t);let a=t[En];a!==null&&a.insertView(e),wa(t),t[M]|=128}function lh(e,t){let n=e[Zo],r=t[ie];if(gt(r))e[M]|=2;else{let o=r[ie][Ne];t[Ne]!==o&&(e[M]|=2)}n===null?e[Zo]=[t]:n.push(t)}var gr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[x];return si(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[X]}set context(t){this._lView[X]=t}get destroyed(){return Rn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[ie];if(it(t)){let n=t[Wo],r=n?n.indexOf(this):-1;r>-1&&(pr(t,r),Ho(n,r))}this._attachedToViewContainer=!1}bi(this._lView[x],this._lView)}onDestroy(t){zd(this._lView,t)}markForCheck(){jc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){wa(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,ih(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new v(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Yo(this._lView),n=this._lView[Dn];n!==null&&!t&&Pc(n,this._lView),Xf(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new v(902,!1);this._appRef=t;let n=Yo(this._lView),r=this._lView[Dn];r!==null&&!n&&lh(r,this._lView),wa(this._lView)}};var Bc=(()=>{class e{static __NG_ELEMENT_ID__=ED}return e})(),vD=Bc,DD=class extends vD{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Oc(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new gr(o)}};function ED(){return wD(ye(),L())}function wD(e,t){return e.type&4?new DD(t,e,wi(e,t)):null}function Uc(e,t,n,r,o){let i=e.data[t];if(i===null)i=ID(e,t,n,r,o),ny()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Jm();i.injectorIndex=s===null?-1:s.injectorIndex}return Er(i,!0),i}function ID(e,t,n,r,o){let i=Zd(),s=Yd(),a=s?i:i&&i.parent,c=e.data[t]=bD(e,a,n,t,r,o);return CD(e,c,i,s),c}function CD(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function bD(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Ym()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var aN=new RegExp(`^(\\d+)*(${zy}|${Hy})*(.*)`);var MD=()=>null;function ai(e,t){return MD(e,t)}var TD=class{},dh=class{},ja=class{resolveComponentFactory(t){throw Error(`No component factory found for ${fe(t)}.`)}},Ti=class{static NULL=new ja},Cn=class{},Si=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>SD()}return e})();function SD(){let e=L(),t=ye(),n=Ve(t.index,e);return(gt(n)?n:e)[ee]}var _D=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>null})}return e})();var sa={},Va=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=mi(r);let o=this.injector.get(t,sa,r);return o!==sa||n===sa?o:this.parentInjector.get(t,n,r)}};function Wl(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Il(o,a);else if(i==2){let c=a,u=t[++s];r=Il(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function ve(e,t=_.Default){let n=L();if(n===null)return I(e,t);let r=ye();return gf(r,n,re(e),t)}function fh(){let e="invalid";throw new Error(e)}function hh(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,u=null,l=RD(s);l===null?a=s:[a,c,u]=l,OD(e,t,n,a,i,c,u)}i!==null&&r!==null&&ND(n,r,i)}function ND(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new v(-301,!1);r.push(t[o],i)}}function RD(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Le(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,xD(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function xD(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function AD(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function OD(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let d=r[h];!c&&Le(d)&&(c=!0,AD(e,n,h)),Ma(Xo(n,t),e,d.type)}VD(n,e.data.length,a);for(let h=0;h<a;h++){let d=r[h];d.providersResolver&&d.providersResolver(d)}let u=!1,l=!1,f=Wf(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let h=0;h<a;h++){let d=r[h];if(n.mergedAttrs=hr(n.mergedAttrs,d.hostAttrs),PD(e,n,t,f,d),jD(f,d,o),s!==null&&s.has(d)){let[m,w]=s.get(d);n.directiveToIndex.set(d.type,[f,m+n.directiveStart,w+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let g=d.type.prototype;!u&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),u=!0),!l&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),f++}kD(e,n,i)}function kD(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Zl(0,t,o,r),Zl(1,t,o,r),Ql(t,r,!1);else{let i=n.get(o);Yl(0,t,i,r),Yl(1,t,i,r),Ql(t,r,!0)}}}function Zl(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),ph(t,i)}}function Yl(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),ph(t,s)}}function ph(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Ql(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Tc(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===t){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function PD(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=At(o.type,!0)),s=new jt(i,Le(o),ve);e.blueprint[r]=s,n[r]=s,FD(e,t,r,Wf(e,n,o.hostVars,yt),o)}function FD(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;LD(s)!=a&&s.push(a),s.push(n,r,i)}}function LD(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function jD(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Le(t)&&(n[""]=e)}}function VD(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function gh(e,t,n,r,o,i,s,a){let c=t.consts,u=wn(c,s),l=Uc(t,e,2,r,u);return i&&hh(t,n,l,wn(c,a),o),l.mergedAttrs=hr(l.mergedAttrs,l.attrs),l.attrs!==null&&Wl(l,l.attrs,!1),l.mergedAttrs!==null&&Wl(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function mh(e,t){sf(e,t),Pd(t)&&e.queries.elementEnd(t)}var ci=class extends Ti{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=kt(t);return new bn(n,this.ngModule)}};function BD(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Ci.SignalBased)!==0};return o&&(i.transform=o),i})}function UD(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function $D(e,t,n){let r=t instanceof te?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Va(n,r):n}function HD(e){let t=e.get(Cn,null);if(t===null)throw new v(407,!1);let n=e.get(_D,null),r=e.get(In,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function zD(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Hf(t,n,n==="svg"?Bd:n==="math"?Bm:null)}var bn=class extends dh{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=BD(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=UD(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=mv(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){j(22);let i=N(null);try{let s=this.componentDef,a=r?["ng-version","19.2.13"]:yv(this.componentDef.selectors[0]),c=Sc(0,null,null,1,0,null,null,null,null,[a],null),u=$D(s,o||this.ngModule,t),l=HD(u),f=l.rendererFactory.createRenderer(null,s),h=r?Tv(f,r,s.encapsulation,u):zD(s,f),d=_c(null,c,null,512|Gf(s),null,null,l,f,u,null,Pf(h,u,!0));d[Ce]=h,lc(d);let g=null;try{let m=gh(Ce,c,d,"#host",()=>[this.componentDef],!0,0);h&&(qf(f,h,m),Ir(h,d)),Rc(c,d,m),Lf(c,m,d),mh(c,m),n!==void 0&&qD(m,this.ngContentSelectors,n),g=Ve(m.index,d),d[X]=g[X],Ac(c,d,null)}catch(m){throw g!==null&&xa(g),xa(d),m}finally{j(23),dc()}return new Ba(this.componentType,d)}finally{N(i)}}},Ba=class extends TD{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=ac(n[x],Ce),this.location=wi(this._tNode,n),this.instance=Ve(this._tNode.index,n)[X],this.hostView=this.changeDetectorRef=new gr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=xc(r,o[x],o,t,n);this.previousInputValues.set(t,n);let s=Ve(r.index,o);jc(s,1)}get injector(){return new xt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function qD(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Cr=(()=>{class e{static __NG_ELEMENT_ID__=GD}return e})();function GD(){let e=ye();return ZD(e,L())}var WD=Cr,yh=class extends WD{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return wi(this._hostTNode,this._hostLView)}get injector(){return new xt(this._hostTNode,this._hostLView)}get parentInjector(){let t=pc(this._hostTNode,this._hostLView);if(uf(t)){let n=Ko(t,this._hostLView),r=Qo(t),o=n[x].data[r+8];return new xt(o,n)}else return new xt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Kl(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-oe}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=ai(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,ii(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Fm(t),a;if(s)a=n;else{let g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}let c=s?t:new bn(kt(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let m=(s?u:this.parentInjector).get(te,null);m&&(i=m)}let l=kt(c.componentType??{}),f=ai(this._lContainer,l?.id??null),h=f?.firstChild??null,d=c.create(u,o,h,i);return this.insertImpl(d.hostView,a,ii(this._hostTNode,f)),d}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Hm(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[ie],u=new yh(c,c[xe],c[ie]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Vc(s,o,i,r),t.attachToViewContainerRef(),bd(aa(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Kl(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=pr(this._lContainer,n);r&&(Ho(aa(this._lContainer),n),bi(r[x],r))}detach(t){let n=this._adjustIndex(t,-1),r=pr(this._lContainer,n);return r&&Ho(aa(this._lContainer),n)!=null?new gr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Kl(e){return e[Wo]}function aa(e){return e[Wo]||(e[Wo]=[])}function ZD(e,t){let n,r=t[e.index];return it(r)?n=r:(n=uh(r,t,null,e),t[e.index]=n,Nc(t,n)),QD(n,t,e,r),new yh(n,e,t)}function YD(e,t){let n=e[ee],r=n.createComment(""),o=$e(t,e),i=n.parentNode(o);return oi(n,i,r,n.nextSibling(o),!1),r}var QD=XD,KD=()=>!1;function JD(e,t,n){return KD(e,t,n)}function XD(e,t,n,r){if(e[Pt])return;let o;n.type&8?o=je(r):o=YD(t,n),e[Pt]=o}var Mn=class{},$c=class{};var Ua=class extends Mn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new ci(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Sd(t);this._bootstrapComponents=Uf(i.bootstrap),this._r3Injector=vf(t,n,[{provide:Mn,useValue:this},{provide:Ti,useValue:this.componentFactoryResolver},...r],fe(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},$a=class extends $c{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Ua(this.moduleType,t,[])}};var ui=class extends Mn{injector;componentFactoryResolver=new ci(this);instance=null;constructor(t){super();let n=new lr([...t.providers,{provide:Mn,useValue:this},{provide:Ti,useValue:this.componentFactoryResolver}],t.parent||ic(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function br(e,t,n=null){return new ui({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var eE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=_d(!1,n.type),o=r.length>0?br([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=E({token:e,providedIn:"environment",factory:()=>new e(I(te))})}return e})();function vh(e){return hi(()=>{let t=Eh(e),n=B(y({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Tf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(eE).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ue.Emulated,styles:e.styles||we,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Ht("NgStandalone"),wh(n);let r=e.dependencies;return n.directiveDefs=Jl(r,!1),n.pipeDefs=Jl(r,!0),n.id=iE(n),n})}function tE(e){return kt(e)||Cm(e)}function nE(e){return e!==null}function vt(e){return hi(()=>({type:e.type,bootstrap:e.bootstrap||we,declarations:e.declarations||we,imports:e.imports||we,exports:e.exports||we,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function rE(e,t){if(e==null)return Ot;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Ci.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function oE(e){if(e==null)return Ot;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function On(e){return hi(()=>{let t=Eh(e);return wh(t),t})}function Dh(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Eh(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ot,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||we,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:rE(e.inputs,t),outputs:oE(e.outputs),debugInfo:null}}function wh(e){e.features?.forEach(t=>t(e))}function Jl(e,t){if(!e)return null;let n=t?bm:tE;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(nE)}function iE(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function sE(e){return Object.getPrototypeOf(e.prototype).constructor}function aE(e){let t=sE(e.type),n=!0,r=[e];for(;t;){let o;if(Le(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new v(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ca(e.inputs),s.declaredInputs=ca(e.declaredInputs),s.outputs=ca(e.outputs);let a=o.hostBindings;a&&fE(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&lE(e,c),u&&dE(e,u),cE(e,o),nm(e.outputs,o.outputs),Le(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===aE&&(n=!1)}}t=Object.getPrototypeOf(t)}uE(r)}function cE(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function uE(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=hr(o.hostAttrs,n=hr(n,o.hostAttrs))}}function ca(e){return e===Ot?{}:e===we?[]:e}function lE(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function dE(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function fE(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Hc(e,t,n){return e[t]=n}function Ih(e,t){return e[t]}function rt(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Xl(e,t,n,r){let o=rt(e,t,n);return rt(e,t+1,r)||o}function hE(e,t,n,r,o,i){let s=Xl(e,t,n,r);return Xl(e,t+2,o,i)||s}function pE(e,t,n,r,o,i,s,a,c){let u=t.consts,l=Uc(t,e,4,s||null,a||null);Wd()&&hh(t,n,l,wn(u,c),Kf),l.mergedAttrs=hr(l.mergedAttrs,l.attrs),sf(t,l);let f=l.tView=Sc(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),f.queries=t.queries.embeddedTView(l)),l}function Ha(e,t,n,r,o,i,s,a,c,u){let l=n+Ce,f=t.firstCreatePass?pE(l,t,e,r,o,i,s,a,c):t.data[l];Er(f,!1);let h=mE(t,e,f,n);fc()&&Fc(t,e,h,f),Ir(h,e);let d=uh(h,e,h,f);return e[l]=d,Nc(e,d),JD(d,f,e),sc(f)&&Rc(t,e,f),c!=null&&Qf(e,f,u),f}function gE(e,t,n,r,o,i,s,a){let c=L(),u=me(),l=wn(u.consts,i);return Ha(c,u,e,t,n,r,o,l,s,a),gE}var mE=yE;function yE(e,t,n,r){return hc(!0),t[ee].createComment("")}var Ch=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var bh=new D("");var vE=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new za})}return e})(),za=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function Mr(e){return!!e&&typeof e.then=="function"}function Mh(e){return!!e&&typeof e.subscribe=="function"}var Th=new D("");function zc(e){return Sn([{provide:Th,multi:!0,useValue:e}])}var Sh=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=p(Th,{optional:!0})??[];injector=p(Re);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=he(this.injector,o);if(Mr(i))n.push(i);else if(Mh(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_i=new D("");function DE(){Ps(()=>{throw new v(600,!1)})}function EE(e){return e.isBoundToModule}var wE=10;var Vt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(ky);afterRenderManager=p(kf);zonelessEnabled=p(yc);rootEffectScheduler=p(vE);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new Q;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=p(st).hasPendingTasks.pipe(R(n=>!n));constructor(){p(An,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=p(te);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Re.NULL){j(10);let i=n instanceof dh;if(!this._injector.get(Sh).done){let d="";throw new v(405,d)}let a;i?a=n:a=this._injector.get(Ti).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=EE(a)?void 0:this._injector.get(Mn),u=r||a.selector,l=a.create(o,[],u,c),f=l.location.nativeElement,h=l.injector.get(bh,null);return h?.registerApplication(f),l.onDestroy(()=>{this.detachView(l.hostView),jo(this.components,l),h?.unregisterApplication(f)}),this._loadComponent(l),j(11,l),l}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){j(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Cc.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new v(101,!1);let n=N(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,N(n),this.afterTick.next(),j(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Cn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<wE;)j(14),this.synchronizeOnce(),j(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)IE(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Di(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;jo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(_i,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>jo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new v(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function jo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function IE(e,t,n,r){if(!n&&!Di(e))return;ih(e,t,n&&!r?0:1)}function qc(e,t,n,r){let o=L(),i=Ei();if(rt(o,i,t)){let s=me(),a=rf();Pv(a,o,e,t,n,r)}return qc}function CE(e,t,n,r){return rt(e,Ei(),n)?t+gi(n)+r:yt}function ko(e,t){return e<<17|t<<2}function Bt(e){return e>>17&32767}function bE(e){return(e&2)==2}function ME(e,t){return e&131071|t<<17}function qa(e){return e|2}function Tn(e){return(e&131068)>>2}function ua(e,t){return e&-131069|t<<2}function TE(e){return(e&1)===1}function Ga(e){return e|1}function SE(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Bt(s),c=Tn(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let f=n;l=f[1],(l===null||yr(f,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let h=Bt(e[a+1]);e[r+1]=ko(h,a),h!==0&&(e[h+1]=ua(e[h+1],r)),e[a+1]=ME(e[a+1],r)}else e[r+1]=ko(a,0),a!==0&&(e[a+1]=ua(e[a+1],r)),a=r;else e[r+1]=ko(c,0),a===0?a=r:e[c+1]=ua(e[c+1],r),c=r;u&&(e[r+1]=qa(e[r+1])),ed(e,l,r,!0),ed(e,l,r,!1),_E(t,l,e,r,i),s=ko(a,c),i?t.classBindings=s:t.styleBindings=s}function _E(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&yr(i,t)>=0&&(n[r+1]=Ga(n[r+1]))}function ed(e,t,n,r){let o=e[n+1],i=t===null,s=r?Bt(o):Tn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];NE(c,t)&&(a=!0,e[s+1]=r?Ga(u):qa(u)),s=r?Bt(u):Tn(u)}a&&(e[n+1]=r?qa(o):Ga(o))}function NE(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?yr(e,t)>=0:!1}function RE(e,t,n){let r=L(),o=Ei();if(rt(r,o,t)){let i=me(),s=rf();Rv(i,s,r,e,t,r[ee],n,!1)}return RE}function td(e,t,n,r,o){xc(t,e,n,o?"class":"style",r)}function xE(e,t){return AE(e,t,null,!0),xE}function AE(e,t,n,r){let o=L(),i=me(),s=ty(2);if(i.firstUpdatePass&&kE(i,e,s,r),t!==yt&&rt(o,s,t)){let a=i.data[Ut()];VE(i,a,o,o[ee],e,o[s+1]=BE(t,n),r,s)}}function OE(e,t){return t>=e.expandoStartIndex}function kE(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Ut()],s=OE(e,n);UE(i,r)&&t===null&&!s&&(t=!1),t=PE(o,i,t,r),SE(o,i,t,n,s,r)}}function PE(e,t,n,r){let o=iy(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=la(null,e,t,n,r),n=mr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=la(o,e,t,n,r),i===null){let c=FE(e,t,r);c!==void 0&&Array.isArray(c)&&(c=la(null,e,t,c[1],r),c=mr(c,t.attrs,r),LE(e,t,r,c))}else i=jE(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function FE(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Tn(r)!==0)return e[Bt(r)]}function LE(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Bt(o)]=r}function jE(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=mr(r,s,n)}return mr(r,t.attrs,n)}function la(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=mr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function mr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),wm(e,s,n?!0:t[++i]))}return e===void 0?null:e}function VE(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=TE(u)?nd(c,t,n,o,Tn(u),s):void 0;if(!li(l)){li(i)||bE(u)&&(i=nd(c,null,n,o,a,s));let f=Ud(Ut(),n);tD(r,s,f,o,i)}}function nd(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,f=l===null,h=n[o+1];h===yt&&(h=f?we:void 0);let d=f?ea(h,r):l===r?h:void 0;if(u&&!li(d)&&(d=ea(c,r)),li(d)&&(a=d,s))return a;let g=e[o+1];o=s?Bt(g):Tn(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ea(c,r))}return a}function li(e){return e!==void 0}function BE(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=fe(Ii(e)))),e}function UE(e,t){return(e.flags&(t?8:16))!==0}var Wa=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function da(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function $E(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let u=e.at(i),l=t[i],f=da(i,u,i,l,n);if(f!==0){f<0&&e.updateValue(i,l),i++;continue}let h=e.at(s),d=t[c],g=da(s,h,c,d,n);if(g!==0){g<0&&e.updateValue(s,d),s--,c--;continue}let m=n(i,u),w=n(s,h),S=n(i,l);if(Object.is(S,w)){let se=n(c,d);Object.is(se,m)?(e.swap(i,s),e.updateValue(s,d),c--,s--):e.move(s,i),e.updateValue(i,l),i++;continue}if(r??=new di,o??=od(e,i,s,n),Za(e,r,i,S))e.updateValue(i,l),i++,s++;else if(o.has(S))r.set(m,e.detach(i)),s--;else{let se=e.create(i,t[i]);e.attach(i,se),i++,s++}}for(;i<=c;)rd(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),u=c.next();for(;!u.done&&i<=s;){let l=e.at(i),f=u.value,h=da(i,l,i,f,n);if(h!==0)h<0&&e.updateValue(i,f),i++,u=c.next();else{r??=new di,o??=od(e,i,s,n);let d=n(i,f);if(Za(e,r,i,d))e.updateValue(i,f),i++,s++,u=c.next();else if(!o.has(d))e.attach(i,e.create(i,f)),i++,s++,u=c.next();else{let g=n(i,l);r.set(g,e.detach(i)),s--}}}for(;!u.done;)rd(e,r,n,e.length,u.value),u=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Za(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function rd(e,t,n,r,o){if(Za(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function od(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var di=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};var Ya=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-oe}};var Qa=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function pN(e,t,n,r,o,i,s,a,c,u,l,f,h){Ht("NgControlFlow");let d=L(),g=me(),m=c!==void 0,w=L(),S=a?s.bind(w[Ne][X]):s,se=new Qa(m,S);w[Ce+e]=se,Ha(d,g,e+1,t,n,r,o,wn(g.consts,i)),m&&Ha(d,g,e+2,c,u,l,f,wn(g.consts,h))}var Ka=class extends Wa{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-oe}at(t){return this.getLView(t)[X].$implicit}attach(t,n){let r=n[dr];this.needsIndexUpdate||=t!==this.length,Vc(this.lContainer,n,t,ii(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,HE(this.lContainer,t)}create(t,n){let r=ai(this.lContainer,this.templateTNode.tView.ssrId),o=Oc(this.hostLView,this.templateTNode,new Ya(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){bi(t[x],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[X].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[X].$index=t}getLView(t){return zE(this.lContainer,t)}};function gN(e){let t=N(null),n=Ut();try{let r=L(),o=r[x],i=r[n],s=n+1,a=id(r,s);if(i.liveCollection===void 0){let u=sd(o,s);i.liveCollection=new Ka(a,r,u)}else i.liveCollection.reset();let c=i.liveCollection;if($E(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let u=Ei(),l=c.length===0;if(rt(r,u,l)){let f=n+2,h=id(r,f);if(l){let d=sd(o,f),g=ai(h,d.tView.ssrId),m=Oc(r,d,void 0,{dehydratedView:g});Vc(h,m,0,ii(d,g))}else mD(h,0)}}}finally{N(t)}}function id(e,t){return e[t]}function HE(e,t){return pr(e,t)}function zE(e,t){return gD(e,t)}function sd(e,t){return ac(e,t)}function _h(e,t,n,r){let o=L(),i=me(),s=Ce+e,a=o[ee],c=i.firstCreatePass?gh(s,i,o,t,Kf,Wd(),n,r):i.data[s],u=qE(i,o,c,a,t,e);o[s]=u;let l=sc(c);return Er(c,!0),qf(a,u,c),!Jf(c)&&fc()&&Fc(i,o,u,c),(Gm()===0||l)&&Ir(u,o),Wm(),l&&(Rc(i,o,c),Lf(i,c,o)),r!==null&&Qf(o,c),_h}function Nh(){let e=ye();Yd()?Xm():(e=e.parent,Er(e,!1));let t=e;Qm(t)&&Km(),Zm();let n=me();return n.firstCreatePass&&mh(n,t),t.classesWithoutHost!=null&&fy(t)&&td(n,t,L(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&hy(t)&&td(n,t,L(),t.stylesWithoutHost,!1),Nh}function Gc(e,t,n,r){return _h(e,t,n,r),Nh(),Gc}var qE=(e,t,n,r,o,i)=>(hc(!0),Hf(r,o,uy()));var fi="en-US";var GE=fi;function WE(e){typeof e=="string"&&(GE=e.toLowerCase().replace(/_/g,"-"))}function ad(e,t,n){return function r(o){if(o===Function)return n;let i=Nn(e)?Ve(e.index,t):t;jc(i,5);let s=t[X],a=cd(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=cd(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function cd(e,t,n,r){let o=N(null);try{return j(6,t,n),n(r)!==!1}catch(i){return ZE(e,i),!1}finally{j(7,t,n),N(o)}}function ZE(e,t){let n=e[vn],r=n?n.get(Be,null):null;r&&r.handleError(t)}function ud(e,t,n,r,o,i){let s=t[n],a=t[x],u=a.data[n].outputs[r],l=s[u],f=a.firstCreatePass?Gd(a):null,h=qd(t),d=l.subscribe(i),g=h.length;h.push(i,d),f&&f.push(o,e.index,g,-(g+1))}function Wc(e,t,n,r){let o=L(),i=me(),s=ye();return QE(i,o,o[ee],s,e,t,r),Wc}function YE(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[qo],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function QE(e,t,n,r,o,i,s){let a=sc(r),u=e.firstCreatePass?Gd(e):null,l=qd(t),f=!0;if(r.type&3||s){let h=$e(r,t),d=s?s(h):h,g=l.length,m=s?S=>s(je(S[r.index])):r.index,w=null;if(!s&&a&&(w=YE(e,t,o,r.index)),w!==null){let S=w.__ngLastListenerFn__||w;S.__ngNextListenerFn__=i,w.__ngLastListenerFn__=i,f=!1}else{i=ad(r,t,i),Jy(t,d,o,i);let S=n.listen(d,o,i);l.push(i,S),u&&u.push(o,m,g,g+1)}}else i=ad(r,t,i);if(f){let h=r.outputs?.[o],d=r.hostDirectiveOutputs?.[o];if(d&&d.length)for(let g=0;g<d.length;g+=2){let m=d[g],w=d[g+1];ud(r,t,m,w,o,i)}if(h&&h.length)for(let g of h)ud(r,t,g,o,o,i)}}function mN(e=1){return ay(e)}function yN(e,t=""){let n=L(),r=me(),o=e+Ce,i=r.firstCreatePass?Uc(r,o,1,t,null):r.data[o],s=KE(r,n,i,t,e);n[o]=s,fc()&&Fc(r,n,s,i),Er(i,!1)}var KE=(e,t,n,r,o)=>(hc(!0),vv(t[ee],r));function JE(e){return Rh("",e,""),JE}function Rh(e,t,n){let r=L(),o=CE(r,e,t,n);return o!==yt&&XE(r,Ut(),o),Rh}function XE(e,t,n){let r=Ud(t,e);Dv(e[ee],r,n)}function ew(e,t,n){let r=me();if(r.firstCreatePass){let o=Le(e);Ja(n,r.data,r.blueprint,o,!0),Ja(t,r.data,r.blueprint,o,!1)}}function Ja(e,t,n,r,o){if(e=re(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ja(e[i],t,n,r,o);else{let i=me(),s=L(),a=ye(),c=yn(e)?e:re(e.provide),u=xd(e),l=a.providerIndexes&1048575,f=a.directiveStart,h=a.providerIndexes>>20;if(yn(e)||!e.multi){let d=new jt(u,o,ve),g=ha(c,t,o?l:l+h,f);g===-1?(Ma(Xo(a,s),i,c),fa(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(d),s.push(d)):(n[g]=d,s[g]=d)}else{let d=ha(c,t,l+h,f),g=ha(c,t,l,l+h),m=d>=0&&n[d],w=g>=0&&n[g];if(o&&!w||!o&&!m){Ma(Xo(a,s),i,c);let S=rw(o?nw:tw,n.length,o,r,u);!o&&w&&(n[g].providerFactory=S),fa(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(S),s.push(S)}else{let S=xh(n[o?g:d],u,!o&&r);fa(i,e,d>-1?d:g,S)}!o&&r&&w&&n[g].componentProviders++}}}function fa(e,t,n,r){let o=yn(t),i=Nm(t);if(o||i){let c=(i?re(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function xh(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ha(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function tw(e,t,n,r,o){return Xa(this.multi,[])}function nw(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=ei(r,r[x],this.providerFactory.index,o);s=c.slice(0,a),Xa(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Xa(i,s);return s}function Xa(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function rw(e,t,n,r,o){let i=new jt(e,n,ve);return i.multi=[],i.index=t,i.componentProviders=0,xh(i,o,r&&!n),i}function vN(e,t=[]){return n=>{n.providersResolver=(r,o)=>ew(r,o?o(e):e,t)}}function DN(e,t,n){let r=uc()+e,o=L();return o[r]===yt?Hc(o,r,n?t.call(n):t()):Ih(o,r)}function EN(e,t,n,r,o,i,s,a){let c=uc()+e,u=L(),l=hE(u,c,n,r,o,i);return rt(u,c+4,s)||l?Hc(u,c+5,a?t.call(a,n,r,o,i,s):t(n,r,o,i,s)):Ih(u,c+5)}function ow(e,t){let n=e[t];return n===yt?void 0:n}function iw(e,t,n,r,o,i){let s=t+n;return rt(e,s,o)?Hc(e,s+1,i?r.call(i,o):r(o)):ow(e,s+1)}function wN(e,t){let n=me(),r,o=e+Ce;n.firstCreatePass?(r=sw(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=At(r.type,!0)),s,a=le(ve);try{let c=Jo(!1),u=i();return Jo(c),$m(n,L(),o,u),u}finally{le(a)}}function sw(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function IN(e,t,n){let r=e+Ce,o=L(),i=Um(o,r);return aw(o,r)?iw(o,uc(),t,i.transform,n,i):i.transform(n)}function aw(e,t){return e[x].data[t].pure}var ec=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Ah=(()=>{class e{compileModuleSync(n){return new $a(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Sd(n),i=Uf(o.declarations).reduce((s,a)=>{let c=kt(a);return c&&s.push(new bn(c)),s},[]);return new ec(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var cw=(()=>{class e{zone=p($);changeDetectionScheduler=p(In);applicationRef=p(Vt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),uw=new D("",{factory:()=>!1});function Oh({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new $(B(y({},kh()),{scheduleInRootZone:n})),[{provide:$,useFactory:e},{provide:ur,multi:!0,useFactory:()=>{let r=p(cw,{optional:!0});return()=>r.initialize()}},{provide:ur,multi:!0,useFactory:()=>{let r=p(lw);return()=>{r.initialize()}}},t===!0?{provide:Ef,useValue:!0}:[],{provide:wf,useValue:n??Df}]}function CN(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Oh({ngZoneFactory:()=>{let o=kh(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&Ht("NgZone_CoalesceEvent"),new $(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Sn([{provide:uw,useValue:!0},{provide:yc,useValue:!1},r])}function kh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var lw=(()=>{class e{subscription=new q;initialized=!1;zone=p($);pendingTasks=p(st);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var dw=(()=>{class e{appRef=p(Vt);taskService=p(st);ngZone=p($);zonelessEnabled=p(yc);tracing=p(An,{optional:!0});disableScheduling=p(Ef,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new q;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ni):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(wf,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ra||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ll:If;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ni+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ll(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fw(){return typeof $localize<"u"&&$localize.locale||fi}var Zc=new D("",{providedIn:"root",factory:()=>p(Zc,_.Optional|_.SkipSelf)||fw()});var tc=new D(""),hw=new D("");function ir(e){return!e.moduleRef}function pw(e){let t=ir(e)?e.r3Injector:e.moduleRef.injector,n=t.get($);return n.run(()=>{ir(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Be,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),ir(e)){let i=()=>t.destroy(),s=e.platformInjector.get(tc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(tc);s.add(i),e.moduleRef.onDestroy(()=>{jo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return mw(r,n,()=>{let i=t.get(Sh);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Zc,fi);if(WE(s||fi),!t.get(hw,!0))return ir(e)?t.get(Vt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(ir(e)){let c=t.get(Vt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return gw(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function gw(e,t){let n=e.injector.get(Vt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new v(-403,!1);t.push(e)}function mw(e,t,n){try{let r=n();return Mr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Vo=null;function yw(e=[],t){return Re.create({name:t,providers:[{provide:yi,useValue:"platform"},{provide:tc,useValue:new Set([()=>Vo=null])},...e]})}function vw(e=[]){if(Vo)return Vo;let t=yw(e);return Vo=t,DE(),Dw(t),t}function Dw(e){let t=e.get(wc,null);he(e,()=>{t?.forEach(n=>n())})}var Tr=(()=>{class e{static __NG_ELEMENT_ID__=Ew}return e})();function Ew(e){return ww(ye(),L(),(e&16)===16)}function ww(e,t,n){if(Nn(e)&&!n){let r=Ve(e.index,t);return new gr(r,r)}else if(e.type&175){let r=t[Ne];return new gr(r,t)}return null}function Ph(e){j(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=vw(r),i=[Oh({}),{provide:In,useExisting:dw},...n||[]],s=new ui({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return pw({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{j(9)}}function Sr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Iw(e){return js(e)}function bN(e,t){return ks(e,t?.equal)}var ld=class{[Me];constructor(t){this[Me]=t}destroy(){this[Me].destroy()}};function Fh(e){let t=kt(e);if(!t)return null;let n=new bn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var W=new D("");var Vh=null;function at(){return Vh}function Yc(e){Vh??=e}var _r=class{},Nr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(Bh),providedIn:"platform"})}return e})(),Qc=new D(""),Bh=(()=>{class e extends Nr{_location;_history;_doc=p(W);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return at().getBaseHref(this._doc)}onPopState(n){let r=at().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=at().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Ni(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Lh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Ae(e){return e&&e[0]!=="?"?`?${e}`:e}var Oe=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(xi),providedIn:"root"})}return e})(),Ri=new D(""),xi=(()=>{class e extends Oe{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(W).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Ni(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Ae(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Ae(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Ae(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(Nr),I(Ri,8))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Et=(()=>{class e{_subject=new Q;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=Mw(Lh(jh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Ae(r))}normalize(n){return e.stripTrailingSlash(bw(this._basePath,jh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ae(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ae(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Ae;static joinWithSlash=Ni;static stripTrailingSlash=Lh;static \u0275fac=function(r){return new(r||e)(I(Oe))};static \u0275prov=E({token:e,factory:()=>Cw(),providedIn:"root"})}return e})();function Cw(){return new Et(I(Oe))}function bw(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function jh(e){return e.replace(/\/index.html$/,"")}function Mw(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Kc=(()=>{class e extends Oe{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Ni(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Ae(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Ae(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(Nr),I(Ri,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var Tw=(()=>{class e{_viewContainer;_context=new Ai;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Uh(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Uh(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(ve(Cr),ve(Bc))};static \u0275dir=On({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ai=class{$implicit=null;ngIf=null};function Uh(e,t){if(e&&!e.createEmbeddedView)throw new v(2020,!1)}function Sw(e,t){return new v(2100,!1)}var _w=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,Nw=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw Sw(e,n);return n.replace(_w,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=Dh({name:"titlecase",type:e,pure:!0})}return e})();var $h=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=vt({type:e});static \u0275inj=mt({})}return e})();function Rr(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Jc="browser",Hh="server";function Oi(e){return e===Hh}var zt=class{};var zh=(()=>{class e{static \u0275prov=E({token:e,providedIn:"root",factory:()=>new Xc(p(W),window)})}return e})(),Xc=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=xw(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function xw(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Fi=new D(""),ru=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new v(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(I(Fi),I($))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),xr=class{_doc;constructor(t){this._doc=t}manager},ki="ng-app-id";function qh(e){for(let t of e)t.remove()}function Gh(e,t){let n=t.createElement("style");return n.textContent=e,n}function Ow(e,t,n,r){let o=e.head?.querySelectorAll(`style[${ki}="${t}"],link[${ki}="${t}"]`);if(o)for(let i of o)i.removeAttribute(ki),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function tu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var ou=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Oi(i),Ow(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Gh);r?.forEach(o=>this.addUsage(o,this.external,tu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(qh(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])qh(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Gh(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,tu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(ki,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(W),I(Ec),I(Ic,8),I(xn))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),eu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},iu=/%COMP%/g;var Zh="%COMP%",kw=`_nghost-${Zh}`,Pw=`_ngcontent-${Zh}`,Fw=!0,Lw=new D("",{providedIn:"root",factory:()=>Fw});function jw(e){return Pw.replace(iu,e)}function Vw(e){return kw.replace(iu,e)}function Yh(e,t){return t.map(n=>n.replace(iu,e))}var su=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,u=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=Oi(a),this.defaultRenderer=new Ar(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ue.ShadowDom&&(r=B(y({},r),{encapsulation:Ue.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Pi?o.applyToHost(n):o instanceof Or&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,f=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Ue.Emulated:i=new Pi(c,u,r,this.appId,l,s,a,f,h);break;case Ue.ShadowDom:return new nu(c,u,n,r,s,a,this.nonce,f,h);default:i=new Or(c,u,r,l,s,a,f,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(I(ru),I(ou),I(Ec),I(Lw),I(W),I(xn),I($),I(Ic),I(An,8))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Ar=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(eu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Wh(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Wh(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new v(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=eu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=eu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(nt.DashCase|nt.Important)?t.style.setProperty(n,r,o&nt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&nt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=at().getGlobalEventTarget(this.doc,t),!t))throw new v(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Wh(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var nu=class extends Ar{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,c,u),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=Yh(o.id,l);for(let h of l){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=h,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let h of f){let d=tu(h,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Or=class extends Ar{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,u){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?Yh(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Pi=class extends Or{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,u,l),this.contentAttr=jw(l),this.hostAttr=Vw(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var Li=class e extends _r{supportsDOMEvents=!0;static makeCurrent(){Yc(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Bw();return n==null?null:Uw(n)}resetBaseElement(){kr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Rr(document.cookie,t)}},kr=null;function Bw(){return kr=kr||document.head.querySelector("base"),kr?kr.getAttribute("href"):null}function Uw(e){return new URL(e,document.baseURI).pathname}var $w=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Kh=(()=>{class e extends xr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(W))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Qh=["alt","control","meta","shift"],Hw={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},zw={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Jh=(()=>{class e extends xr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>at().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Qh.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=Hw[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Qh.forEach(s=>{if(s!==o){let a=zw[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(I(W))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function qw(e,t){return Ph(y({rootComponent:e},Gw(t)))}function Gw(e){return{appProviders:[...Kw,...e?.providers??[]],platformProviders:Qw}}function Ww(){Li.makeCurrent()}function Zw(){return new Be}function Yw(){return xf(document),document}var Qw=[{provide:xn,useValue:Jc},{provide:wc,useValue:Ww,multi:!0},{provide:W,useFactory:Yw}];var Kw=[{provide:yi,useValue:"root"},{provide:Be,useFactory:Zw},{provide:Fi,useClass:Kh,multi:!0,deps:[W]},{provide:Fi,useClass:Jh,multi:!0,deps:[W]},su,ou,ru,{provide:Cn,useExisting:su},{provide:zt,useClass:$w},[]];var Pn=class{},Fn=class{},He=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Bi=class{encodeKey(t){return Xh(t)}encodeValue(t){return Xh(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function Jw(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var Xw=/%(\d[a-f0-9])/gi,eI={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Xh(e){return encodeURIComponent(e).replace(Xw,(t,n)=>eI[n]??t)}function ji(e){return`${e}`}var ut=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Bi,t.fromString){if(t.fromObject)throw new v(2805,!1);this.map=Jw(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(ji):[ji(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ji(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(ji(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ui=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function tI(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function ep(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function tp(e){return typeof Blob<"u"&&e instanceof Blob}function np(e){return typeof FormData<"u"&&e instanceof FormData}function nI(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Pr="Content-Type",$i="Accept",du="X-Request-URL",ip="text/plain",sp="application/json",ap=`${sp}, ${ip}, */*`,kn=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(tI(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new He,this.context??=new Ui,!this.params)this.params=new ut,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||ep(this.body)||tp(this.body)||np(this.body)||nI(this.body)?this.body:this.body instanceof ut?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||np(this.body)?null:tp(this.body)?this.body.type||null:ep(this.body)?null:typeof this.body=="string"?ip:this.body instanceof ut?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?sp:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,f=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((h,d)=>h.set(d,t.setHeaders[d]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((h,d)=>h.set(d,t.setParams[d]),l)),new e(n,r,s,{params:l,headers:u,context:f,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},lt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(lt||{}),Ln=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new He,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Fr=class e extends Ln{constructor(t={}){super(t)}type=lt.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},jn=class e extends Ln{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=lt.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},ct=class extends Ln{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},cp=200,rI=204;function au(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var up=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof kn)i=n;else{let c;o.headers instanceof He?c=o.headers:c=new He(o.headers);let u;o.params&&(o.params instanceof ut?u=o.params:u=new ut({fromObject:o.params})),i=new kn(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=C(i).pipe(Pe(c=>this.handler.handle(c)));if(n instanceof kn||o.observe==="events")return s;let a=s.pipe(ne(c=>c instanceof jn));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new v(2806,!1);return c.body}));case"blob":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new v(2807,!1);return c.body}));case"text":return a.pipe(R(c=>{if(c.body!==null&&typeof c.body!="string")throw new v(2808,!1);return c.body}));case"json":default:return a.pipe(R(c=>c.body))}case"response":return a;default:throw new v(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new ut().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,au(o,r))}post(n,r,o={}){return this.request("POST",n,au(o,r))}put(n,r,o={}){return this.request("PUT",n,au(o,r))}static \u0275fac=function(r){return new(r||e)(I(Pn))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),oI=/^\)\]\}',?\n/;function rp(e){if(e.url)return e.url;let t=du.toLocaleLowerCase();return e.headers.get(t)}var lp=new D(""),Vi=(()=>{class e{fetchImpl=p(cu,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=p($);destroyRef=p($t);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new k(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(uu,i=>r.error(new ct({error:i}))),()=>o.abort()})}doRequest(n,r,o){return Qn(this,null,function*(){let i=this.createRequestInit(n),s;try{let d=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,y({signal:r},i)));iI(d),o.next({type:lt.Sent}),s=yield d}catch(d){o.error(new ct({error:d,status:d.status??0,statusText:d.statusText,url:n.urlWithParams,headers:d.headers}));return}let a=new He(s.headers),c=s.statusText,u=rp(s)??n.urlWithParams,l=s.status,f=null;if(n.reportProgress&&o.next(new Fr({headers:a,status:l,statusText:c,url:u})),s.body){let d=s.headers.get("content-length"),g=[],m=s.body.getReader(),w=0,S,se,H=typeof Zone<"u"&&Zone.current,Jt=!1;if(yield this.ngZone.runOutsideAngular(()=>Qn(this,null,function*(){for(;;){if(this.destroyed){yield m.cancel(),Jt=!0;break}let{done:Tt,value:ws}=yield m.read();if(Tt)break;if(g.push(ws),w+=ws.length,n.reportProgress){se=n.responseType==="text"?(se??"")+(S??=new TextDecoder).decode(ws,{stream:!0}):void 0;let Hu=()=>o.next({type:lt.DownloadProgress,total:d?+d:void 0,loaded:w,partialText:se});H?H.run(Hu):Hu()}}})),Jt){o.complete();return}let Es=this.concatChunks(g,w);try{let Tt=s.headers.get(Pr)??"";f=this.parseBody(n,Es,Tt)}catch(Tt){o.error(new ct({error:Tt,headers:new He(s.headers),status:s.status,statusText:s.statusText,url:rp(s)??n.urlWithParams}));return}}l===0&&(l=f?cp:0),l>=200&&l<300?(o.next(new jn({body:f,headers:a,status:l,statusText:c,url:u})),o.complete()):o.error(new ct({error:f,headers:a,status:l,statusText:c,url:u}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(oI,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has($i)||(r[$i]=ap),!n.headers.has(Pr)){let i=n.detectContentTypeHeader();i!==null&&(r[Pr]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),cu=class{};function uu(){}function iI(e){e.then(uu,uu)}function dp(e,t){return t(e)}function sI(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function aI(e,t,n){return(r,o)=>he(n,()=>t(r,i=>e(i,o)))}var fp=new D(""),fu=new D(""),hp=new D(""),hu=new D("",{providedIn:"root",factory:()=>!0});function cI(){let e=null;return(t,n)=>{e===null&&(e=(p(fp,{optional:!0})??[]).reduceRight(sI,dp));let r=p(st);if(p(hu)){let i=r.add();return e(t,n).pipe(ht(()=>r.remove(i)))}else return e(t,n)}}var Hi=(()=>{class e extends Pn{backend;injector;chain=null;pendingTasks=p(st);contributeToStability=p(hu);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(fu),...this.injector.get(hp,[])]));this.chain=r.reduceRight((o,i)=>aI(o,i,this.injector),dp)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(ht(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(I(Fn),I(te))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();var uI=/^\)\]\}',?\n/,lI=RegExp(`^${du}:`,"m");function dI(e){return"responseURL"in e&&e.responseURL?e.responseURL:lI.test(e.getAllResponseHeaders())?e.getResponseHeader(du):null}var lu=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new v(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?U(r.\u0275loadImpl()):C(null)).pipe(ue(()=>new k(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((m,w)=>s.setRequestHeader(m,w.join(","))),n.headers.has($i)||s.setRequestHeader($i,ap),!n.headers.has(Pr)){let m=n.detectContentTypeHeader();m!==null&&s.setRequestHeader(Pr,m)}if(n.responseType){let m=n.responseType.toLowerCase();s.responseType=m!=="json"?m:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let m=s.statusText||"OK",w=new He(s.getAllResponseHeaders()),S=dI(s)||n.url;return c=new Fr({headers:w,status:s.status,statusText:m,url:S}),c},l=()=>{let{headers:m,status:w,statusText:S,url:se}=u(),H=null;w!==rI&&(H=typeof s.response>"u"?s.responseText:s.response),w===0&&(w=H?cp:0);let Jt=w>=200&&w<300;if(n.responseType==="json"&&typeof H=="string"){let Es=H;H=H.replace(uI,"");try{H=H!==""?JSON.parse(H):null}catch(Tt){H=Es,Jt&&(Jt=!1,H={error:Tt,text:H})}}Jt?(i.next(new jn({body:H,headers:m,status:w,statusText:S,url:se||void 0})),i.complete()):i.error(new ct({error:H,headers:m,status:w,statusText:S,url:se||void 0}))},f=m=>{let{url:w}=u(),S=new ct({error:m,status:s.status||0,statusText:s.statusText||"Unknown Error",url:w||void 0});i.error(S)},h=!1,d=m=>{h||(i.next(u()),h=!0);let w={type:lt.DownloadProgress,loaded:m.loaded};m.lengthComputable&&(w.total=m.total),n.responseType==="text"&&s.responseText&&(w.partialText=s.responseText),i.next(w)},g=m=>{let w={type:lt.UploadProgress,loaded:m.loaded};m.lengthComputable&&(w.total=m.total),i.next(w)};return s.addEventListener("load",l),s.addEventListener("error",f),s.addEventListener("timeout",f),s.addEventListener("abort",f),n.reportProgress&&(s.addEventListener("progress",d),a!==null&&s.upload&&s.upload.addEventListener("progress",g)),s.send(a),i.next({type:lt.Sent}),()=>{s.removeEventListener("error",f),s.removeEventListener("abort",f),s.removeEventListener("load",l),s.removeEventListener("timeout",f),n.reportProgress&&(s.removeEventListener("progress",d),a!==null&&s.upload&&s.upload.removeEventListener("progress",g)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(I(zt))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),pp=new D(""),fI="XSRF-TOKEN",hI=new D("",{providedIn:"root",factory:()=>fI}),pI="X-XSRF-TOKEN",gI=new D("",{providedIn:"root",factory:()=>pI}),Lr=class{},mI=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Rr(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(I(W),I(hI))};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function yI(e,t){let n=e.url.toLowerCase();if(!p(pp)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=p(Lr).getToken(),o=p(gI);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var zi=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(zi||{});function gp(e,t){return{\u0275kind:e,\u0275providers:t}}function mp(...e){let t=[up,lu,Hi,{provide:Pn,useExisting:Hi},{provide:Fn,useFactory:()=>p(lp,{optional:!0})??p(lu)},{provide:fu,useValue:yI,multi:!0},{provide:pp,useValue:!0},{provide:Lr,useClass:mI}];for(let n of e)t.push(...n.\u0275providers);return Sn(t)}var op=new D("");function yp(){return gp(zi.LegacyInterceptors,[{provide:op,useFactory:cI},{provide:fu,useExisting:op,multi:!0}])}function vI(){return gp(zi.Fetch,[Vi,{provide:lp,useExisting:Vi},{provide:Fn,useExisting:Vi}])}var DI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=vt({type:e});static \u0275inj=mt({providers:[mp(yp())]})}return e})();var vp=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(I(W))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var T="primary",Qr=Symbol("RouteTitle"),vu=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Wt(e){return new vu(e)}function Tp(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function wI(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!ze(e[n],t[n]))return!1;return!0}function ze(e,t){let n=e?Du(e):void 0,r=t?Du(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!Sp(e[o],t[o]))return!1;return!0}function Du(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Sp(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function _p(e){return e.length>0?e[e.length-1]:null}function Mt(e){return Ys(e)?e:Mr(e)?U(Promise.resolve(e)):C(e)}var II={exact:Rp,subset:xp},Np={exact:CI,subset:bI,ignored:()=>!0};function Dp(e,t,n){return II[n.paths](e.root,t.root,n.matrixParams)&&Np[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function CI(e,t){return ze(e,t)}function Rp(e,t,n){if(!qt(e.segments,t.segments)||!Wi(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Rp(e.children[r],t.children[r],n))return!1;return!0}function bI(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Sp(e[n],t[n]))}function xp(e,t,n){return Ap(e,t,t.segments,n)}function Ap(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!qt(o,n)||t.hasChildren()||!Wi(o,n,r))}else if(e.segments.length===n.length){if(!qt(e.segments,n)||!Wi(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!xp(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!qt(e.segments,o)||!Wi(e.segments,o,r)||!e.children[T]?!1:Ap(e.children[T],t,i,r)}}function Wi(e,t,n){return t.every((r,o)=>Np[n](e[o].parameters,r.parameters))}var Ge=class{root;queryParams;fragment;_queryParamMap;constructor(t=new F([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Wt(this.queryParams),this._queryParamMap}toString(){return SI.serialize(this)}},F=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Zi(this)}},wt=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Wt(this.parameters),this._parameterMap}toString(){return kp(this)}};function MI(e,t){return qt(e,t)&&e.every((n,r)=>ze(n.parameters,t[r].parameters))}function qt(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function TI(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===T&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==T&&(n=n.concat(t(o,r)))}),n}var Zt=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>new It,providedIn:"root"})}return e})(),It=class{parse(t){let n=new wu(t);return new Ge(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${jr(t.root,!0)}`,r=RI(t.queryParams),o=typeof t.fragment=="string"?`#${_I(t.fragment)}`:"";return`${n}${r}${o}`}},SI=new It;function Zi(e){return e.segments.map(t=>kp(t)).join("/")}function jr(e,t){if(!e.hasChildren())return Zi(e);if(t){let n=e.children[T]?jr(e.children[T],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==T&&r.push(`${o}:${jr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=TI(e,(r,o)=>o===T?[jr(e.children[T],!1)]:[`${o}:${jr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[T]!=null?`${Zi(e)}/${n[0]}`:`${Zi(e)}/(${n.join("//")})`}}function Op(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function qi(e){return Op(e).replace(/%3B/gi,";")}function _I(e){return encodeURI(e)}function Eu(e){return Op(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Yi(e){return decodeURIComponent(e)}function Ep(e){return Yi(e.replace(/\+/g,"%20"))}function kp(e){return`${Eu(e.path)}${NI(e.parameters)}`}function NI(e){return Object.entries(e).map(([t,n])=>`;${Eu(t)}=${Eu(n)}`).join("")}function RI(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${qi(n)}=${qi(o)}`).join("&"):`${qi(n)}=${qi(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var xI=/^[^\/()?;#]+/;function pu(e){let t=e.match(xI);return t?t[0]:""}var AI=/^[^\/()?;=#]+/;function OI(e){let t=e.match(AI);return t?t[0]:""}var kI=/^[^=?&#]+/;function PI(e){let t=e.match(kI);return t?t[0]:""}var FI=/^[^&#]+/;function LI(e){let t=e.match(FI);return t?t[0]:""}var wu=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new F([],{}):new F([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[T]=new F(t,n)),r}parseSegment(){let t=pu(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new v(4009,!1);return this.capture(t),new wt(Yi(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=OI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=pu(this.remaining);o&&(r=o,this.capture(r))}t[Yi(n)]=Yi(r)}parseQueryParam(t){let n=PI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=LI(this.remaining);s&&(r=s,this.capture(r))}let o=Ep(n),i=Ep(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=pu(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new v(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=T);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[T]:new F([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new v(4011,!1)}};function Pp(e){return e.segments.length>0?new F([],{[T]:e}):e}function Fp(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Fp(o);if(r===T&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new F(e.segments,t);return jI(n)}function jI(e){if(e.numberOfChildren===1&&e.children[T]){let t=e.children[T];return new F(e.segments.concat(t.segments),t.children)}return e}function Ct(e){return e instanceof Ge}function Lp(e,t,n=null,r=null){let o=jp(e);return Vp(o,t,n,r)}function jp(e){let t;function n(i){let s={};for(let c of i.children){let u=n(c);s[c.outlet]=u}let a=new F(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Pp(r);return t??o}function Vp(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return gu(o,o,o,n,r);let i=VI(t);if(i.toRoot())return gu(o,o,new F([],{}),n,r);let s=BI(i,o,e),a=s.processChildren?Br(s.segmentGroup,s.index,i.commands):Up(s.segmentGroup,s.index,i.commands);return gu(o,s.segmentGroup,a,n,r)}function Ki(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function $r(e){return typeof e=="object"&&e!=null&&e.outlets}function gu(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===t?s=n:s=Bp(e,t,n);let a=Pp(Fp(s));return new Ge(a,i,o)}function Bp(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Bp(i,t,n)}),new F(e.segments,r)}var Ji=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Ki(r[0]))throw new v(4003,!1);let o=r.find($r);if(o&&o!==_p(r))throw new v(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function VI(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Ji(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Ji(n,t,r)}var Un=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function BI(e,t,n){if(e.isAbsolute)return new Un(t,!0,0);if(!n)return new Un(t,!1,NaN);if(n.parent===null)return new Un(n,!0,0);let r=Ki(e.commands[0])?0:1,o=n.segments.length-1+r;return UI(n,o,e.numberOfDoubleDots)}function UI(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new v(4005,!1);o=r.segments.length}return new Un(r,!1,o-i)}function $I(e){return $r(e[0])?e[0].outlets:{[T]:e}}function Up(e,t,n){if(e??=new F([],{}),e.segments.length===0&&e.hasChildren())return Br(e,t,n);let r=HI(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new F(e.segments.slice(0,r.pathIndex),{});return i.children[T]=new F(e.segments.slice(r.pathIndex),e.children),Br(i,0,o)}else return r.match&&o.length===0?new F(e.segments,{}):r.match&&!e.hasChildren()?Iu(e,t,n):r.match?Br(e,0,o):Iu(e,t,n)}function Br(e,t,n){if(n.length===0)return new F(e.segments,{});{let r=$I(n),o={};if(Object.keys(r).some(i=>i!==T)&&e.children[T]&&e.numberOfChildren===1&&e.children[T].segments.length===0){let i=Br(e.children[T],t,n);return new F(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Up(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new F(e.segments,o)}}function HI(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if($r(a))break;let c=`${a}`,u=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Ip(c,u,s))return i;r+=2}else{if(!Ip(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Iu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if($r(i)){let c=zI(i.outlets);return new F(r,c)}if(o===0&&Ki(n[0])){let c=e.segments[t];r.push(new wt(c.path,wp(n[0]))),o++;continue}let s=$r(i)?i.outlets[T]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Ki(a)?(r.push(new wt(s,wp(a))),o+=2):(r.push(new wt(s,{})),o++)}return new F(r,{})}function zI(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Iu(new F([],{}),0,r))}),t}function wp(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Ip(e,t,n){return e==n.path&&ze(t,n.parameters)}var Qi="imperative",Y=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Y||{}),Ee=class{id;url;constructor(t,n){this.id=t,this.url=n}},bt=class extends Ee{type=Y.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},be=class extends Ee{urlAfterRedirects;type=Y.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},pe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(pe||{}),Hn=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Hn||{}),qe=class extends Ee{reason;code;type=Y.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},We=class extends Ee{reason;code;type=Y.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},zn=class extends Ee{error;target;type=Y.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Hr=class extends Ee{urlAfterRedirects;state;type=Y.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xi=class extends Ee{urlAfterRedirects;state;type=Y.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},es=class extends Ee{urlAfterRedirects;state;shouldActivate;type=Y.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ts=class extends Ee{urlAfterRedirects;state;type=Y.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ns=class extends Ee{urlAfterRedirects;state;type=Y.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},rs=class{route;type=Y.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},os=class{route;type=Y.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},is=class{snapshot;type=Y.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ss=class{snapshot;type=Y.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},as=class{snapshot;type=Y.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},cs=class{snapshot;type=Y.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},qn=class{routerEvent;position;anchor;type=Y.Scroll;constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},zr=class{},Gn=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function qI(e,t){return e.providers&&!e._injector&&(e._injector=br(e.providers,t,`Route: ${e.path}`)),e._injector??t}function ke(e){return e.outlet||T}function GI(e,t){let n=e.filter(r=>ke(r)===t);return n.push(...e.filter(r=>ke(r)!==t)),n}function Kr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var us=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Kr(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Yt(this.rootInjector)}},Yt=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new us(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(I(te))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ls=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Cu(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Cu(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=bu(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return bu(t,this._root).map(n=>n.value)}};function Cu(e,t){if(e===t.value)return t;for(let n of t.children){let r=Cu(e,n);if(r)return r}return null}function bu(e,t){if(e===t.value)return[t];for(let n of t.children){let r=bu(e,n);if(r.length)return r.unshift(t),r}return[]}var De=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Bn(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var qr=class extends ls{snapshot;constructor(t,n){super(t),this.snapshot=n,Au(this,t)}toString(){return this.snapshot.toString()}};function $p(e){let t=WI(e),n=new J([new wt("",{})]),r=new J({}),o=new J({}),i=new J({}),s=new J(""),a=new Ze(n,r,i,s,o,T,e,t.root);return a.snapshot=t.root,new qr(new De(a,[]),t)}function WI(e){let t={},n={},r={},o="",i=new Gt([],t,r,o,n,T,e,null,{});return new Gr("",new De(i,[]))}var Ze=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(R(u=>u[Qr]))??C(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(R(t=>Wt(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(R(t=>Wt(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ds(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:y(y({},t.params),e.params),data:y(y({},t.data),e.data),resolve:y(y(y(y({},e.data),t.data),o?.data),e._resolvedData)}:r={params:y({},e.params),data:y({},e.data),resolve:y(y({},e.data),e._resolvedData??{})},o&&zp(o)&&(r.resolve[Qr]=o.title),r}var Gt=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Qr]}constructor(t,n,r,o,i,s,a,c,u){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Wt(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Wt(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Gr=class extends ls{url;constructor(t,n){super(n),this.url=t,Au(this,n)}toString(){return Hp(this._root)}};function Au(e,t){t.value._routerState=e,t.children.forEach(n=>Au(e,n))}function Hp(e){let t=e.children.length>0?` { ${e.children.map(Hp).join(", ")} } `:"";return`${e.value}${t}`}function mu(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,ze(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),ze(t.params,n.params)||e.paramsSubject.next(n.params),wI(t.url,n.url)||e.urlSubject.next(n.url),ze(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Mu(e,t){let n=ze(e.params,t.params)&&MI(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Mu(e.parent,t.parent))}function zp(e){return typeof e.title=="string"||e.title===null}var qp=new D(""),Ou=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=T;activateEvents=new de;deactivateEvents=new de;attachEvents=new de;detachEvents=new de;routerOutletData=bf(void 0);parentContexts=p(Yt);location=p(Cr);changeDetector=p(Tr);inputBinder=p(Jr,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new v(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new v(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new v(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new v(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Tu(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=On({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[vr]})}return e})(),Tu=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Ze?this.route:t===Yt?this.childContexts:t===qp?this.outletData:this.parent.get(t,n)}},Jr=new D(""),ku=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=or([r.queryParams,r.params,r.data]).pipe(ue(([i,s,a],c)=>(a=y(y(y({},i),s),a),c===0?C(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Fh(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})(),Pu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=vh({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Gc(0,"router-outlet")},dependencies:[Ou],encapsulation:2})}return e})();function Fu(e){let t=e.children&&e.children.map(Fu),n=t?B(y({},e),{children:t}):y({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==T&&(n.component=Pu),n}function ZI(e,t,n){let r=Wr(e,t._root,n?n._root:void 0);return new qr(r,t)}function Wr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=YI(e,t,n);return new De(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Wr(e,a)),s}}let r=QI(t.value),o=t.children.map(i=>Wr(e,i));return new De(r,o)}}function YI(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Wr(e,r,o);return Wr(e,r)})}function QI(e){return new Ze(new J(e.url),new J(e.params),new J(e.queryParams),new J(e.fragment),new J(e.data),e.outlet,e.component,e)}var Wn=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Gp="ngNavigationCancelingError";function fs(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=Ct(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Wp(!1,pe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Wp(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Gp]=!0,n.cancellationCode=t,n}function KI(e){return Zp(e)&&Ct(e.url)}function Zp(e){return!!e&&e[Gp]}var JI=(e,t,n,r)=>R(o=>(new Su(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Su=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),mu(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Bn(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Bn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Bn(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Bn(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new cs(i.value.snapshot))}),t.children.length&&this.forwardEvent(new ss(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(mu(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),mu(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},hs=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},$n=class{component;route;constructor(t,n){this.component=t,this.route=n}};function XI(e,t,n){let r=e._root,o=t?t._root:null;return Vr(r,o,n,[r.value])}function eC(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Yn(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!yd(e)?e:t.get(e):r}function Vr(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Bn(t);return e.children.forEach(s=>{tC(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Ur(a,n.getContext(s),o)),o}function tC(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=nC(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new hs(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Vr(e,t,a?a.children:null,r,o):Vr(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new $n(a.outlet.component,s))}else s&&Ur(t,a,o),o.canActivateChecks.push(new hs(r)),i.component?Vr(e,null,a?a.children:null,r,o):Vr(e,null,n,r,o);return o}function nC(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!qt(e.url,t.url);case"pathParamsOrQueryParamsChange":return!qt(e.url,t.url)||!ze(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Mu(e,t)||!ze(e.queryParams,t.queryParams);case"paramsChange":default:return!Mu(e,t)}}function Ur(e,t,n){let r=Bn(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Ur(s,t.children.getContext(i),n):Ur(s,null,n):Ur(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new $n(t.outlet.component,o)):n.canDeactivateChecks.push(new $n(null,o)):n.canDeactivateChecks.push(new $n(null,o))}function Xr(e){return typeof e=="function"}function rC(e){return typeof e=="boolean"}function oC(e){return e&&Xr(e.canLoad)}function iC(e){return e&&Xr(e.canActivate)}function sC(e){return e&&Xr(e.canActivateChild)}function aC(e){return e&&Xr(e.canDeactivate)}function cC(e){return e&&Xr(e.canMatch)}function Yp(e){return e instanceof Ke||e?.name==="EmptyError"}var Gi=Symbol("INITIAL_VALUE");function Zn(){return ue(e=>or(e.map(t=>t.pipe(Xe(1),Js(Gi)))).pipe(R(t=>{for(let n of t)if(n!==!0){if(n===Gi)return Gi;if(n===!1||uC(n))return n}return!0}),ne(t=>t!==Gi),Xe(1)))}function uC(e){return Ct(e)||e instanceof Wn}function lC(e,t){return z(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?C(B(y({},n),{guardsResult:!0})):dC(s,r,o,e).pipe(z(a=>a&&rC(a)?fC(r,i,e,t):C(a)),R(a=>B(y({},n),{guardsResult:a})))})}function dC(e,t,n,r){return U(e).pipe(z(o=>yC(o.component,o.route,n,t,r)),et(o=>o!==!0,!0))}function fC(e,t,n,r){return U(t).pipe(Pe(o=>un(pC(o.route.parent,r),hC(o.route,r),mC(e,o.path,n),gC(e,o.route,n))),et(o=>o!==!0,!0))}function hC(e,t){return e!==null&&t&&t(new as(e)),C(!0)}function pC(e,t){return e!==null&&t&&t(new is(e)),C(!0)}function gC(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return C(!0);let o=r.map(i=>xo(()=>{let s=Kr(t)??n,a=Yn(i,s),c=iC(a)?a.canActivate(t,e):he(s,()=>a(t,e));return Mt(c).pipe(et())}));return C(o).pipe(Zn())}function mC(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>eC(s)).filter(s=>s!==null).map(s=>xo(()=>{let a=s.guards.map(c=>{let u=Kr(s.node)??n,l=Yn(c,u),f=sC(l)?l.canActivateChild(r,e):he(u,()=>l(r,e));return Mt(f).pipe(et())});return C(a).pipe(Zn())}));return C(i).pipe(Zn())}function yC(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return C(!0);let s=i.map(a=>{let c=Kr(t)??o,u=Yn(a,c),l=aC(u)?u.canDeactivate(e,t,n,r):he(c,()=>u(e,t,n,r));return Mt(l).pipe(et())});return C(s).pipe(Zn())}function vC(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return C(!0);let i=o.map(s=>{let a=Yn(s,e),c=oC(a)?a.canLoad(t,n):he(e,()=>a(t,n));return Mt(c)});return C(i).pipe(Zn(),Qp(r))}function Qp(e){return qs(K(t=>{if(typeof t!="boolean")throw fs(e,t)}),R(t=>t===!0))}function DC(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return C(!0);let i=o.map(s=>{let a=Yn(s,e),c=cC(a)?a.canMatch(t,n):he(e,()=>a(t,n));return Mt(c)});return C(i).pipe(Zn(),Qp(r))}var Zr=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Yr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Vn(e){return an(new Zr(e))}function EC(e){return an(new v(4e3,!1))}function wC(e){return an(Wp(!1,pe.GuardRejected))}var _u=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return C(r);if(o.numberOfChildren>1||!o.children[T])return EC(`${t.redirectTo}`);o=o.children[T]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,params:d,data:g,title:m}=o,w=he(i,()=>a({params:d,data:g,queryParams:c,fragment:u,routeConfig:l,url:f,outlet:h,title:m}));if(w instanceof Ge)throw new Yr(w);n=w}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Yr(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Ge(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new F(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new v(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Nu={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function IC(e,t,n,r,o){let i=Kp(e,t,n);return i.matched?(r=qI(t,r),DC(r,t,n,o).pipe(R(s=>s===!0?i:y({},Nu)))):C(i)}function Kp(e,t,n){if(t.path==="**")return CC(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?y({},Nu):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||Tp)(n,e,t);if(!o)return y({},Nu);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?y(y({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function CC(e){return{matched:!0,parameters:e.length>0?_p(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Cp(e,t,n,r){return n.length>0&&TC(e,n,r)?{segmentGroup:new F(t,MC(r,new F(n,e.children))),slicedSegments:[]}:n.length===0&&SC(e,n,r)?{segmentGroup:new F(e.segments,bC(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new F(e.segments,e.children),slicedSegments:n}}function bC(e,t,n,r){let o={};for(let i of n)if(gs(e,t,i)&&!r[ke(i)]){let s=new F([],{});o[ke(i)]=s}return y(y({},r),o)}function MC(e,t){let n={};n[T]=t;for(let r of e)if(r.path===""&&ke(r)!==T){let o=new F([],{});n[ke(r)]=o}return n}function TC(e,t,n){return n.some(r=>gs(e,t,r)&&ke(r)!==T)}function SC(e,t,n){return n.some(r=>gs(e,t,r))}function gs(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function _C(e,t,n){return t.length===0&&!e.children[n]}var Ru=class{};function NC(e,t,n,r,o,i,s="emptyOnly"){return new xu(e,t,n,r,o,s,i).recognize()}var RC=31,xu=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new _u(this.urlSerializer,this.urlTree)}noMatchError(t){return new v(4002,`'${t.segmentGroup}'`)}recognize(){let t=Cp(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(R(({children:n,rootSnapshot:r})=>{let o=new De(r,n),i=new Gr("",o),s=Lp(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Gt([],Object.freeze({}),Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),T,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,T,n).pipe(R(r=>({children:r,rootSnapshot:n})),Je(r=>{if(r instanceof Yr)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Zr?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(R(s=>s instanceof De?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return U(i).pipe(Pe(s=>{let a=r.children[s],c=GI(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Ks((s,a)=>(s.push(...a),s)),ft(null),Qs(),z(s=>{if(s===null)return Vn(r);let a=Jp(s);return xC(a),C(a)}))}processSegment(t,n,r,o,i,s,a){return U(n).pipe(Pe(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(Je(u=>{if(u instanceof Zr)return C(null);throw u}))),et(c=>!!c),Je(c=>{if(Yp(c))return _C(r,o,i)?C(new Ru):Vn(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return ke(r)!==s&&(s===T||!gs(o,i,r))?Vn(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Vn(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:f,remainingSegments:h}=Kp(n,o,i);if(!c)return Vn(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>RC&&(this.allowRedirects=!1));let d=new Gt(i,u,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,bp(o),ke(o),o.component??o._loadedComponent??null,o,Mp(o)),g=ds(d,a,this.paramsInheritanceStrategy);d.params=Object.freeze(g.params),d.data=Object.freeze(g.data);let m=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,f,d,t);return this.applyRedirects.lineralizeSegments(o,m).pipe(z(w=>this.processSegment(t,r,n,w.concat(h),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=IC(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(ue(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(ue(({routes:u})=>{let l=r._loadedInjector??t,{parameters:f,consumedSegments:h,remainingSegments:d}=c,g=new Gt(h,f,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,bp(r),ke(r),r.component??r._loadedComponent??null,r,Mp(r)),m=ds(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(m.params),g.data=Object.freeze(m.data);let{segmentGroup:w,slicedSegments:S}=Cp(n,h,d,u);if(S.length===0&&w.hasChildren())return this.processChildren(l,u,w,g).pipe(R(H=>new De(g,H)));if(u.length===0&&S.length===0)return C(new De(g,[]));let se=ke(r)===i;return this.processSegment(l,u,w,S,se?T:i,!0,g).pipe(R(H=>new De(g,H instanceof De?[H]:[])))}))):Vn(n)))}getChildConfig(t,n,r){return n.children?C({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?C({routes:n._loadedRoutes,injector:n._loadedInjector}):vC(t,n,r,this.urlSerializer).pipe(z(o=>o?this.configLoader.loadChildren(t,n).pipe(K(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):wC(n))):C({routes:[],injector:t})}};function xC(e){e.sort((t,n)=>t.value.outlet===T?-1:n.value.outlet===T?1:t.value.outlet.localeCompare(n.value.outlet))}function AC(e){let t=e.value.routeConfig;return t&&t.path===""}function Jp(e){let t=[],n=new Set;for(let r of e){if(!AC(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Jp(r.children);t.push(new De(r.value,o))}return t.filter(r=>!n.has(r))}function bp(e){return e.data||{}}function Mp(e){return e.resolve||{}}function OC(e,t,n,r,o,i){return z(s=>NC(e,t,n,r,s.extractedUrl,o,i).pipe(R(({state:a,tree:c})=>B(y({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function kC(e,t){return z(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return C(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of Xp(c))s.add(u);let a=0;return U(s).pipe(Pe(c=>i.has(c)?PC(c,r,e,t):(c.data=ds(c,c.parent,e).resolve,C(void 0))),K(()=>a++),ln(1),z(c=>a===s.size?C(n):ae))})}function Xp(e){let t=e.children.map(n=>Xp(n)).flat();return[e,...t]}function PC(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!zp(o)&&(i[Qr]=o.title),FC(i,e,t,r).pipe(R(s=>(e._resolvedData=s,e.data=ds(e,e.parent,n).resolve,null)))}function FC(e,t,n,r){let o=Du(e);if(o.length===0)return C({});let i={};return U(o).pipe(z(s=>LC(e[s],t,n,r).pipe(et(),K(a=>{if(a instanceof Wn)throw fs(new It,a);i[s]=a}))),ln(1),R(()=>i),Je(s=>Yp(s)?ae:an(s)))}function LC(e,t,n,r){let o=Kr(t)??r,i=Yn(e,o),s=i.resolve?i.resolve(t,n):he(o,()=>i(t,n));return Mt(s)}function yu(e){return ue(t=>{let n=e(t);return n?U(n).pipe(R(()=>t)):C(t)})}var Lu=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===T);return r}getResolvedTitleForRoute(n){return n.data[Qr]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(eg),providedIn:"root"})}return e})(),eg=(()=>{class e extends Lu{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(vp))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Qt=new D("",{providedIn:"root",factory:()=>({})}),Kt=new D(""),ms=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Ah);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return C(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Mt(n.loadComponent()).pipe(R(ng),K(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),ht(()=>{this.componentLoaders.delete(n)})),o=new sn(r,()=>new Q).pipe(on());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return C({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=tg(r,this.compiler,n,this.onLoadEndListener).pipe(ht(()=>{this.childrenLoaders.delete(r)})),s=new sn(i,()=>new Q).pipe(on());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function tg(e,t,n,r){return Mt(e.loadChildren()).pipe(R(ng),z(o=>o instanceof $c||Array.isArray(o)?C(o):U(t.compileModuleAsync(o))),R(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Kt,[],{optional:!0,self:!0}).flat()),{routes:s.map(Fu),injector:i}}))}function jC(e){return e&&typeof e=="object"&&"default"in e}function ng(e){return jC(e)?e.default:e}var ys=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(VC),providedIn:"root"})}return e})(),VC=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ju=new D(""),Vu=new D("");function rg(e,t,n){let r=e.get(Vu),o=e.get(W);return e.get($).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(u=>setTimeout(u));let i,s=new Promise(u=>{i=u}),a=o.startViewTransition(()=>(i(),BC(e))),{onViewTransitionCreated:c}=r;return c&&he(e,()=>c({transition:a,from:t,to:n})),s})}function BC(e){return new Promise(t=>{bc({read:()=>setTimeout(t)},{injector:e})})}var Bu=new D(""),vs=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new Q;transitionAbortSubject=new Q;configLoader=p(ms);environmentInjector=p(te);destroyRef=p($t);urlSerializer=p(Zt);rootContexts=p(Yt);location=p(Et);inputBindingEnabled=p(Jr,{optional:!0})!==null;titleStrategy=p(Lu);options=p(Qt,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(ys);createViewTransition=p(ju,{optional:!0});navigationErrorHandler=p(Bu,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>C(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new rs(o)),r=o=>this.events.next(new os(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(B(y({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(n){return this.transitions=new J(null),this.transitions.pipe(ne(r=>r!==null),ue(r=>{let o=!1,i=!1;return C(r).pipe(ue(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",pe.SupersededByNewNavigation),ae;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?B(y({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!a&&c!=="reload"){let u="";return this.events.next(new We(s.id,this.urlSerializer.serialize(s.rawUrl),u,Hn.IgnoredSameUrlNavigation)),s.resolve(!1),ae}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return C(s).pipe(ue(u=>(this.events.next(new bt(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?ae:Promise.resolve(u))),OC(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),K(u=>{r.targetSnapshot=u.targetSnapshot,r.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=B(y({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let l=new Hr(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:u,extractedUrl:l,source:f,restoredState:h,extras:d}=s,g=new bt(u,this.urlSerializer.serialize(l),f,h);this.events.next(g);let m=$p(this.rootComponentType).snapshot;return this.currentTransition=r=B(y({},s),{targetSnapshot:m,urlAfterRedirects:l,extras:B(y({},d),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,C(r)}else{let u="";return this.events.next(new We(s.id,this.urlSerializer.serialize(s.extractedUrl),u,Hn.IgnoredByUrlHandlingStrategy)),s.resolve(!1),ae}}),K(s=>{let a=new Xi(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),R(s=>(this.currentTransition=r=B(y({},s),{guards:XI(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),lC(this.environmentInjector,s=>this.events.next(s)),K(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw fs(this.urlSerializer,s.guardsResult);let a=new es(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),ne(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",pe.GuardRejected),!1)),yu(s=>{if(s.guards.canActivateChecks.length!==0)return C(s).pipe(K(a=>{let c=new ts(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),ue(a=>{let c=!1;return C(a).pipe(kC(this.paramsInheritanceStrategy,this.environmentInjector),K({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",pe.NoDataFromResolver)}}))}),K(a=>{let c=new ns(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),yu(s=>{let a=c=>{let u=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(c.routeConfig).pipe(K(l=>{c.component=l}),R(()=>{})));for(let l of c.children)u.push(...a(l));return u};return or(a(s.targetSnapshot.root)).pipe(ft(null),Xe(1))}),yu(()=>this.afterPreactivation()),ue(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?U(c).pipe(R(()=>r)):C(r)}),R(s=>{let a=ZI(n.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=B(y({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),K(()=>{this.events.next(new zr)}),JI(this.rootContexts,n.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),Xe(1),K({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new be(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),Xs(this.transitionAbortSubject.pipe(K(s=>{throw s}))),ht(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",pe.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),Je(s=>{if(this.destroyed)return r.resolve(!1),ae;if(i=!0,Zp(s))this.events.next(new qe(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),KI(s)?this.events.next(new Gn(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new zn(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=he(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof Wn){let{message:u,cancellationCode:l}=fs(this.urlSerializer,c);this.events.next(new qe(r.id,this.urlSerializer.serialize(r.extractedUrl),u,l)),this.events.next(new Gn(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return ae}))}))}cancelNavigationTransition(n,r,o){let i=new qe(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function UC(e){return e!==Qi}var og=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p($C),providedIn:"root"})}return e})(),ps=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},$C=(()=>{class e extends ps{static \u0275fac=(()=>{let n;return function(o){return(n||(n=gc(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ig=(()=>{class e{urlSerializer=p(Zt);options=p(Qt,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Et);urlHandlingStrategy=p(ys);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Ge;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Ge?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=$p(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:()=>p(HC),providedIn:"root"})}return e})(),HC=(()=>{class e extends ig{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof bt?this.updateStateMemento():n instanceof We?this.commitTransition(r):n instanceof Hr?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof zr?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof qe&&(n.code===pe.GuardRejected||n.code===pe.NoDataFromResolver)?this.restoreHistory(r):n instanceof zn?this.restoreHistory(r,!0):n instanceof be&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=y(y({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=y(y({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=gc(e)))(o||e)}})();static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ds(e,t){e.events.pipe(ne(n=>n instanceof be||n instanceof qe||n instanceof zn||n instanceof We),R(n=>n instanceof be||n instanceof We?0:(n instanceof qe?n.code===pe.Redirect||n.code===pe.SupersededByNewNavigation:!1)?2:1),ne(n=>n!==2),Xe(1)).subscribe(()=>{t()})}var zC={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},qC={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ye=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Ch);stateManager=p(ig);options=p(Qt,{optional:!0})||{};pendingTasks=p(st);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(vs);urlSerializer=p(Zt);location=p(Et);urlHandlingStrategy=p(ys);_events=new Q;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(og);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Kt,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Jr,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new q;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof qe&&r.code!==pe.Redirect&&r.code!==pe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof be)this.navigated=!0;else if(r instanceof Gn){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=y({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||UC(o.source)},s);this.scheduleNavigation(a,Qi,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}WC(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Qi,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=y({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Fu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=y(y({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let f;try{let h=o?o.snapshot:this.routerState.snapshot.root;f=jp(h)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return Vp(f,n,l,u??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=Ct(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Qi,null,r)}navigate(n,r={skipLocationChange:!1}){return GC(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=y({},zC):r===!1?o=y({},qC):o=r,Ct(n))return Dp(this.currentUrlTree,n,o);let i=this.parseUrl(n);return Dp(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((f,h)=>{a=f,c=h});let l=this.pendingTasks.add();return Ds(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function GC(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new v(4008,!1)}function WC(e){return!(e instanceof zr)&&!(e instanceof Gn)}var sg=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new Q;constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(u=>{u instanceof be&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Ct(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:Vf(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:Ct(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(ve(Ye),ve(Ze),mc("tabindex"),ve(Si),ve(wr),ve(Oe))};static \u0275dir=On({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&Wc("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&qc("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Sr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Sr],replaceUrl:[2,"replaceUrl","replaceUrl",Sr],routerLink:"routerLink"},features:[vr]})}return e})();var eo=class{};var ag=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(ne(n=>n instanceof be),Pe(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=br(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return U(o).pipe(cn())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=C(null);let i=o.pipe(z(s=>s===null?C(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return U([i,s]).pipe(cn())}else return i})}static \u0275fac=function(r){return new(r||e)(I(Ye),I(te),I(eo),I(ms))};static \u0275prov=E({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),cg=new D(""),ZC=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof bt?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof be?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof We&&n.code===Hn.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof qn&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new qn(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){fh()};static \u0275prov=E({token:e,factory:e.\u0275fac})}return e})();function YC(e,...t){return Sn([{provide:Kt,multi:!0,useValue:e},[],{provide:Ze,useFactory:ug,deps:[Ye]},{provide:_i,multi:!0,useFactory:lg},t.map(n=>n.\u0275providers)])}function ug(e){return e.routerState.root}function to(e,t){return{\u0275kind:e,\u0275providers:t}}function lg(){let e=p(Re);return t=>{let n=e.get(Vt);if(t!==n.components[0])return;let r=e.get(Ye),o=e.get(dg);e.get($u)===1&&r.initialNavigation(),e.get(pg,null,_.Optional)?.setUpPreloading(),e.get(cg,null,_.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var dg=new D("",{factory:()=>new Q}),$u=new D("",{providedIn:"root",factory:()=>1});function fg(){let e=[{provide:$u,useValue:0},zc(()=>{let t=p(Re);return t.get(Qc,Promise.resolve()).then(()=>new Promise(r=>{let o=t.get(Ye),i=t.get(dg);Ds(o,()=>{r(!0)}),t.get(vs).afterPreactivation=()=>(r(!0),i.closed?C(void 0):i),o.initialNavigation()}))})];return to(2,e)}function hg(){let e=[zc(()=>{p(Ye).setUpLocationChangeListener()}),{provide:$u,useValue:2}];return to(3,e)}var pg=new D("");function gg(e){return to(0,[{provide:pg,useExisting:ag},{provide:eo,useExisting:e}])}function mg(){return to(8,[ku,{provide:Jr,useExisting:ku}])}function yg(e){Ht("NgRouterViewTransitions");let t=[{provide:ju,useValue:rg},{provide:Vu,useValue:y({skipNextTransition:!!e?.skipInitialTransition},e)}];return to(9,t)}var vg=[Et,{provide:Zt,useClass:It},Ye,Yt,{provide:Ze,useFactory:ug,deps:[Ye]},ms,[]],QC=(()=>{class e{constructor(){}static forRoot(n,r){return{ngModule:e,providers:[vg,[],{provide:Kt,multi:!0,useValue:n},[],r?.errorHandler?{provide:Bu,useValue:r.errorHandler}:[],{provide:Qt,useValue:r||{}},r?.useHash?JC():XC(),KC(),r?.preloadingStrategy?gg(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?eb(r):[],r?.bindToComponentInputs?mg().\u0275providers:[],r?.enableViewTransitions?yg().\u0275providers:[],tb()]}}static forChild(n){return{ngModule:e,providers:[{provide:Kt,multi:!0,useValue:n}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=vt({type:e});static \u0275inj=mt({})}return e})();function KC(){return{provide:cg,useFactory:()=>{let e=p(zh),t=p($),n=p(Qt),r=p(vs),o=p(Zt);return n.scrollOffset&&e.setOffset(n.scrollOffset),new ZC(o,r,e,t,n)}}}function JC(){return{provide:Oe,useClass:Kc}}function XC(){return{provide:Oe,useClass:xi}}function eb(e){return[e.initialNavigation==="disabled"?hg().\u0275providers:[],e.initialNavigation==="enabledBlocking"?fg().\u0275providers:[]]}var Uu=new D("");function tb(){return[{provide:Uu,useFactory:lg},{provide:_i,multi:!0,useExisting:Uu}]}export{y as a,B as b,Q as c,J as d,U as e,R as f,Jg as g,K as h,v as i,gd as j,E as k,mt as l,D as m,I as n,Mm as o,vr as p,tN as q,nN as r,gc as s,de as t,wr as u,rN as v,ov as w,oN as x,Si as y,ve as z,vh as A,vt as B,On as C,aE as D,gE as E,Mr as F,RE as G,xE as H,pN as I,gN as J,_h as K,Nh as L,Gc as M,Wc as N,mN as O,yN as P,JE as Q,Rh as R,vN as S,DN as T,EN as U,wN as V,IN as W,CN as X,Iw as Y,bN as Z,at as _,Tw as $,Nw as aa,$h as ba,qw as ca,up as da,mp as ea,vI as fa,DI as ga,Ou as ha,Ye as ia,sg as ja,YC as ka,QC as la};
