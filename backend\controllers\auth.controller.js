const authService = require('../services/auth.service');

class AuthController {
  // Login handler
  async login(req, res) {
    try {
      const { email, password } = req.body;
      
      // Validate input
      if (!email || !password) {
        return res.status(400).json({ message: 'Email and password are required' });
      }
      
      // Attempt to login
      const result = await authService.login(email, password);
      
      if (!result) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }
      
      return res.status(200).json(result);
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Register handler
  async register(req, res) {
    try {
      const { name, email, password, role } = req.body;
      
      // Validate input
      if (!name || !email || !password || !role) {
        return res.status(400).json({ message: 'All fields are required' });
      }
      
      // Attempt to register
      const result = await authService.register({ name, email, password, role });
      
      if (result.error) {
        return res.status(400).json({ message: result.error });
      }
      
      return res.status(201).json(result);
    } catch (error) {
      console.error('Registration error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }
}

module.exports = new AuthController();
