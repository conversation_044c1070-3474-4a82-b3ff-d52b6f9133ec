/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material + Custom Styles */
html, body { height: 100%; }
body { margin: 0; font-family: <PERSON>o, "Helvetica Neue", sans-serif; }

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }
}

/* Angular Material Custom Styles */

/* Fix for Material form fields - Center everything properly */
.mat-mdc-form-field {
  /* Remove the notch borders that create middle lines */
  .mdc-notched-outline__notch {
    border-right: none !important;
    border-left: none !important;
    border-top: none !important;
  }

  /* Fix the floating label position */
  .mdc-floating-label {
    top: 50% !important;
    transform: translateY(-50%) !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    pointer-events: none !important;
    line-height: 1 !important;
  }

  /* When label is floating (has content or focused) */
  .mdc-floating-label--float-above {
    top: -8px !important;
    transform: translateY(0) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
    z-index: 1 !important;
  }

  /* Ensure proper floating on focus and when has value */
  &.mat-focused .mdc-floating-label,
  &.mat-form-field-should-float .mdc-floating-label {
    top: -8px !important;
    transform: translateY(0) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
  }

  /* Center the input text */
  .mat-mdc-form-field-input-control input {
    padding: 0 12px !important;
    margin: 0 !important;
    line-height: normal !important;
    vertical-align: middle !important;
    box-sizing: border-box !important;
    text-align: left !important;
  }

  /* Center placeholder when no label is used */
  .mat-mdc-form-field-input-control input::placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
    line-height: normal !important;
    vertical-align: middle !important;
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    left: 12px !important;
    right: 12px !important;
  }
}

/* Compact form fields - Small height */
.mat-form-field-compact {
  .mat-mdc-form-field-infix {
    min-height: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
  }

  /* Fix floating label for compact size */
  .mdc-floating-label {
    top: 50% !important;
    transform: translateY(-50%) !important;
    line-height: 1 !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    pointer-events: none !important;
  }

  /* When focused or has content - label floats to top */
  .mdc-floating-label--float-above {
    top: -6px !important;
    transform: translateY(0) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
    z-index: 1 !important;
  }

  /* Ensure label floats when input has focus */
  .mat-mdc-form-field.mat-focused .mdc-floating-label,
  .mat-mdc-form-field .mdc-floating-label--float-above {
    top: -6px !important;
    transform: translateY(0) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
  }

  .mat-mdc-form-field-input-control {
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 40px !important;
    line-height: 1 !important;
    padding: 0 12px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input::placeholder {
    line-height: 1 !important;
    position: static !important;
    transform: none !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
  }
}

/* Extra compact form fields - Very small height */
.mat-form-field-mini {
  .mat-mdc-form-field-infix {
    min-height: 32px !important;
    padding: 4px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    font-size: 14px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}

/* Medium compact form fields */
.mat-form-field-medium {
  .mat-mdc-form-field-infix {
    min-height: 48px !important;
    padding: 10px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}
