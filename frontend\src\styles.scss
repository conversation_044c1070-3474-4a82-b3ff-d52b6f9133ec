/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material + Custom Styles */
html, body { height: 100%; }
body { margin: 0; font-family: <PERSON>o, "Helvetica Neue", sans-serif; }

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }
}

/* Angular Material Custom Styles */

/* Simple and effective fix for Material form fields */
.mat-mdc-form-field {
  /* Remove the notch borders that create middle lines */
  .mdc-notched-outline__notch {
    border-right: none !important;
    border-left: none !important;
    border-top: none !important;
  }

  /* Center the input text and placeholder */
  .mat-mdc-form-field-input-control input {
    padding: 0 12px !important;
    margin: 0 !important;
    line-height: 1.5 !important;
    vertical-align: middle !important;
    box-sizing: border-box !important;
  }

  /* Placeholder specific styling */
  .mat-mdc-form-field-input-control input::placeholder {
    color: rgba(0, 0, 0, 0.6) !important;
    line-height: 1.5 !important;
    vertical-align: middle !important;
  }
}

/* Compact form fields - Small height */
.mat-form-field-compact {
  .mat-mdc-form-field-infix {
    min-height: 40px !important;
    padding: 0 !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 40px !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 40px !important;
    line-height: 40px !important;
    padding: 0 12px !important;
  }

  .mat-mdc-form-field-input-control input::placeholder {
    line-height: 40px !important;
  }
}

/* Extra compact form fields - Very small height */
.mat-form-field-mini {
  .mat-mdc-form-field-infix {
    min-height: 32px !important;
    padding: 4px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    font-size: 14px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}

/* Medium compact form fields */
.mat-form-field-medium {
  .mat-mdc-form-field-infix {
    min-height: 48px !important;
    padding: 10px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}
