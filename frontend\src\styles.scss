/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material + Custom Styles */
html, body { height: 100%; }
body { margin: 0; font-family: <PERSON>o, "Helvetica Neue", sans-serif; }

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }
}

/* Angular Material Custom Styles */

/* Compact form fields - Small height */
.mat-form-field-compact {
  .mat-mdc-form-field-infix {
    min-height: 40px !important;
    padding: 8px 0 !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 40px !important;
  }

  .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,
  .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch,
  .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
    border-width: 1px !important;
  }
}

/* Extra compact form fields - Very small height */
.mat-form-field-mini {
  .mat-mdc-form-field-infix {
    min-height: 32px !important;
    padding: 4px 0 !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 32px !important;
  }

  .mat-mdc-form-field-input-control input {
    padding: 6px 12px !important;
    font-size: 14px !important;
  }
}

/* Medium compact form fields */
.mat-form-field-medium {
  .mat-mdc-form-field-infix {
    min-height: 48px !important;
    padding: 10px 0 !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 48px !important;
  }
}
