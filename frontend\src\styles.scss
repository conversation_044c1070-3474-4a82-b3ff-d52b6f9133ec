/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Angular Material + Custom Styles */
html, body { height: 100%; }
body { margin: 0; font-family: <PERSON>o, "Helvetica Neue", sans-serif; }

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200;
  }
}

/* Angular Material Custom Styles */

/* Google Login Style Material Form Fields */
.mat-mdc-form-field {
  /* Gmail style border with notch */
  .mdc-notched-outline {
    .mdc-notched-outline__leading {
      border: 1px solid #dadce0 !important;
      border-radius: 4px 0 0 4px !important;
      border-right: none !important;
    }

    .mdc-notched-outline__trailing {
      border: 1px solid #dadce0 !important;
      border-radius: 0 4px 4px 0 !important;
      border-left: none !important;
    }

    .mdc-notched-outline__notch {
      border: 1px solid #dadce0 !important;
      border-top: none !important;
      border-left: none !important;
      border-right: none !important;
      max-width: calc(100% - 28px) !important;
    }
  }

  /* When label is floating - create notch cut */
  &.mat-form-field-should-float .mdc-notched-outline__notch,
  &.mat-focused .mdc-notched-outline__notch {
    border-top: none !important;
    padding: 0 4px !important;
  }

  /* Hover effect */
  &:hover:not(.mat-focused) .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__trailing,
    .mdc-notched-outline__notch {
      border-color: #5f6368 !important;
    }
  }

  /* Focus effect - Google blue */
  &.mat-focused .mdc-notched-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__trailing,
    .mdc-notched-outline__notch {
      border-color: #1a73e8 !important;
      border-width: 2px !important;
    }
  }

  /* Label - Google style positioning */
  .mdc-floating-label {
    top: 50% !important;
    left: 16px !important;
    transform: translateY(-50%) !important;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
    pointer-events: none !important;
    color: #5f6368 !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 1 !important;
  }

  /* Floating label - Gmail style with background */
  .mdc-floating-label--float-above {
    top: 0 !important;
    left: 12px !important;
    transform: translateY(-50%) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
    color: #1a73e8 !important;
    z-index: 1 !important;
  }

  /* Input field - Google style */
  .mat-mdc-form-field-input-control input {
    padding: 13px 15px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
    color: #202124 !important;
    background: transparent !important;
    border: none !important;
    outline: none !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Hide placeholder */
  .mat-mdc-form-field-input-control input::placeholder {
    opacity: 0 !important;
  }
}

/* Compact form fields - Google style with smaller height */
.mat-form-field-compact {
  .mat-mdc-form-field-infix {
    min-height: 48px !important;
    height: 48px !important;
    padding: 0 !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 48px !important;
  }

  /* Compact label positioning */
  .mdc-floating-label {
    top: 50% !important;
    left: 16px !important;
    transform: translateY(-50%) !important;
    font-size: 16px !important;
  }

  /* Compact floating label */
  .mdc-floating-label--float-above {
    top: 0 !important;
    left: 12px !important;
    transform: translateY(-50%) scale(0.75) !important;
    background: white !important;
    padding: 0 4px !important;
    z-index: 1 !important;
  }

  /* Compact input styling */
  .mat-mdc-form-field-input-control input {
    padding: 12px 15px !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
}

/* Extra compact form fields - Very small height */
.mat-form-field-mini {
  .mat-mdc-form-field-infix {
    min-height: 32px !important;
    padding: 4px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    font-size: 14px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}

/* Medium compact form fields */
.mat-form-field-medium {
  .mat-mdc-form-field-infix {
    min-height: 48px !important;
    padding: 10px 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-text-field-wrapper {
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .mat-mdc-form-field-input-control input {
    height: 100% !important;
    padding: 0 12px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
  }
}
