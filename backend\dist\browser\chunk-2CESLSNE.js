import{a as z}from"./chunk-OBWAOF5P.js";import{a as N,b as p,c as T,d as O,e as k,f as q,g as A,h as G,i as K,j as Q,k as U,l as W,m as X,n as Y,o as Z,p as $,q as ee,r as te}from"./chunk-K2OZDCQR.js";import{Bb as P,Da as m,Eb as J,Fa as x,K as y,Ma as i,Na as r,Nb as I,Oa as u,Ob as L,Qb as B,Rb as R,Sa as F,Ta as c,bb as n,db as M,ma as l,qa as g,va as w,wa as E,za as d}from"./chunk-BKY6BYBU.js";function me(e,a){e&1&&(i(0,"mat-error"),n(1," Email is required "),r())}function se(e,a){e&1&&(i(0,"mat-error"),n(1," Please enter a valid email "),r())}function de(e,a){e&1&&(i(0,"mat-error"),n(1," Password is required "),r())}function pe(e,a){e&1&&(i(0,"mat-error"),n(1," Password must be at least 6 characters "),r())}function ue(e,a){if(e&1&&(i(0,"div",20),n(1),r()),e&2){let o=c();l(),M(" ",o.errorMessage," ")}}function ge(e,a){e&1&&u(0,"mat-spinner",21)}function ce(e,a){e&1&&(i(0,"span"),n(1,"Logging in..."),r())}function fe(e,a){e&1&&(i(0,"span"),n(1,"Login"),r())}var j=class e{constructor(a,o,t){this.fb=a;this.authService=o;this.router=t;this.loginForm=this.fb.group({email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]]})}loginForm;isLoading=!1;errorMessage="";onSubmit(){if(this.loginForm.invalid)return;this.isLoading=!0,this.errorMessage="";let{email:a,password:o}=this.loginForm.value;this.authService.login(a,o).subscribe({next:()=>{this.isLoading=!1,this.router.navigate(["/dashboard"])},error:t=>{this.isLoading=!1,this.errorMessage=t.error?.message||"Login failed. Please try again."}})}static \u0275fac=function(o){return new(o||e)(g(G),g(R),g(I))};static \u0275cmp=w({type:e,selectors:[["app-login"]],standalone:!1,decls:51,vars:10,consts:[[1,"min-h-screen","bg-gray-50","flex","items-center","justify-center","py-12","px-4","sm:px-6","lg:px-8"],[1,"max-w-md","w-full","space-y-8"],[1,"text-center"],[1,"text-3xl","font-bold","text-gray-900","mb-2"],[1,"text-gray-600"],[1,"p-8"],[1,"space-y-6",3,"ngSubmit","formGroup"],["appearance","outline",1,"w-full","mat-form-field-compact"],["matInput","","type","email","formControlName","email"],["matSuffix",""],[4,"ngIf"],["matInput","","type","password","formControlName","password"],["class","bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"w-full","h-12","text-lg","font-medium",3,"disabled"],["diameter","20","class","mr-2",4,"ngIf"],[1,"text-center","mt-6","pt-6","border-t","border-gray-200"],["routerLink","/auth/register",1,"text-blue-600","hover:text-blue-800","font-medium","ml-1"],[1,"bg-blue-50","border","border-blue-200","rounded-lg","p-4","text-sm"],[1,"font-medium","text-blue-900","mb-2"],[1,"text-blue-700","space-y-1"],[1,"bg-red-50","border","border-red-200","text-red-700","px-4","py-3","rounded-lg"],["diameter","20",1,"mr-2"]],template:function(o,t){if(o&1&&(i(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2",3),n(4,"Login to HMS2025"),r(),i(5,"p",4),n(6,"Hospital Management System"),r()(),i(7,"mat-card",5)(8,"form",6),F("ngSubmit",function(){return t.onSubmit()}),i(9,"mat-form-field",7)(10,"mat-label"),n(11,"Email Address"),r(),u(12,"input",8),i(13,"mat-icon",9),n(14,"email"),r(),d(15,me,2,0,"mat-error",10)(16,se,2,0,"mat-error",10),r(),i(17,"mat-form-field",7)(18,"mat-label"),n(19,"Password"),r(),u(20,"input",11),i(21,"mat-icon",9),n(22,"lock"),r(),d(23,de,2,0,"mat-error",10)(24,pe,2,0,"mat-error",10),r(),d(25,ue,2,1,"div",12),i(26,"button",13),d(27,ge,1,0,"mat-spinner",14)(28,ce,2,0,"span",10)(29,fe,2,0,"span",10),r()(),i(30,"div",15)(31,"p",4),n(32," Don't have an account? "),i(33,"a",16),n(34," Register here "),r()()()(),i(35,"div",17)(36,"h4",18),n(37,"Test Credentials:"),r(),i(38,"div",19)(39,"p")(40,"strong"),n(41,"Admin:"),r(),n(42," <EMAIL> / admin123"),r(),i(43,"p")(44,"strong"),n(45,"Doctor:"),r(),n(46," <EMAIL> / doctor123"),r(),i(47,"p")(48,"strong"),n(49,"Patient:"),r(),n(50," <EMAIL> / patient123"),r()()()()()),o&2){let s,f,_,v;l(8),m("formGroup",t.loginForm),l(7),m("ngIf",(s=t.loginForm.get("email"))==null?null:s.hasError("required")),l(),m("ngIf",(f=t.loginForm.get("email"))==null?null:f.hasError("email")),l(7),m("ngIf",(_=t.loginForm.get("password"))==null?null:_.hasError("required")),l(),m("ngIf",(v=t.loginForm.get("password"))==null?null:v.hasError("minlength")),l(),m("ngIf",t.errorMessage),l(),m("disabled",t.loginForm.invalid||t.isLoading),l(),m("ngIf",t.isLoading),l(),m("ngIf",t.isLoading),l(),m("ngIf",!t.isLoading)}},dependencies:[P,L,k,N,T,O,q,A,K,Y,X,Q,U,W,Z,$,ee],encapsulation:2})};function _e(e,a){e&1&&(i(0,"span"),n(1,"Name is required"),r())}function ve(e,a){e&1&&(i(0,"span"),n(1,"Name must be at least 3 characters"),r())}function xe(e,a){if(e&1&&(i(0,"div",18),d(1,_e,2,0,"span",15)(2,ve,2,0,"span",15),r()),e&2){let o,t,s=c();l(),m("ngIf",(o=s.registerForm.get("name"))==null||o.errors==null?null:o.errors.required),l(),m("ngIf",(t=s.registerForm.get("name"))==null||t.errors==null?null:t.errors.minlength)}}function he(e,a){e&1&&(i(0,"span"),n(1,"Email is required"),r())}function be(e,a){e&1&&(i(0,"span"),n(1,"Please enter a valid email"),r())}function Ce(e,a){if(e&1&&(i(0,"div",18),d(1,he,2,0,"span",15)(2,be,2,0,"span",15),r()),e&2){let o,t,s=c();l(),m("ngIf",(o=s.registerForm.get("email"))==null||o.errors==null?null:o.errors.required),l(),m("ngIf",(t=s.registerForm.get("email"))==null||t.errors==null?null:t.errors.email)}}function Se(e,a){e&1&&(i(0,"span"),n(1,"Password is required"),r())}function ye(e,a){e&1&&(i(0,"span"),n(1,"Password must be at least 6 characters"),r())}function we(e,a){if(e&1&&(i(0,"div",18),d(1,Se,2,0,"span",15)(2,ye,2,0,"span",15),r()),e&2){let o,t,s=c();l(),m("ngIf",(o=s.registerForm.get("password"))==null||o.errors==null?null:o.errors.required),l(),m("ngIf",(t=s.registerForm.get("password"))==null||t.errors==null?null:t.errors.minlength)}}function Ee(e,a){e&1&&(i(0,"span"),n(1,"Please confirm your password"),r())}function Fe(e,a){e&1&&(i(0,"span"),n(1,"Passwords do not match"),r())}function Me(e,a){if(e&1&&(i(0,"div",18),d(1,Ee,2,0,"span",15)(2,Fe,2,0,"span",15),r()),e&2){let o,t,s=c();l(),m("ngIf",(o=s.registerForm.get("confirmPassword"))==null||o.errors==null?null:o.errors.required),l(),m("ngIf",(t=s.registerForm.get("confirmPassword"))==null||t.errors==null?null:t.errors.passwordMismatch)}}function Pe(e,a){if(e&1&&(i(0,"div",19),n(1),r()),e&2){let o=c();l(),M(" ",o.errorMessage," ")}}function Ie(e,a){e&1&&(i(0,"span"),n(1,"Registering..."),r())}function Le(e,a){e&1&&(i(0,"span"),n(1,"Register"),r())}var D=class e{constructor(a,o,t){this.fb=a;this.authService=o;this.router=t;this.registerForm=this.fb.group({name:["",[p.required,p.minLength(3)]],email:["",[p.required,p.email]],password:["",[p.required,p.minLength(6)]],confirmPassword:["",[p.required]]},{validators:this.passwordMatchValidator})}registerForm;isLoading=!1;errorMessage="";passwordMatchValidator(a){let o=a.get("password")?.value,t=a.get("confirmPassword")?.value;return o!==t?(a.get("confirmPassword")?.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):null}onSubmit(){if(this.registerForm.invalid)return;this.isLoading=!0,this.errorMessage="";let{name:a,email:o,password:t}=this.registerForm.value;this.authService.register(a,o,t).subscribe({next:()=>{this.isLoading=!1,this.router.navigate(["/dashboard"])},error:s=>{this.isLoading=!1,this.errorMessage=s.error?.message||"Registration failed. Please try again."}})}static \u0275fac=function(o){return new(o||e)(g(G),g(R),g(I))};static \u0275cmp=w({type:e,selectors:[["app-register"]],standalone:!1,decls:34,vars:17,consts:[[1,"register-container"],[1,"register-card"],[3,"ngSubmit","formGroup"],[1,"form-group"],["for","name"],["type","text","id","name","formControlName","name","placeholder","Enter your full name"],["class","error-message",4,"ngIf"],["for","email"],["type","email","id","email","formControlName","email","placeholder","Enter your email"],["for","password"],["type","password","id","password","formControlName","password","placeholder","Enter your password"],["for","confirmPassword"],["type","password","id","confirmPassword","formControlName","confirmPassword","placeholder","Confirm your password"],["class","alert alert-danger",4,"ngIf"],["type","submit",1,"register-button",3,"disabled"],[4,"ngIf"],[1,"login-link"],["routerLink","/auth/login"],[1,"error-message"],[1,"alert","alert-danger"]],template:function(o,t){if(o&1&&(i(0,"div",0)(1,"div",1)(2,"h2"),n(3,"HMS2025 Registration"),r(),i(4,"form",2),F("ngSubmit",function(){return t.onSubmit()}),i(5,"div",3)(6,"label",4),n(7,"Full Name"),r(),u(8,"input",5),d(9,xe,3,2,"div",6),r(),i(10,"div",3)(11,"label",7),n(12,"Email"),r(),u(13,"input",8),d(14,Ce,3,2,"div",6),r(),i(15,"div",3)(16,"label",9),n(17,"Password"),r(),u(18,"input",10),d(19,we,3,2,"div",6),r(),i(20,"div",3)(21,"label",11),n(22,"Confirm Password"),r(),u(23,"input",12),d(24,Me,3,2,"div",6),r(),d(25,Pe,2,1,"div",13),i(26,"button",14),d(27,Ie,2,0,"span",15)(28,Le,2,0,"span",15),r(),i(29,"div",16)(30,"p"),n(31,"Already have an account? "),i(32,"a",17),n(33,"Login"),r()()()()()()),o&2){let s,f,_,v,h,b,C,S;l(4),m("formGroup",t.registerForm),l(4),x("invalid",((s=t.registerForm.get("name"))==null?null:s.invalid)&&((s=t.registerForm.get("name"))==null?null:s.touched)),l(),m("ngIf",((f=t.registerForm.get("name"))==null?null:f.invalid)&&((f=t.registerForm.get("name"))==null?null:f.touched)),l(4),x("invalid",((_=t.registerForm.get("email"))==null?null:_.invalid)&&((_=t.registerForm.get("email"))==null?null:_.touched)),l(),m("ngIf",((v=t.registerForm.get("email"))==null?null:v.invalid)&&((v=t.registerForm.get("email"))==null?null:v.touched)),l(4),x("invalid",((h=t.registerForm.get("password"))==null?null:h.invalid)&&((h=t.registerForm.get("password"))==null?null:h.touched)),l(),m("ngIf",((b=t.registerForm.get("password"))==null?null:b.invalid)&&((b=t.registerForm.get("password"))==null?null:b.touched)),l(4),x("invalid",((C=t.registerForm.get("confirmPassword"))==null?null:C.invalid)&&((C=t.registerForm.get("confirmPassword"))==null?null:C.touched)),l(),m("ngIf",((S=t.registerForm.get("confirmPassword"))==null?null:S.invalid)&&((S=t.registerForm.get("confirmPassword"))==null?null:S.touched)),l(),m("ngIf",t.errorMessage),l(),m("disabled",t.registerForm.invalid||t.isLoading),l(),m("ngIf",t.isLoading),l(),m("ngIf",!t.isLoading)}},dependencies:[P,L,k,N,T,O,q,A],styles:[".register-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background-color:#f5f5f5;padding:20px}.register-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:8px;box-shadow:0 4px 12px #0000001a;padding:30px;width:100%;max-width:500px}h2[_ngcontent-%COMP%]{text-align:center;margin-bottom:24px;color:#333}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}label[_ngcontent-%COMP%]{display:block;margin-bottom:6px;font-weight:500;color:#555}input[_ngcontent-%COMP%]{width:100%;padding:10px 12px;border:1px solid #ddd;border-radius:4px;font-size:16px;transition:border-color .3s;box-sizing:border-box}input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff}input.invalid[_ngcontent-%COMP%]{border-color:#dc3545}.error-message[_ngcontent-%COMP%]{color:#dc3545;font-size:14px;margin-top:5px}.alert[_ngcontent-%COMP%]{padding:12px;border-radius:4px;margin-bottom:20px}.alert.alert-danger[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.register-button[_ngcontent-%COMP%]{width:100%;padding:12px;background-color:#007bff;color:#fff;border:none;border-radius:4px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .3s}.register-button[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0069d9}.register-button[_ngcontent-%COMP%]:disabled{background-color:#6c757d;cursor:not-allowed}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:20px}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#007bff;text-decoration:none}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}"]})};var Re=[{path:"login",component:j,canActivate:[z]},{path:"register",component:D,canActivate:[z]},{path:"",redirectTo:"login",pathMatch:"full"}],V=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=E({type:e});static \u0275inj=y({imports:[B.forChild(Re),B]})};var re=class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=E({type:e});static \u0275inj=y({imports:[J,V,te]})};export{re as AuthModule};
