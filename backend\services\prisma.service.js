const { PrismaClient } = require('@prisma/client');

// Create a singleton instance of PrismaClient
class PrismaService {
  constructor() {
    if (!PrismaService.instance) {
      this.prisma = new PrismaClient();
      PrismaService.instance = this;
    }

    return PrismaService.instance;
  }

  // Helper method to get the Prisma client
  getClient() {
    return this.prisma;
  }

  // Disconnect from the database
  async disconnect() {
    await this.prisma.$disconnect();
  }

  // Connect to the database
  async connect() {
    await this.prisma.$connect();
  }
}

// Export a singleton instance
module.exports = new PrismaService();
