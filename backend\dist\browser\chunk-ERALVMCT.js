import{a as N}from"./chunk-OU3SRGRK.js";import{r as R}from"./chunk-K2OZDCQR.js";import{Bb as w,Da as h,Db as E,Eb as D,K as p,Ma as t,Na as n,Nb as k,Oa as M,Ob as H,Qb as P,Rb as I,Sa as O,Ta as b,bb as e,cb as u,db as v,kb as S,lb as y,ma as c,qa as x,va as a,wa as m,za as _}from"./chunk-BKY6BYBU.js";var s=class i{static \u0275fac=function(o){return new(o||i)};static \u0275cmp=a({type:i,selectors:[["app-header"]],standalone:!1,decls:21,vars:0,consts:[[1,"header"],[1,"container"],[1,"logo"],[1,"navigation"],["routerLink","/dashboard"],["href","#"]],template:function(o,d){o&1&&(t(0,"header",0)(1,"div",1)(2,"div",2)(3,"h2"),e(4,"HMS2025"),n(),t(5,"span"),e(6,"Hospital Management System"),n()(),t(7,"nav",3)(8,"ul")(9,"li")(10,"a",4),e(11,"Dashboard"),n()(),t(12,"li")(13,"a",5),e(14,"Patients"),n()(),t(15,"li")(16,"a",5),e(17,"Appointments"),n()(),t(18,"li")(19,"a",5),e(20,"Reports"),n()()()()()())},dependencies:[H],styles:[".header[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;padding:15px 0;box-shadow:0 2px 4px #0000001a}.header[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 20px;display:flex;justify-content:space-between;align-items:center}.header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:24px;font-weight:600}.header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;opacity:.9;display:block;margin-top:2px}.header[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;margin:0;padding:0;display:flex;gap:30px}.header[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;text-decoration:none;font-weight:500;transition:opacity .3s}.header[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{opacity:.8}.header[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]{border-bottom:2px solid white;padding-bottom:2px}@media (max-width: 768px){.header[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{flex-direction:column;gap:15px}.header[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{gap:20px;flex-wrap:wrap;justify-content:center}}"]})};var g=class i{static \u0275fac=function(o){return new(o||i)};static \u0275cmp=a({type:i,selectors:[["app-footer"]],standalone:!1,decls:7,vars:0,consts:[[1,"footer"],[1,"container"],[1,"footer-bottom"],["href","https://sarthaktech.co.in"]],template:function(o,d){o&1&&(t(0,"footer",0)(1,"div",1)(2,"div",2)(3,"p"),e(4,"\xA9 2025 Sarthak HMS. All rights reserved. Designed & Developed by "),t(5,"a",3),e(6,"Sarthak Tech"),n()()()()())},styles:[".footer[_ngcontent-%COMP%]{background-color:#343a40;color:#fff;padding:40px 0 20px;margin-top:auto}.footer[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:0 20px}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:30px;margin-bottom:30px}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#007bff;margin-bottom:15px;font-size:18px}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#adb5bd;line-height:1.6;margin-bottom:10px}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#adb5bd;text-decoration:none;transition:color .3s}.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#007bff}.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]{border-top:1px solid #495057;padding-top:20px;text-align:center}.footer[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#adb5bd;margin:0;font-size:14px}@media (max-width: 768px){.footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:20px}}"]})};function z(i,r){if(i&1&&(t(0,"div",9)(1,"p"),e(2,"Hello, "),t(3,"strong"),e(4),n(),e(5,"!"),n(),t(6,"p"),e(7,"Role: "),t(8,"span",10),e(9),S(10,"titlecase"),n()(),t(11,"p"),e(12),n()()),i&2){let o=b();c(4),u(o.currentUser.name),c(5),u(y(10,3,o.currentUser.role)),c(3),v("Email: ",o.currentUser.email,"")}}var f=class i{constructor(r,o){this.authService=r;this.router=o}currentUser=null;ngOnInit(){this.authService.currentUser$.subscribe(r=>{this.currentUser=r})}logout(){this.authService.logout(),this.router.navigate(["/auth/login"])}static \u0275fac=function(o){return new(o||i)(x(I),x(k))};static \u0275cmp=a({type:i,selectors:[["app-dashboard-home"]],standalone:!1,decls:40,vars:1,consts:[[1,"dashboard-container"],[1,"main-content"],[1,"welcome-section"],["class","user-info",4,"ngIf"],[1,"dashboard-cards"],[1,"card"],[1,"btn","btn-primary"],[1,"actions"],[1,"btn","btn-danger",3,"click"],[1,"user-info"],[1,"role-badge"]],template:function(o,d){o&1&&(t(0,"div",0),M(1,"app-header"),t(2,"main",1)(3,"div",2)(4,"h1"),e(5,"Welcome to HMS2025 Dashboard"),n(),_(6,z,13,5,"div",3),n(),t(7,"div",4)(8,"div",5)(9,"h3"),e(10,"Patients"),n(),t(11,"p"),e(12,"Manage patient records and information"),n(),t(13,"button",6),e(14,"View Patients"),n()(),t(15,"div",5)(16,"h3"),e(17,"Appointments"),n(),t(18,"p"),e(19,"Schedule and manage appointments"),n(),t(20,"button",6),e(21,"View Appointments"),n()(),t(22,"div",5)(23,"h3"),e(24,"Doctors"),n(),t(25,"p"),e(26,"Manage doctor profiles and schedules"),n(),t(27,"button",6),e(28,"View Doctors"),n()(),t(29,"div",5)(30,"h3"),e(31,"Reports"),n(),t(32,"p"),e(33,"Generate and view medical reports"),n(),t(34,"button",6),e(35,"View Reports"),n()()(),t(36,"div",7)(37,"button",8),O("click",function(){return d.logout()}),e(38,"Logout"),n()()(),M(39,"app-footer"),n()),o&2&&(c(6),h("ngIf",d.currentUser))},dependencies:[w,s,g,E],styles:[".dashboard-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column}.main-content[_ngcontent-%COMP%]{flex:1;padding:20px;background-color:#f8f9fa}.welcome-section[_ngcontent-%COMP%]{background-color:#fff;padding:30px;border-radius:8px;box-shadow:0 2px 4px #0000001a;margin-bottom:30px}.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#333;margin-bottom:20px}.welcome-section[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;color:#666}.welcome-section[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;text-transform:uppercase}.dashboard-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px;margin-bottom:30px}.dashboard-cards[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background-color:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 4px #0000001a;transition:transform .2s,box-shadow .2s}.dashboard-cards[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.dashboard-cards[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:10px}.dashboard-cards[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;line-height:1.5}.actions[_ngcontent-%COMP%]{text-align:center;padding:20px}.btn[_ngcontent-%COMP%]{padding:10px 20px;border:none;border-radius:4px;font-size:14px;font-weight:500;cursor:pointer;transition:background-color .3s;text-decoration:none;display:inline-block}.btn.btn-primary[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.btn.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0069d9}.btn.btn-danger[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.btn.btn-danger[_ngcontent-%COMP%]:hover{background-color:#c82333}@media (max-width: 768px){.main-content[_ngcontent-%COMP%]{padding:15px}.dashboard-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}.welcome-section[_ngcontent-%COMP%]{padding:20px}}"]})};var V=[{path:"",component:f}],C=class i{static \u0275fac=function(o){return new(o||i)};static \u0275mod=m({type:i});static \u0275inj=p({imports:[P.forChild(V),P]})};var U=class i{static \u0275fac=function(o){return new(o||i)};static \u0275mod=m({type:i});static \u0275inj=p({imports:[D,C,R,N]})};export{U as DashboardModule};
