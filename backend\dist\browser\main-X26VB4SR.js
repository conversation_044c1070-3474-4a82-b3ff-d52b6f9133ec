import{a as v}from"./chunk-OBWAOF5P.js";import{a as g}from"./chunk-OU3SRGRK.js";import{Gb as u,Ib as s,Jb as f,Mb as l,N as r,Nb as d,Oa as m,Pb as h,Q as n,Rb as C,ob as c,va as a}from"./chunk-BKY6BYBU.js";var A=(t,p)=>{let o=r(C),i=r(d);return o.isLoggedIn()?!0:(i.navigate(["/auth/login"]),!1)};var M=[{path:"auth",loadChildren:()=>import("./chunk-2CESLSNE.js").then(t=>t.AuthModule),canActivate:[v]},{path:"dashboard",loadChildren:()=>import("./chunk-ERALVMCT.js").then(t=>t.DashboardModule),canActivate:[A]},{path:"",redirectTo:"auth",pathMatch:"full"},{path:"**",redirectTo:"auth"}];var b={providers:[c({eventCoalescing:!0}),h(M),s(f()),n(g)]};var e=class t{title="frontend";static \u0275fac=function(o){return new(o||t)};static \u0275cmp=a({type:t,selectors:[["app-root"]],decls:1,vars:0,template:function(o,i){o&1&&m(0,"router-outlet")},dependencies:[l],encapsulation:2})};u(e,b).catch(t=>console.error(t));
