<div class="login-container">
  <div class="login-card">
    <h2>HMS2025 Login</h2>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Enter your email"
          [ngClass]="{'invalid': loginForm.get('email')?.invalid && loginForm.get('email')?.touched}"
        >
        @if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {
          <div class="error-message">
            @if (loginForm.get('email')?.errors?.['required']) {
              <span>Email is required</span>
            } @else if (loginForm.get('email')?.errors?.['email']) {
              <span>Please enter a valid email</span>
            }
          </div>
        }
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [ngClass]="{'invalid': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
        >
        @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
          <div class="error-message">
            @if (loginForm.get('password')?.errors?.['required']) {
              <span>Password is required</span>
            } @else if (loginForm.get('password')?.errors?.['minlength']) {
              <span>Password must be at least 6 characters</span>
            }
          </div>
        }
      </div>

      @if (errorMessage) {
        <div class="alert alert-danger">
          {{ errorMessage }}
        </div>
      }

      <button
        type="submit"
        [disabled]="loginForm.invalid || isLoading"
        class="login-button"
      >
        @if (isLoading) {
          <span>Logging in...</span>
        } @else {
          <span>Login</span>
        }
      </button>

      <div class="register-link">
        <p>Don't have an account? <a routerLink="/auth/register">Register</a></p>
      </div>
    </form>
  </div>
</div>
