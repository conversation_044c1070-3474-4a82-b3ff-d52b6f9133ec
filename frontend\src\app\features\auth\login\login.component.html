<div class="login-container">
  <div class="login-card">
    <h2>HMS2025 Login</h2>
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          placeholder="Enter your email"
          [class.invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
        >
        <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="error-message">
          <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [class.invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
        >
        <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="error-message">
          <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div *ngIf="errorMessage" class="alert alert-danger">
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        [disabled]="loginForm.invalid || isLoading"
        class="login-button"
      >
        <span *ngIf="isLoading">Logging in...</span>
        <span *ngIf="!isLoading">Login</span>
      </button>

      <div class="register-link">
        <p>Don't have an account? <a routerLink="/auth/register">Register</a></p>
      </div>
    </form>
  </div>
</div>
