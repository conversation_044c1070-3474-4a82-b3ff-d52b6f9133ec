<div class="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Login to HMS2025</h2>
      <p class="text-gray-600">Hospital Management System</p>
    </div>

    <!-- Material Card -->
    <mat-card class="p-8">
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">

        <!-- Email Field - Compact Size -->
        <mat-form-field appearance="outline" class="w-full mat-form-field-compact">
          <mat-label>Email Address</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter your email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
            Email is required
          </mat-error>
          <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
            Please enter a valid email
          </mat-error>
        </mat-form-field>

        <!-- Password Field - Compact Size -->
        <mat-form-field appearance="outline" class="w-full mat-form-field-compact">
          <mat-label>Password</mat-label>
          <input matInput type="password" formControlName="password" placeholder="Enter your password">
          <mat-icon matSuffix>lock</mat-icon>
          <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
          <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
            Password must be at least 6 characters
          </mat-error>
        </mat-form-field>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {{ errorMessage }}
        </div>

        <!-- Submit Button -->
        <button
          mat-raised-button
          color="primary"
          type="submit"
          [disabled]="loginForm.invalid || isLoading"
          class="w-full h-12 text-lg font-medium"
        >
          <mat-spinner *ngIf="isLoading" diameter="20" class="mr-2"></mat-spinner>
          <span *ngIf="isLoading">Logging in...</span>
          <span *ngIf="!isLoading">Login</span>
        </button>
      </form>

      <!-- Register Link -->
      <div class="text-center mt-6 pt-6 border-t border-gray-200">
        <p class="text-gray-600">
          Don't have an account?
          <a routerLink="/auth/register" class="text-blue-600 hover:text-blue-800 font-medium ml-1">
            Register here
          </a>
        </p>
      </div>
    </mat-card>

    <!-- Test Credentials -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
      <h4 class="font-medium text-blue-900 mb-2">Test Credentials:</h4>
      <div class="text-blue-700 space-y-1">
        <p><strong>Admin:</strong> admin&#64;hms2025.com / admin123</p>
        <p><strong>Doctor:</strong> doctor&#64;hms2025.com / doctor123</p>
        <p><strong>Patient:</strong> patient&#64;hms2025.com / patient123</p>
      </div>
    </div>
  </div>
</div>
