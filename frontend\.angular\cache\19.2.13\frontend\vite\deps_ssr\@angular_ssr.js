import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-XQ6GDXLT.js";
import "./chunk-AJPPH4OE.js";
import "./chunk-SVCXARWZ.js";
import "./chunk-GF22SLDQ.js";
import "./chunk-EIBM5JGR.js";
import "./chunk-OHC3YNZO.js";
import "./chunk-A7NXGW4Q.js";
import "./chunk-TIXOQUZR.js";
import "./chunk-AQYIT73X.js";
import "./chunk-YHCV7DAQ.js";
export {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrerenderFallback,
  RenderMode,
  createRequestHand<PERSON>,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
