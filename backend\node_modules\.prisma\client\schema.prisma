// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model
model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  role      String // admin, doctor, patient, etc.
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  patients     Patient[]     @relation("DoctorToPatient")
  doctor       Doctor?
  patient      Patient?
  appointments Appointment[] @relation("UserAppointments")

  @@map("users")
}

// Doctor model
model Doctor {
  id             Int      @id @default(autoincrement())
  userId         Int      @unique @map("user_id")
  specialization String
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  user         User          @relation(fields: [userId], references: [id])
  appointments Appointment[] @relation("DoctorAppointments")

  @@map("doctors")
}

// Patient model
model Patient {
  id          Int      @id @default(autoincrement())
  userId      Int      @unique @map("user_id")
  dateOfBirth DateTime @map("date_of_birth")
  bloodGroup  String?  @map("blood_group")
  doctorId    Int?     @map("doctor_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user         User          @relation(fields: [userId], references: [id])
  doctor       User?         @relation("DoctorToPatient", fields: [doctorId], references: [id])
  appointments Appointment[] @relation("PatientAppointments")

  @@map("patients")
}

// Appointment model
model Appointment {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  doctorId  Int      @map("doctor_id")
  patientId Int      @map("patient_id")
  date      DateTime
  status    String // scheduled, completed, cancelled
  notes     String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user    User    @relation("UserAppointments", fields: [userId], references: [id])
  doctor  Doctor  @relation("DoctorAppointments", fields: [doctorId], references: [id])
  patient Patient @relation("PatientAppointments", fields: [patientId], references: [id])

  @@map("appointments")
}
