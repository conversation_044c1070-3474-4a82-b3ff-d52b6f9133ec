{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"@prisma/client": "^6.8.2", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "prisma": "^6.8.2"}, "devDependencies": {"nodemon": "^3.1.10"}}