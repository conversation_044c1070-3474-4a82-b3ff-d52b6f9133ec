const userService = require('../services/user.service');

class UserController {
  // Get all users
  async getAllUsers(req, res) {
    try {
      const users = await userService.getAllUsers();
      return res.status(200).json(users);
    } catch (error) {
      console.error('Error getting all users:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Get user by ID
  async getUserById(req, res) {
    try {
      const { id } = req.params;
      const user = await userService.getUserById(id);
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      return res.status(200).json(user);
    } catch (error) {
      console.error(`Error getting user with ID ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Create a new user
  async createUser(req, res) {
    try {
      const userData = req.body;
      
      // Validate input
      if (!userData.name || !userData.email || !userData.password || !userData.role) {
        return res.status(400).json({ message: 'All fields are required' });
      }
      
      const user = await userService.createUser(userData);
      return res.status(201).json(user);
    } catch (error) {
      console.error('Error creating user:', error);
      
      // Handle duplicate email error
      if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
        return res.status(400).json({ message: 'Email already in use' });
      }
      
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Update a user
  async updateUser(req, res) {
    try {
      const { id } = req.params;
      const userData = req.body;
      
      const user = await userService.updateUser(id, userData);
      
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }
      
      return res.status(200).json(user);
    } catch (error) {
      console.error(`Error updating user with ID ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Delete a user
  async deleteUser(req, res) {
    try {
      const { id } = req.params;
      
      await userService.deleteUser(id);
      return res.status(204).send();
    } catch (error) {
      console.error(`Error deleting user with ID ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }
}

module.exports = new UserController();
