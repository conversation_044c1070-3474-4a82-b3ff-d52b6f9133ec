import{a as v}from"./chunk-U2XJBSKQ.js";import{a as g}from"./chunk-XH52BG3U.js";import{F as m,O as c,V as u,W as s,X as f,Z as l,_ as d,aa as h,ca as C,l as r,m as n,v as a}from"./chunk-3XVLO7MW.js";var A=(t,p)=>{let o=r(C),i=r(d);return o.isLoggedIn()?!0:(i.navigate(["/auth/login"]),!1)};var M=[{path:"auth",loadChildren:()=>import("./chunk-HXJ7DAW5.js").then(t=>t.AuthModule),canActivate:[v]},{path:"dashboard",loadChildren:()=>import("./chunk-ZBAKAFCP.js").then(t=>t.DashboardModule),canActivate:[A]},{path:"",redirectTo:"auth",pathMatch:"full"},{path:"**",redirectTo:"auth"}];var b={providers:[c({eventCoalescing:!0}),h(M),s(f()),n(g)]};var e=class t{title="frontend";static \u0275fac=function(o){return new(o||t)};static \u0275cmp=a({type:t,selectors:[["app-root"]],decls:1,vars:0,template:function(o,i){o&1&&m(0,"router-outlet")},dependencies:[l],encapsulation:2})};u(e,b).catch(t=>console.error(t));
