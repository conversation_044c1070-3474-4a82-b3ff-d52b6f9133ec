.header {
  background-color: #007bff;
  color: white;
  padding: 15px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    span {
      font-size: 12px;
      opacity: 0.9;
      display: block;
      margin-top: 2px;
    }
  }

  .navigation {
    ul {
      list-style: none;
      margin: 0;
      padding: 0;
      display: flex;
      gap: 30px;

      li {
        a {
          color: white;
          text-decoration: none;
          font-weight: 500;
          transition: opacity 0.3s;

          &:hover {
            opacity: 0.8;
          }

          &.active {
            border-bottom: 2px solid white;
            padding-bottom: 2px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .header {
    .container {
      flex-direction: column;
      gap: 15px;
    }

    .navigation {
      ul {
        gap: 20px;
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }
}
