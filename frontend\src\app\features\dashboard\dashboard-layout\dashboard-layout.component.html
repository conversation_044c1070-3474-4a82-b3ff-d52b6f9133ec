<div class="flex h-screen bg-gray-100">
  <!-- Sidebar -->
  <div [class]="'bg-gray-900 text-white transition-all duration-300 ease-in-out ' + (isSidebarOpen ? 'w-64' : 'w-16')">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-700">
      <div [class]="'flex items-center ' + (isSidebarOpen ? '' : 'justify-center')">
        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">H</span>
        </div>
        <span [class]="'ml-3 text-xl font-semibold ' + (isSidebarOpen ? 'block' : 'hidden')">HMS2025</span>
      </div>
      <button 
        (click)="toggleSidebar()"
        class="p-1 rounded-lg hover:bg-gray-700 transition-colors duration-200"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Navigation Menu -->
    <nav class="mt-4 px-2">
      <!-- Dashboard -->
      <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200 mb-1">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
        </svg>
        <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Dashboard</span>
      </a>

      <!-- Patients Menu -->
      <div class="mb-1">
        <button 
          (click)="toggleDropdown('patients')"
          class="w-full flex items-center justify-between px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200"
        >
          <div class="flex items-center">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Patients</span>
          </div>
          <svg [class]="'w-4 h-4 transition-transform duration-200 ' + (isSidebarOpen ? 'block' : 'hidden') + ' ' + (isDropdownOpen('patients') ? 'rotate-180' : '')" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <!-- Patients Submenu -->
        <div [class]="'ml-8 mt-1 space-y-1 ' + (isDropdownOpen('patients') && isSidebarOpen ? 'block' : 'hidden')">
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">All Patients</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Add Patient</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Patient Records</a>
        </div>
      </div>

      <!-- Doctors Menu -->
      <div class="mb-1">
        <button 
          (click)="toggleDropdown('doctors')"
          class="w-full flex items-center justify-between px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200"
        >
          <div class="flex items-center">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Doctors</span>
          </div>
          <svg [class]="'w-4 h-4 transition-transform duration-200 ' + (isSidebarOpen ? 'block' : 'hidden') + ' ' + (isDropdownOpen('doctors') ? 'rotate-180' : '')" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <!-- Doctors Submenu -->
        <div [class]="'ml-8 mt-1 space-y-1 ' + (isDropdownOpen('doctors') && isSidebarOpen ? 'block' : 'hidden')">
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">All Doctors</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Add Doctor</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Doctor Schedule</a>
        </div>
      </div>

      <!-- Appointments Menu -->
      <div class="mb-1">
        <button 
          (click)="toggleDropdown('appointments')"
          class="w-full flex items-center justify-between px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200"
        >
          <div class="flex items-center">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Appointments</span>
          </div>
          <svg [class]="'w-4 h-4 transition-transform duration-200 ' + (isSidebarOpen ? 'block' : 'hidden') + ' ' + (isDropdownOpen('appointments') ? 'rotate-180' : '')" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <!-- Appointments Submenu -->
        <div [class]="'ml-8 mt-1 space-y-1 ' + (isDropdownOpen('appointments') && isSidebarOpen ? 'block' : 'hidden')">
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">All Appointments</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Book Appointment</a>
          <a href="#" class="block px-4 py-2 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors duration-200">Calendar View</a>
        </div>
      </div>

      <!-- Reports -->
      <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200 mb-1">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Reports</span>
      </a>

      <!-- Settings -->
      <a href="#" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200 mb-1">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Settings</span>
      </a>
    </nav>

    <!-- Logout Button -->
    <div class="absolute bottom-4 left-2 right-2">
      <button 
        (click)="logout()"
        class="w-full flex items-center px-4 py-3 text-gray-300 hover:bg-red-600 hover:text-white rounded-lg transition-colors duration-200"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
        </svg>
        <span [class]="'ml-3 ' + (isSidebarOpen ? 'block' : 'hidden')">Logout</span>
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- Top Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="flex items-center justify-between px-6 py-4">
        <div class="flex items-center">
          <h1 class="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- Notifications -->
          <button class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 9 9l-4 4-4-4a6 6 0 0 1-9-9z"></path>
            </svg>
          </button>
          
          <!-- User Profile -->
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span class="text-white text-sm font-medium">A</span>
            </div>
            <div class="hidden md:block">
              <p class="text-sm font-medium text-gray-900">Admin User</p>
              <p class="text-xs text-gray-500"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
