const express = require('express');
const router = express.Router();
const { users } = require('../data/mockData');

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', message: 'API is healthy' });
});

// Users routes
router.get('/users', (req, res) => {
  // Return users without passwords
  const safeUsers = users.map(user => {
    const { password, ...safeUser } = user;
    return safeUser;
  });
  res.json(safeUsers);
});

router.get('/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(u => u.id === id);

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  const { password, ...safeUser } = user;
  res.json(safeUser);
});

// Auth routes
router.post('/auth/login', (req, res) => {
  const { email, password } = req.body;

  const user = users.find(u => u.email === email && u.password === password);

  if (!user) {
    return res.status(401).json({ message: 'Invalid credentials' });
  }

  const { password: userPassword, ...safeUser } = user;
  res.json({
    user: safeUser,
    token: 'mock-jwt-token-' + user.id
  });
});

// Export the router
module.exports = router;
