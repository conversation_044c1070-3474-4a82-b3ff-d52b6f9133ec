const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const userController = require('../controllers/user.controller');
const { authenticate, authorize } = require('../middleware/auth.middleware');

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', message: 'API is healthy' });
});

// Auth routes
router.post('/auth/login', authController.login);
router.post('/auth/register', authController.register);

// User routes
router.get('/users', authenticate, authorize(['admin']), userController.getAllUsers);
router.get('/users/:id', authenticate, userController.getUserById);
router.post('/users', authenticate, authorize(['admin']), userController.createUser);
router.put('/users/:id', authenticate, userController.updateUser);
router.delete('/users/:id', authenticate, authorize(['admin']), userController.deleteUser);

// Protected route example
router.get('/protected', authenticate, (req, res) => {
  res.json({ message: 'This is a protected route', user: req.user });
});

// Export the router
module.exports = router;
